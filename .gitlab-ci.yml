#GIT_STRATEGY: none
#GIT策略，定义拉取代码的方式，有3种：clone/fetch/none，默认为clone，速度最慢，每步job都会重新clone一次代码。我们一般将它设置为none，在具体任务里设置为fetch就可以满足需求，毕竟不是每步都需要新代码，那也不符合我们测试的流程
#在job中可以用${GIT_STRATEGY}来使用这个变量。常用的预定义变量有CI_COMMIT_REF_NAME（项目所在的分支或标签名称），CI_JOB_NAME（任务名称），CI_JOB_STAGE（任务阶段）

before_script:
  - |
    ifconfig | grep "inet "    
    pwd
    export LC_ALL=en_US.UTF-8
    export LANG="en_US.UTF-8" 

stages:
  - 协议路径检查
  - 协议依赖更新
  - 协议规范检查
  - 协议兼容检查

tt_root_keeper:
  stage: 协议路径检查
  script:
    - echo ${CI_REPOSITORY_URL}
    - cd ${CI_PROJECT_DIR}
    - echo $(pwd)
    - echo ${CI_COMMIT_SHORT_SHA}
    - chmod +x ./ci/rootkeeper.sh
    - ./ci/rootkeeper.sh $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
    - echo "var $?"
  tags:
    - tt-buf
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'

tt_buf_dep_update:
  stage: 协议依赖更新
  script:
    - echo ${CI_REPOSITORY_URL}
    - cd ${CI_PROJECT_DIR}
    - echo $(pwd)
    - echo ${CI_COMMIT_SHORT_SHA}
    - /usr/local/bin/buf dep update
    - echo "var $?"
  tags:
    - tt-buf
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
#  artifacts:
#    expire_in: 1 day
#    when: always
#    paths:
#      - .ci_result_file

tt_buf_lint_check:
  stage: 协议规范检查
  script:
    - echo ${CI_REPOSITORY_URL}
    - cd ${CI_PROJECT_DIR}
    - echo $(pwd)
    - echo ${CI_COMMIT_SHORT_SHA}
    - chmod +x ./ci/tt_buf_lint_check.sh
    - ./ci/tt_buf_lint_check.sh $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
    - echo "var $?"
  tags:
    - tt-buf
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
#  artifacts:
#    expire_in: 1 day
#    when: always
#    paths:
#      - .ci_result_file

tt_buf_breaking_check:
  stage: 协议兼容检查
  script:
    - cd ${CI_PROJECT_DIR}
    - echo $(pwd)
    - echo ${CI_COMMIT_SHORT_SHA}
    - chmod +x ./ci/tt_buf_breaking_check.sh
    - ./ci/tt_buf_breaking_check.sh $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
    - echo "var $?"
  tags:
    - tt-buf
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
#  artifacts:
#    expire_in: 1 day
#    when: always
#    paths:
#      - .ci_result_file