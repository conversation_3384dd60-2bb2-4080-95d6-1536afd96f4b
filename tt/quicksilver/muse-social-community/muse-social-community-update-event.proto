syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package muse_social_community_update_event;
option go_package = "golang.52tt.com/protocol/services/social-community-update-event";

import "tt/quicksilver/muse-social-community/muse-social-community.proto";

message CreateEvent {
  string id = 1;
  string name = 2;
  string category_type_simple_desc = 3;
  int64 create_ts = 4;
  muse_social_community.BrandStatus status = 5;
}

message StatusChangeEvent {
  string id = 1;
  muse_social_community.BrandStatus origin_status = 2;
  muse_social_community.BrandStatus current_status = 3;
  string name = 4;
}

message NameChangeEvent {
  string id = 1;
  string origin_name = 2;
  string current_name = 3;
  muse_social_community.BrandStatus status = 4;
}

message SocialCommunityProfessionEvent {
  string id = 1;
  uint32 caption = 2;
  uint32 origin_brand_professionalism=3;
  uint32 current_brand_professionalism=4;
}

message CaptainChannelExtendMicChangeEvent {
  uint32 caption = 1;
}


//主理人变更事件
message CaptainChangeEvent {
  string social_community_id = 1;
  uint32 uid=2;
  // buf:lint:ignore ONEOF_LOWER_SNAKE_CASE
  oneof subEvent {
    CaptainRoleChangeEvent role_change = 3;
    CaptainStatusChangeEvent status_change = 4;
  }
}
message CaptainRoleChangeEvent {
  uint32 origin_role = 1;
  uint32 current_role = 2;
}
message CaptainStatusChangeEvent {
  uint32 origin_status = 1;
  uint32 current_status = 2;
}
/**/

message UpdateEvent {
  oneof event {
    CreateEvent create_event = 1;
    StatusChangeEvent status_change_event = 2;
    NameChangeEvent name_change_event = 3;
    CaptainChangeEvent captain_change_event=4;
    SocialCommunityProfessionEvent profession_event=5;
    CaptainChannelExtendMicChangeEvent channel_extend_mic_change_event=6;
  }
}