syntax = "proto3";

package muse_integration_middlelayer;

option go_package = "golang.52tt.com/protocol/services/muse-integration-middlelayer";

import "tt/quicksilver/extension/options/options.proto";
import "muse_biz_integration_middlelayer_logic/muse_biz_integration_middlelayer_logic.proto";
service MuseIntegrationMiddlelayer {
  option (service.options.service_ext) = {
    service_name: "muse-integration-middlelayer"
  };



  rpc GetMtEnterChannelPublicScreenExtendInfo(GetMtEnterChannelPublicScreenExtendInfoRequest)
      returns(GetMtEnterChannelPublicScreenExtendInfoResponse){}

  rpc GetHomePageHeadMuseConfig(GetHomePageHeadMuseConfigRequest)returns(GetHomePageHeadMuseConfigResponse){}
}

message GetMtEnterChannelPublicScreenExtendInfoRequest{
  uint32 uid=1;
}

message GetMtEnterChannelPublicScreenExtendInfoResponse{
  ga.muse_biz_integration_middlelayer_logic.MtEnterChannelPublicScreenExtend  channel_info=1;
}


message GetHomePageHeadMuseConfigRequest{
  repeated uint32 config_types =1;  //HomePageHeadConfigEnum
}

message GetHomePageHeadMuseConfigResponse{
  map<uint32,HomePageHeadMuseConfig> config_data_map=1;
  map<uint32, MuseConfigList> config_data_map_list = 2;
}

message MuseConfigList {
  repeated HomePageHeadMuseConfig config_list = 1; // HomePageHeadMuseConfig
}

//muse-integration-middlelayer.proto
message HomePageHeadMuseConfig{
  // 专区类型
  uint32 config_type = 1;
  // 主标题
  string title = 2;
  // 副标题
  string sub_title = 3;
  // 底图， 旧版代表展示两个一行的大图
  string background = 4;
  string jump_link = 5;
  string small_background = 6;
  bytes extra_data=7; //muse_integration_middlelayer_logic  HomePageHeadMuseExtraInfo
}
