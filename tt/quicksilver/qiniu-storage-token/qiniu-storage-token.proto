syntax="proto3";
package qiniu_storage_token;
option go_package = "golang.52tt.com/protocol/services/qiniu-storage-token;qiniu_storage_token";


// buf:lint:ignore SERVICE_PASCAL_CASE
service qiniuTokenServer{
    rpc GetStorageToken(tokenReq )returns (tokenResp){};
}

//
// buf:lint:ignore MESSAGE_PASCAL_CASE
message tokenReq{
    string bucket = 1;
    string prefix = 2;
    uint32 expired = 3;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message tokenResp{
    string token = 1;
}