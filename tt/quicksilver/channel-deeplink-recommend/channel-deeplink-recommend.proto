syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-deeplink-recommend";
package channel_deeplink_recommend;


service ChannelDeeplinkRecommend {
  // 根据规则获取推荐的直播房
  rpc GetRecommendChannelByRules (GetRecommendChannelByRulesReq) returns (GetRecommendChannelByRulesResp) {
  }

  // 新增推荐房分流配置列表
  rpc AddRecommendDiversionConf (AddRecommendDiversionConfReq) returns (AddRecommendDiversionConfResp) {
  }
}

// bitmap 二进制移位操作求和
// 例如 1 << 1 代表 情感
enum SubTagIdBitMap {
  MusicSubTagId = 0;            // 音乐
  EmotionSubTagId = 1;          // 情感
  QuadraticElementSubTagId = 2; // 二次元
  StorySubTagId = 3;            // 故事
  TalkShowSubTagId = 4;         // 脱口秀
}

message GetRecommendChannelByRulesReq{
  enum TagId {
    UnknownTagId = 0;
    YuYinTagId = 1;
  }

  enum SexMode {
    NotLimit = 0;     // 不限制
    OppositeSex = 1;  // 异性
    SameSex = 2;      // 同性
    Female = 3;       // 女性
    Male = 4;         // 男性
  }

  enum DiversionMode {
    UnknownDiversionMode = 0;
    LivingChannelMemCnt = 1;  // 将下发用户随机平均分流到正在开播中的房间
    DiversionConf = 2;        // 将下发用户随机平均分流至房间cid列表
    DiversionConfWithRate = 3;// 支持在房间cid列表中分配不同的流量占比
  }

  uint32 uid = 1;
  uint32 tag_id = 2;                    // 1-语音直播 暂时只支持语音直播
  repeated uint32 sub_tag_id_list = 3;  // 不填=不限语音直播房间类型。0=音乐，1=情感，2=二次元，3=故事，4=脱口秀
  uint32 sex_mode = 4;                  // 不填=不做要求。1=为用户下发异性主播，2=为用户下发同性主播，3=女性主播，4=男性主播
  uint32 diversion_mode = 5;            // 1=将下发用户随机平均分流至在线人数[x,y]的房间（正在开播中的房间） 2=将下发用户随机平均分流至房间cid列表 3. 3=支持在房间cid列表中分配不同的流量占比
  uint32 begin_rank = 6;
  uint32 end_rank = 7;
  uint32 division_conf_idx = 8;
}

message GetRecommendChannelByRulesResp{
  uint32 channel_id = 1;
}

message Diversion {
  uint32 channel_id = 1;
  uint32 rate = 2;       // 流量占比值， =比率*1000
}

// 新增分流配置列表
message AddRecommendDiversionConfReq{
  repeated Diversion diversion_conf = 1;
}

message AddRecommendDiversionConfResp{
  uint32 conf_idx = 1;
}