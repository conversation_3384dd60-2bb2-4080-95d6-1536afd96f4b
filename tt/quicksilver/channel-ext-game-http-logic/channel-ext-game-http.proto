syntax = "proto3";

option go_package = "channel-ext-game-http";

package channel_ext_game_http;

// deprecated
message SessionCheckReq {
    string session = 1;
    string app_id = 2;
    string open_id = 3;
    string room_id = 4;
}

message SessionCheckResp {
    bool check_result = 1;
}

// deprecated
message GetAccessTokenReq{
    string app_id = 1;
    string secret = 2[deprecated=true];
    string open_id = 3;
    string js_code = 4;
    string room_id = 5;
    string app_secret = 6;
}

message GetAccessTokenResp{
    string session = 1;
    uint32 session_expired = 2;
}


message SessionRenewReq {
    string session = 1;
    string appid = 2;
    string open_id = 3;
    string room_id = 4;
    string app_secret = 5;
}

message SessionRenewResp {
    string session = 1;
    uint32 session_expired = 2;
}

message BatchGetUserInfoReq {
    string session = 1;
    string appid = 2;
    string open_id = 3; // 本人的open_id
    string room_id = 4;
    repeated string open_id_list = 5; // 查询的用户open_id列表
}

message UserInfo {
    string nickname = 1;
    uint32 gender = 2; // 1为女性，2为男性
    string avatar = 3;
    repeated string possess_room_list = 4;
}

message BatchGetUserInfoResp {
    map<string, UserInfo> user_infos = 1;
}


message BatchGetRoomInfoReq {
    string session = 1;
    string appid = 2;
    string open_id = 3;
    string room_id = 4; // 游戏中的房间id
    repeated string room_id_list = 5;
}

message RoomInfo {
    string room_name = 1;
    string avatar = 2;
}

message BatchGetRoomInfoResp {
    map<string, RoomInfo> room_infos = 1;
}