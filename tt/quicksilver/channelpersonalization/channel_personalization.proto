syntax="proto3";

package channel.personalization;

import "google/protobuf/empty.proto";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service ChannelPersonalization {
    // Common API for all decoration types
    rpc GetUserDecorations( GetUserDecorationsReq ) returns ( GetUserDecorationsResp );
    rpc GrantDecorationToUser( GrantDecorationToUserReq ) returns ( GrantDecorationToUserResp );
    rpc ActivateUserDecoration( ActivateUserDecorationReq ) returns ( ActivateUserDecorationResp );

    // 运营后台用

    // 添加座骑配置
    rpc AddUserDecorationConfig( AddUserDecorationConfigReq ) returns ( AddUserDecorationConfigResp );
    // 删除座骑配置
    rpc DelUserDecorationConfig( DelUserDecorationConfigReq ) returns ( DelUserDecorationConfigResp );
    // 更新座骑配置
    rpc UpdateUserDecorationConfig( UpdateUserDecorationConfigReq ) returns ( UpdateUserDecorationConfigResp );
    // 获取所有座骑配置
    rpc GetChannelEnterSpecialEffectConfig( google.protobuf.Empty ) returns ( ChannelEnterSpecialEffectConfig );
    // 配置财富/贵族等级特定座骑
    rpc SetUserDecorationSpecialLevel( SetUserDecorationSpecialLevelReq ) returns ( SetUserDecorationSpecialLevelResp );
    // 发放座骑
    rpc GrantDecorationToUserV2( GrantDecorationToUserReq ) returns ( GrantDecorationToUserResp );
    // 批量发放座骑
    rpc BatchGrantDecorationToUser(BatchGrantDecorationToUserReq) returns (BatchGrantDecorationToUserResp);
    // 获取单个坐骑配置
    rpc GetChannelEnterSpecialEffectConfigById( GetChannelEnterSpecialEffectConfigByIdReq ) returns ( GetChannelEnterSpecialEffectConfigByIdResp );

    rpc ChangeDecorationCustomText(ChangeDecorationCustomTextReq) returns (ChangeDecorationCustomTextResp);

    //获取时间范围内的背包订单数量和物品数量
    rpc GetBackpackItemUseCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

    //获取时间范围内的背包订单列表
    rpc GetBackpackItemUseOrder(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
};

message ChannelEnterSpecialEffect {
    string name = 1;        // 特效名称
    string desc = 2;        // 特效描述，历史遗留，与名称一致即可
    string preview_url = 3; // 特效预览图地址，可以不填
    uint32 min_level = 4;	// 0表示非等级坐骑
    EffectType effect_type = 5;   // 等级特效类型
}

message ChannelEnterSpecialEffectList {
    repeated ChannelEnterSpecialEffect special_effects = 1;
}

message ChannelEnterFunMessage {
    bool    random = 1;
    string  fixed_text = 2;
}

enum DecorationType {
    INVALID = 0;
    CHANNEL_ENTER_SPECIAL_EFFECT = 1;
    CHANNEL_ENTER_FUN_MESSAGE = 2;
}


//特效类型，0普通，1财富值特效，2贵族特效
enum EffectType {
    NORMAL = 0;
    RICH = 1;
    NOBILITY = 2;
}

// 个性化装饰
// NOTE: 一个用户拥有多个分类的个性化装饰，每个类别下都按照<uid, decoration_id>作为唯一索引
message Decoration {
    string id = 1;
    DecorationType type = 2;
    string ver = 3;

    // 以下字段发放的时候不用填写
    DecorationDetail detail = 4;
    string operator_info = 5; // 更新时间/最后操作人
    string notice = 6; // 备注
    uint32 award_time = 7; // 默认的奖励时间
    EnterEffectType enter_effect_type = 8; // 进房特效分类 ，EnterEffectType
    string source_url = 9;  // 资源包地址
    string preview_url = 10; // 预览图地址
    uint32 create_time = 11; // 创建时间,是个时间戳
    uint32 update_time = 12; // 更新时间

    EffectCustomType custom_type = 13; // 特效的自定义类型
    string custom_color = 14; // 如果有自定义文案/融合礼物 文案的颜色

    EffectSourceType source_type = 15; // 特效资源的类型
    EffectSpecialType special_type = 16; // 特效特殊展示类型
}

//特效类型，0普通进房特效，1座骑进房特效
enum EnterEffectType {
    ENTER_NORMAL = 0; // 普通进房特效
    ENTER_PET = 1;  // 座骑进房特效
}

enum EffectCustomType {
    EffectNone = 0;
    EffectCustomText = 1; // 自定义文案
    EffectFusion = 2; // 融合特效
}

// 资源类型
enum EffectSourceType {
    EFFECT_SOURCE_NONE = 0; // 未知类型
    EFFECT_SOURCE_NORMAL_MP4 = 1;  // 普通 - VAP
    EFFECT_SOURCE_NORMAL_STATIC = 2;  // 普通 - 静态
    EFFECT_SOURCE_NORMAL_LOTTIE = 3;  // 普通 - LOTTIE
    EFFECT_SOURCE_PET_MP4 = 4;  // 坐骑 - VAP
    EFFECT_SOURCE_PET_FULLSCREEN = 5;  // 坐骑 - 全屏
    EFFECT_SOURCE_PET_LOTTIE = 6;  // 坐骑 - LOTTIE
}

// 资源类型
enum EffectSpecialType {
    EFFECT_SPECIAL_NONE = 0; // 未知类型
    EFFECT_SPECIAL_PLATE_TOP = 1; // 牌匾显示在上方
}


message DecorationDetail {
    // 用oneof更合适，但是想直接把pb的结构存进mongo，序列化成问题
    // 根据DecorationType决定哪个字段填值
    ChannelEnterSpecialEffect   channel_enter_special_effect = 1;
    ChannelEnterFunMessage      channel_enter_fun_message = 2;
}

message UserDecoration {
    uint32 uid = 1;
    Decoration decoration = 2;

    uint64 effect_begin = 3;
    uint64 effect_end = 4;
    uint64 grant_at = 5;
    string grant_reason = 6;
    string grant_operator = 7;

    bool actived = 8;
    string order_id = 9;

    string custom_text = 10;
    string fusion_ttid = 11; // 融合特效对方的ttid
    string extend_json = 12;

  enum TextStatus{
    Default = 0;
    UnderReview = 1;  // 审核中
  }
  TextStatus text_status = 13;       //自定义文案状态
  string show_custom_text = 14;  // 用来展示的自定义文案
  uint32 remain_change_count = 15; // 剩余的修改次数
  string fusion_nickname = 16; // 融合特效对方的昵称
}

message UserInfo {
    uint32 uid = 1;        // 用户id
    uint32 rich_level = 2; // 用户的财富等级
    uint32 nobility_level  = 3; // 用户的贵族等级
}

message GetUserDecorationsReq {
    UserInfo user_info = 1;             // 用户id
    bool only_effective = 2;            // 是否只查询有效的
    DecorationType decoration_type = 3; // 查询类型
    bool need_under_view_info = 4;       // 是否需要仍在审核的custom_text
}

message GetUserDecorationsResp {
    repeated UserDecoration user_decorations = 1;
}

message GrantDecorationToUserReq {
    UserDecoration user_decoration = 1;
    bool add_ttl_for_existing = 2;
    int64 outside_time = 3;
    uint32 source_type = 4;
}

message GrantDecorationToUserResp {

}

message ActivateUserDecorationReq {
    uint32 uid = 1;
    DecorationType decoration_type = 2;
    string decoration_id = 3;
    string custom_text = 4;
    string fusion_ttid = 5; // 融合特效对方的ttid
}

message ActivateUserDecorationResp {

}

message AddUserDecorationConfigReq {
    Decoration decoration = 1;
}

message AddUserDecorationConfigResp {
}

message DelUserDecorationConfigReq {
    string id = 1;
    DecorationType type = 2;
    string custom_text = 3;
    string fusion_ttid = 4;
}

message DelUserDecorationConfigResp {
}

message UpdateUserDecorationConfigReq {
    Decoration decoration = 1;
}

message UpdateUserDecorationConfigResp {
}

// 财富等级特效
message RichLevelSpecialEffect {
    uint32 min_level = 1;
    Decoration effect_info = 2;
}

// 贵族等级特效
message NobilityLevelSpecialEffect {
    uint32 min_level = 1;
    Decoration effect_info = 2;
}

message ChannelEnterSpecialEffectConfig {
    repeated RichLevelSpecialEffect rich_level_config = 1;  // 财富等级特效列表
    map<string, Decoration> non_rich_level_decorations = 2; // 非财富等级特效, id->Decoration
    repeated NobilityLevelSpecialEffect nobility_level_config = 3;  // 贵族等级特效列表
}

message SetUserDecorationSpecialLevelReq{
    string id = 1;
    DecorationType type = 2;
    string ver = 3;
    EffectType effect_type = 4;   // 特殊特效类型
    uint32 min_level = 5;	// 财富值/贵族等级限制
}

message SetUserDecorationSpecialLevelResp{

}


message FailedUserInfo{
    uint32 uid = 1;
    string reason = 2;
}

message BatchGrantDecorationToUserReq {
    // 批量发放不用填uid和ttid 填在后面的list里
    repeated UserDecoration user_decoration = 1;
    bool add_ttl_for_existing = 2;
}

message BatchGrantDecorationToUserResp {
    repeated FailedUserInfo fail_list = 1;
}

message ChangeDecorationCustomTextReq {
    enum DecorationType {
        Unknown = 0;
        HeadWear = 1;   // 麦位框
        ChannelPersonalization = 2;  // 坐骑
    }
    uint32 uid = 1; // uid
    DecorationType decoration_type = 2;  // 装扮类型
    string decoration_id = 3;
    string version = 4; // 麦位框可以不填
    string custom_text = 5; // 自定义文案（发放时的）
    string new_custom_text = 6; // 新的自定义文案
    uint32 cp_uid = 7; // cp麦位框的对方uid
}

message ChangeDecorationCustomTextResp{
    string extend_json = 1; // 额外的json
}

// 自动取对应id最新的版本
message GetChannelEnterSpecialEffectConfigByIdReq {
    string id = 1; // 坐骑id
}

message GetChannelEnterSpecialEffectConfigByIdResp {
    Decoration decoration = 1;  // 坐骑
}