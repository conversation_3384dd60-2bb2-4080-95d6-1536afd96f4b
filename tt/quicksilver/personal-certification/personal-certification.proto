syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/personalcertification";
package personalcertification;


service PersonalCertification {
    rpc SetPersonalCertification (SetPersonalCertificationReq) returns (SetPersonalCertificationResp) {}
    rpc GetPersonalCertification (GetPersonalCertificationReq) returns (GetPersonalCertificationResp) {}
    rpc BatGetPersonalCertification (BatGetPersonalCertificationReq) returns (BatGetPersonalCertificationResp) {}
    rpc StopPersonalCertification (StopPersonalCertificationReq) returns (StopPersonalCertificationResp) {}

    rpc SetCertType (SetCertTypeReq) returns (SetCertTypeResp) {}
    rpc DelCertType (DelCertTypeReq) returns (DelCertTypeResp) {}
    rpc GetCertType (GetCertTypeReq) returns (GetCertTypeResp) {}
    rpc GetCertTypeById(GetCertTypeByIdReq)returns(GetCertTypeByIdResp){}
    /* 模糊搜索 */
    rpc GetCertTypeFuzzySearch (GetCertTypeFuzzySearchReq) returns (GetCertTypeFuzzySearchResp) {}
    rpc SetUserCert (SetUserCertReq) returns (SetUserCertResp) {}
    rpc GetUserCert (GetUserCertReq) returns (GetUserCertResp) {}
    rpc DelUserCertByID (DelUserCertByIDReq) returns (DelUserCertByIDResp) {}  /* 批量删除用户发放认证表 */
    rpc BatDelUserCert (BatDelUserCertReq) returns (BatDelUserCertResp) {}  /* 批量删除用户发放认证表 */

    /* 提供客户端的接口 生效中 */
    rpc GetUserCertByUid (GetUserCertByUidReq) returns (GetUserCertByUidResp) {}

    /* 设置优先展示的认证标 */
    rpc SetPriorityDisplayCert (SetPriorityDisplayCertReq) returns (SetPriorityDisplayCertResp) {}

    /*检查用户重复的认证标*/
    rpc CheckRepeatedUserCert(CheckRepeatedUserCertRequest) returns (CheckRepeatedUserCertResponse) {}

}

/* 运营后台配置 */
enum CertType{
    UNKNOWN = 0;
    PERSONAL_CERT = 1;
}
enum PresentPosition{
    VALID_POSITION = 0;
    PERSONAL_HOME_PAGE = 1; /* 个人主页 */
    INFORMATION_CARD = 2; /* 资料卡片 */
    RCMD_CARD = 3; /* 推荐卡片 */
    UGC_POST = 4; /* 动态广场 */
    IM_FOLLOW = 5; /* IM跟随区域 */
    CHANNEL_NICKNAME = 6; /* 房内昵称 */
}
message GetCertTypeByIdReq{
    string id=1;
}
message GetCertTypeByIdResp{
    CertTypeInfo cert = 1;
}
message PersonalCertificationCommon{
    uint32 uid = 1;
    uint32 cert_type = 2; /* CertType */
    string icon = 3;
    string text = 4;
    uint32 begin_time = 5;
    uint32 end_time = 6;
    uint32 update_time = 7;
    string operator = 8;
    repeated uint32 present_position_list = 9; /* PresentPosition */
    repeated string color = 10;
    string text_shadow_color = 11; /* 文字阴影颜色 */
    string short_text = 12; /* 备注-短文案 */
    string introduce = 13; /* 介绍 */
    string jump_url = 14; /* 跳转链接 */
    string cert_type_name = 15; /* 类型名称 */
    int32 priority_display_bit = 16;
    repeated  ExtraContent  extra_content=17;   /*认证标额外信息*/
    repeated uint32 not_display_tab_ids=18;   /*新增 不需要展示tabids，当且仅当存在present_position_list 中存在CHANNEL_NICKNAME = 6;*/

}
message PersonalCertificationInfo{
    string id = 1;
    PersonalCertificationCommon common = 2;
}

message SetPersonalCertificationReq{
    repeated PersonalCertificationInfo info_list = 1;
}
message SetPersonalCertificationResp{
    repeated uint32 failed_uid_list = 1;
}

message GetPersonalCertificationReq{
    string id = 1;
    uint32 uid = 2;
    string operator = 3;
    uint32 cert_type = 4; /* CertType */
    uint32 present_position = 5; /* PresentPosition */
    uint32 offset = 6;
    uint32 limit = 7;
    bool valid = 8;
    bool not_use_cache = 9;
    uint32 channel_id=10;
}
message GetPersonalCertificationResp{
    repeated PersonalCertificationInfo info_list = 1;
    uint32 total = 2;
}

message StopPersonalCertificationReq{
    string id = 1;
}
message StopPersonalCertificationResp{
}

message BatGetPersonalCertificationReq{
    repeated uint32 uid = 1;
    uint32 present_position = 2; /* PresentPosition */
    uint32 channel_id=3;
}
message PersonalCertificationInfoList{
    repeated PersonalCertificationInfo info_list = 1;
}
message BatGetPersonalCertificationResp{
    map<uint32,PersonalCertificationInfoList> user_cert_map = 1;
}

/*--------------------- 新版本 类型+给用户发标 ---------------------*/
message CertTypeInfo{
    string id = 1;
    string name = 2;     /* 认证类型名称*/
    string icon = 3;  /* Logo图片 */
    repeated string color = 4; /* 背景色块颜色 */
    string text_shadow_color = 5; /* 文字阴影颜色 */
    repeated uint32 present_position_list = 6; /* PresentPosition */
    string operator = 7;
    uint32 update_time=8;           //更新时间
    string introduce = 9; /* 介绍 */
    string jump_url = 10; /* 跳转链接 */
    repeated uint32 not_display_tab_ids=11;   /*新增 不需要展示tabids，当且仅当存在present_position_list 中存在CHANNEL_NICKNAME = 6;*/
    repeated uint32  channel_classification_id=12;   /*房间分类id*/
}
message SetCertTypeReq{
    CertTypeInfo info = 1;
}
message SetCertTypeResp{
}

message DelCertTypeReq{
    string id = 1;
}
message DelCertTypeResp{
}

message GetCertTypeReq{
    string id = 1;
    uint32 offset = 2;
    uint32 limit = 3;
}


message GetCertTypeResp{
    repeated CertTypeInfo cert_list = 1;
    uint32 total = 2;
}

/* 模糊搜索 */
message GetCertTypeFuzzySearchReq{
    string str = 1;
}
message CertTypeIdInfo {
    string id = 1;
    string name = 2; /* 认证类型名称*/
}
message GetCertTypeFuzzySearchResp{
    repeated CertTypeIdInfo cert_id_name_list = 1;
}

message UserCertInfo{
    string id = 1;
    uint32 uid = 2;
    string cert_type_id = 3; /* CertTypeID */
    string cert_type_name = 4;
    string text = 5; /* 认证文案 */
    string short_text = 6; /* 短文案 */
    uint32 begin_time = 7;
    uint32 end_time = 8;
    uint32 status = 9; /* CertStatusType */
    uint32 update_time = 10;
    string operator = 11;
    uint32 is_del=12;   /*IsDelType*/
    repeated  ExtraContent  extra_content=13;   /*认证标额外信息*/
  uint32  total_distribution_days=14; //累计发放天数
}

    message ExtraContent{
        uint32 type=1;    /*ExtraContentType*/
        oneof extra_content {
            SocialCommunityInfo social_community = 2;
        }
    }
    enum ExtraContentType{
        EXTRA_CONTENT_TYPE_NONE=0;
        EXTRA_CONTENT_TYPE_SOCIAL_COMMUNITY=1;    /*社团信息*/
    }

    message SocialCommunityInfo{
        string social_community_id=1;
    }


message SetUserCertReq{
    repeated UserCertInfo cert_list = 1;
}
message SetUserCertResp{
    repeated string invalid_cert_type_list = 1;
}

enum CertStatusType{
    Unknown = 0;
    Valid = 1; /* 生效中 */
    Expired = 2; /*   - 已过期 */
    NotYet = 3; /*   - 未生效 */
    Replaced = 4; /* 当前时间处于生效时间内，但被新的处于生效时间内的配置取代了 */
}

enum IsDelType{
    IS_DEL_TYPE_NONE = 0;
    IS_DEL_TYPE_NORMAL=1;
    IS_DEL_TYPE_DELETE=2;
}




/* 运营后台 无缓存 */
message GetUserCertReq{
    uint32 uid = 1;
    string cert_type_id = 2; /* CertTypeID */
    string text = 3; /* 认证文案 */
    string operator = 4;
    uint32 status = 5; /* CertStatusType */
    uint32 offset = 6;
    uint32 limit = 7;
    uint32 is_del=8;  /*IsDelType*/
}
message GetUserCertResp{
    repeated UserCertInfo cert_list = 1;
    uint32 total = 2;
}


message DelUserCertByIDReq{
   string id = 1;
   uint32 uid = 2;
   string cert_type_id = 3;
   string desc= 4;
}
message DelUserCertByIDResp{
}

message DelUserCertInfo{
    uint32 uid = 1;
    string cert_type_id = 2;
}
message BatDelUserCertReq{
    repeated DelUserCertInfo del_info = 1;
}
message BatDelUserCertResp{
}

/* 提供客户端的接口 生效中 */
message ValidUserCertTypeInfo{
    string id = 1;
    uint32 uid = 2;
    string icon = 3;
    string text = 4;
    string short_text = 5; /* 备注-短文案 */
    repeated string color = 6; /* 背景色块颜色 */
    string text_shadow_color = 7; /* 文字阴影颜色 */
    repeated uint32 present_position_list = 8; /* PresentPosition */
    uint32 begin_time = 9;
    uint32 end_time = 10;
    string introduce = 11; /* 介绍 */
    string jump_url = 12; /* 跳转链接 */
    string cert_type_name = 13; /* 类型名称 */
    int32 priority_display_bit = 14;
    repeated  ExtraContent  extra_content=15;   /*认证标额外信息*/
    repeated uint32 not_display_tab_ids=16;   /*新增 不需要展示tabids，当且仅当存在present_position_list 中存在CHANNEL_NICKNAME = 6;*/

}
message GetUserCertByUidReq{
    uint32 uid = 1;
    uint32 present_position = 5; /* PresentPosition */
}
message GetUserCertByUidResp{
    repeated ValidUserCertTypeInfo cert = 1;
}

/* 设置优先展示的认证标 */
message SetPriorityDisplayCertReq{
    uint32 uid = 1;
    string cert_id = 2;
    uint32 present_position = 3; /* 优先的位置 PresentPosition */
    uint32 channel_id = 4; /* 用于房间内推送 */
}
message SetPriorityDisplayCertResp{
}

message CheckRepeatedUserCertRequest{
    repeated UserCertInfo cert_list = 1;
}

message CheckRepeatedUserCertResponse{
        repeated uint32  uids = 1;
}