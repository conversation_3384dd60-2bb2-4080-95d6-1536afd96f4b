syntax = "proto3";

option go_package = "richer-birthday-http";

package richer_birthday_http;

// 获取生日信息
message GetRicherBirthdayRegisterStatusReq {
}

// 查看生日登记状态
message GetRicherBirthdayRegisterStatusResp {
  int64 birthday = 1; // 生日时间戳， 秒， 返回0则说明还没登记
  bool is_richer = 2; // 是否是大R
  string account = 3; // ttid
  string nick_name = 4; // 昵称
  bool could_modify = 5; // 是否可以修改，true:可以修改
  bool could_receive_gift = 6; // 是否可以领取礼包 true:可领取 false:按钮置灰
}

// 生日登记（设置或者修改）
message RegisterRicherBirthdayReq {
  uint32 birthday_month = 1; // 生日月份
  uint32 birthday_day = 2; // 几号
  bool is_update = 3; // 是否更新生日
}

message RegisterRicherBirthdayResp {
}

// 获取生日奖励列表
message GetRicherBirthdayRewardListReq {
}

message RicherBirthdayReward {
  string reward_id = 1; // 奖励ID
  string reward_name = 2; // 奖励名称
  uint32 reward_type = 3; // 奖励类型
  uint32 reward_num = 4; // 奖励数量
  string reward_icon = 5; // 奖励图标
}

message GetRicherBirthdayRewardListResp {
  repeated RicherBirthdayReward richer_birthday_reward_list = 1;
}

// 领取生日礼包
message ReceiveRicherBirthdayGiftReq {
  uint32 uid = 1;
}

message ReceiveRicherBirthdayGiftResp {
}

message UserInfo {
  uint32 uid = 1;
  string nickname = 2;
  string account = 3;
}

message RankInfo {
  UserInfo user = 1;  // 用户信息
  uint32 rank = 2; // 排名
  uint32 val = 3;  // 榜单值
}

// 获取生日排行榜
message GetRicherBirthdayRankListReq {
  uint32 uid = 1; // 用户ID
}

message GetRicherBirthdayRankListResp {
  repeated RankInfo richer_birthday_rank_list = 1;
  RankInfo my_rank = 2;
  bool rank_switch = 3; // 是否关闭榜单展示
}

// 根据TT ID获取生日排行榜信息
message GetRicherBirthdayRankInfoReq {
  string account = 1; // 用户TT ID
}

message GetRicherBirthdayRankInfoResp {
  RankInfo richer_birthday_rank_info = 1;
}

message GiftInfo {
  uint32 id = 1; // 礼物ID
  string name = 2; // 礼物名称
  string icon = 3; // 礼物图标
  uint32 price = 4; // 价值
}

// 祝福信息
message BlessInfo {
  UserInfo from_user = 1; // 用户信息
  string order_id = 2; // 订单号
  GiftInfo gift = 3; // 礼物信息
  string bless_content = 4; // 祝福内容
  int64 bless_time = 5; // 祝福时间
}

// 获取生日祝福列表
message GetRicherBirthdayBlessListReq {
  uint32 uid = 1; // 用户ID
  uint32 offset = 2;
  uint32 limit = 3;
}

message GetRicherBirthdayBlessListResp {
  repeated BlessInfo richer_birthday_bless_list = 1;
}
