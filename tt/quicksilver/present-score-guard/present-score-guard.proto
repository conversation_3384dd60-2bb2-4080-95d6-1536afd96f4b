syntax = "proto3";

package present_score_guard;
option go_package = "golang.52tt.com/protocol/services/present-score-guard";

message AddUserScoreReq
{
  uint32 uid = 1;
  int32 add_score = 2;
  string order_id = 3;
  uint32 op_uid = 4;
  uint32 change_reason = 5;	// ScoreChangeReason
  string order_desc = 6;
  bytes extend = 7;
  uint32 time_value = 8;	// 时间值会记录在日志表中，一般情况下改字段不允许填值，默认0的时候，svr处理时会取当前时间
  string deal_token = 9; //校验链
  uint32 score_type = 10; //0只能兑换T豆，1可提现
}

message AddUserScoreResp
{
  uint32 final_score = 1;
}

// buf:lint:ignore SERVICE_PASCAL_CASE
service present_score_guard {
  rpc AddUserScore( AddUserScoreReq ) returns( AddUserScoreResp ) {}
}