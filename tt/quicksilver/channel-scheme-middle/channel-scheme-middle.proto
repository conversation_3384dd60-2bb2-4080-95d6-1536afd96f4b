syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-scheme-middle";
package channel_scheme_middle;

import "tt/quicksilver/extension/options/options.proto";

service ChannelSchemeMiddle {

  option (service.options.service_ext) = {
    service_name: "channel-scheme-middle"
  };

  //同个虚拟类型下互相切换schemeDetailType
  rpc SwitchCurChannelSchemeType(SwitchCurChannelSchemeTypeReq) returns (SwitchCurChannelSchemeTypeResp) {
  }

  //设置玩法id
  rpc SetCurChannelScheme(SetCurChannelSchemeReq) returns (SetCurChannelSchemeResp) {}

  //获取房间当前麦位数, 并对玩法预期麦位数与真实麦位数进行一致性校验
  rpc GetCurMicSize(GetCurMicSizeReq) returns (GetCurMicSizeResp) {}

  //设置分类下的玩法的麦位数
  rpc SetSchemeBusinessRelatedMicSize(SetSchemeBusinessRelatedMicSizeReq) returns (SetSchemeBusinessRelatedMicSizeResp) {}
}

enum Source {
  UNKNOWN = 0;
  //1 ~ 100,切换类型
  UGC_GAME_SWITCH_MODE = 1;     //ugc开黑玩法切换模式

  //100~，设置玩法
  SET_UGC_CREATE = 101;        //ugc create
  SET_UGC_SWITCH = 102;        //switch
  SET_TEMP_CREATE = 103;       //临时房创建
  SET_COMMUNITY_CHANNEL_CREATE = 104;
  SET_COMMUNITY_CHANNEL_SWITCH = 105;

  SET_PGC_SWITCH = 120;        //pgc切换
  SET_PGC_CREATE = 121;
  SET_RADIO_LIVE_CREATE = 122 ;
  SET_OFFICIAL_CHANNEL_CREATE = 123;
  SET_CPL_CHANNEL_CREATE = 124;
  SET_CPL_CHANNEL_SWITCH = 125;
}

message SwitchCurChannelSchemeTypeReq{
  uint32 op_uid = 1;
  uint32 cid = 2;
  uint32 scheme_detail_type = 3;
  Source source = 4;
  uint32 app_id = 5;
  uint32 market_id = 6;
}

message SwitchCurChannelSchemeTypeResp{
  uint32 scheme_id = 1;
  string scheme_name = 2;
  uint32 scheme_detail_type = 3;
  uint64 switch_ms_ts = 4;  //切换时服务端毫秒时间戳
  uint32 cur_mic_size = 5;
}

message SetCurChannelSchemeReq{
  uint32 op_uid = 1;
  uint32 cid = 2;
  uint32 scheme_id = 3;
  Source source = 4;
  uint32 app_id = 5;
  uint32 market_id = 6;
  bool   not_set_mic_and_minigame = 7 ; //兼容用
}

message SetCurChannelSchemeResp {
  uint32 scheme_id = 1;
  string scheme_name = 2;
  uint32 scheme_type = 3;
  uint64 switch_ms_ts = 4; //切换时服务端毫秒时间戳
  uint32 cur_mic_size = 5;
  uint32 scheme_detail_type = 6;
}

// 获取房间当前麦位数, 并对玩法预期麦位数与真实麦位数进行一致性校验
message GetCurMicSizeReq {
  // cid
  uint32 cid = 1;
}

message GetCurMicSizeResp {
  // 麦位数
  uint32 mic_size = 1;
}

// 设置分类下的玩法的麦位数
message SetSchemeBusinessRelatedMicSizeReq {
  // 操作者uid
  uint32 op_uid = 1;
  // cid
  uint32 cid = 2;
  // 麦位数
  uint32 mic_size = 3;
  // 业务分类see channel-scheme.proto -> SchemeBusinessRelatedType
  uint32 scheme_related_type = 4;
}

message SetSchemeBusinessRelatedMicSizeResp {
}