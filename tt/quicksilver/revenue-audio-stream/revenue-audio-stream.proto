syntax = "proto3";

package revenue_audio_stream;

import "tt/quicksilver/extension/options/options.proto";

option go_package = "golang.52tt.com/protocol/services/revenue-audio-stream";

service RevenueAudioStream {
  option (service.options.old_package_name) = "RevenueAudioStream.RevenueAudioStream";

  rpc ReportAudioStream(ReportAudioStreamReq) returns (ReportAudioStreamResp) {}
  rpc StartPK(StartPKReq) returns (StartPKResp) {}
  rpc StopPK(StopPKReq) returns (StopPKResp) {}
  rpc RenewPK(RenewPKReq) returns (RenewPKResp) {}
  rpc GetAudioStreamInfo(GetAudioStreamInfoReq) returns (GetAudioStreamInfoResp) {}
  rpc SetBlankingStreamStatus(SetBlankingStreamStatusReq) returns (SetBlankingStreamStatusResp) {}
}

message AudioStreamInfo {
  uint32 channel_id = 1; // 房间id
  uint32 mic_id = 2;     // 麦位id
  string stream_id = 3;  // 语音流id
  uint32 uid = 4;        // 用户id
  string video_stream_id = 5; // 视频流id
}

message ChannelAudioStreamInfo {
  uint32 channel_id = 1;
  repeated AudioStreamInfo audio_stream_info = 2;
}

message PKAudioStreamInfo {
  string pk_id = 1; //  跟具体业务玩法数据携带的pk_id对应
  uint32 pk_type = 2; // PK业务类型, 见 PKType
  uint32 pk_status = 3;  // PK状态
  repeated ChannelAudioStreamInfo channel_audio_stream_info = 4; // 语音流信息
}

message BlankingStreamInfo {
  message ChannelBlankingInfo {
    uint32 channel_id = 1;
    uint32 status = 2; // 开/闭麦 见 BlankingStreamStatus
  }

  string pk_id = 1; // 跟具体业务玩法数据携带的pk_id对应
  uint32 pk_type = 2; // PK业务类型, 见 PKType
  repeated ChannelBlankingInfo channel_blanking_info = 3;
}

message ReportAudioStreamReq {
  AudioStreamInfo audio_stream_info = 1;
}

message ReportAudioStreamResp {}

message PKMember {
  uint32 cid = 1;
  repeated uint32 layout = 2; // 该成员主态视觉的布局顺序 例如有4个房间, 描述该视角看到的房间顺序: [cid1, cid3, cid2, cid4]
}

// 开始连麦
message StartPKReq {
  string pk_id = 1;
  uint32 pk_type = 2;                  // pk类型, 见 PKType
  repeated PKMember member_list = 3;  // 参与成员
  uint32 duration = 4;                  // 持续时间, 玩法侧超时未结束需要调用续租延长时间
}

message StartPKResp {
}

// 结束连麦
message StopPKReq {
  string pk_id = 1;
  uint32 pk_type = 2;
}

message StopPKResp {}

// 连麦时长续租
message RenewPKReq {
  string pk_id = 1;
  uint32 pk_type = 2; // pk类型, 见 PKType
  uint32 duration = 4;
}

message RenewPKResp {}


// 进房获取语音流信息
message GetAudioStreamInfoReq {
  uint32 channel_id = 1;  // 本房间id
}

message GetAudioStreamInfoResp {
  PKAudioStreamInfo pk_audio_stream_info = 2; // PK语音流信息
  BlankingStreamInfo blanking_stream_info = 3;    // 当前房对其他房间的闭麦数据
}


// 设置麦位状态, 开闭麦
message SetBlankingStreamStatusReq {
  uint32 my_channel_id = 1;    // 我方房间id
  uint32 target_channel_id = 2; // 被闭麦的房间id
  uint32 target_mic_id = 3;    // 被闭麦的麦位id, 全房间闭麦填0
  uint32 status = 4;   // 开/闭麦 见 BlankingStreamStatus
}

message SetBlankingStreamStatusResp {}
