syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/knightgroupmembers";
package knightgroupmembers;

import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service KnightGroupMembers {
    rpc JoinKnightGroup (JoinKnightGroupReq) returns (JoinKnightGroupResp) {
    }

    //  T豆开通骑士团，预下单
    rpc JoinKnightPreOrder(JoinKnightGroupReq) returns (JoinKnightGroupResp) {
    }

    rpc GetJoinKnightPreOrder ( GetJoinOrderReq ) returns ( GetJoinOrderResp ){
    }

    ////骑士查自己在当前房间的信息
    rpc GetKnightInfo (GetKnightInfoReq) returns (GetKnightInfoResp) {
    }
    rpc GetKnightGroupMember (GetKnightGroupMemberReq) returns (GetKnightGroupMemberResp) {
    }

    rpc TimeRangeCount ( TimeRangeReq ) returns ( CountResp ) {
    }

    rpc TimeRangeOrderIds ( TimeRangeReq ) returns ( OrderIdsResp ) {
    }
    //补单-主播积分
    rpc FixKnighAnchorScoreOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {
    }

    //补单-开通骑士团
    rpc FixJoinKnightOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {
    }

    rpc SetFirstChief ( SetFirstChiefReq ) returns ( SetFirstChiefResp ) {
    }

    rpc BatchGetMember(BatchGetMemberReq)returns(BatchGetMemberResp){
    }

    rpc ModifyMember( ModifyMemberReq ) returns( ModifyMemberResp ){
    }

    rpc GetJoinOrder ( GetJoinOrderReq ) returns ( GetJoinOrderResp ){
    }

    rpc CheckCanJoinKnightGroup (CheckCanJoinKnightGroupReq) returns (CheckCanJoinKnightGroupRsp){
    }
    
    //查询用户开通的所有骑士信息
    rpc GetKnightAllAnchorInfos (GetKnightAllAnchorInfosReq) returns (GetKnightAllAnchorInfosRsp){}
    //查询所有有骑士的主播信息
    rpc GetAllAnchorHasKnight (GetAllAnchorHasKnightReq) returns (GetAllAnchorHasKnightRsp){}
 }

message GetJoinOrderReq{
    string order_id = 1;
}

message GetJoinOrderResp{
    JoinKnightOrder order_info =1;
    string deal_token = 2;
}


message JoinKnightOrder {
    string order_id = 1;
    uint32 knight_uid = 2;
    uint32 anchor_uid = 3;
    uint32 channel_type = 4;
    uint32 channel_id = 5;
    uint32 guild_id = 6;
    uint32 server_time = 7;
    uint32 price = 8;
    uint32 begin_time = 9;
    uint32 expire_time = 10;
    uint32 source = 11;       //JoinKnightGroupSource
    uint32 param  = 12;       //参数(比如骑士卡ID)
    string param_str = 13;    //开通参数
}

//查自己在当前房间的骑士信息
message GetKnightInfoReq{
    uint32 knight_uid = 1;
    uint32 channel_id = 2;
}

message KnightInfo {
    uint32 anchor_uid = 1;
    uint32 begin_time = 2;  //
    uint32 end_time   = 3;
}

message GetKnightInfoResp{
    bool   first_chief = 1; //是否首席
    repeated KnightInfo knight_info_list = 2;
}

message KnightMember {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 anchor_uid = 3;
    uint32 begin_time = 4;  //
    uint32 end_time   = 5;
    uint32 total_day  = 6;  //总开通天数
}

//主播取自己骑士团成员列表,需要分页？
message GetKnightGroupMemberReq{
    uint32 channel_id = 1;
    uint32 anchor_uid = 2;
    bool   with_member_list = 3;//是否需要成员列表
}

message GetKnightGroupMemberResp{
    uint32 total_member_cnt = 1;
    repeated KnightMember knight_member_list = 2;
    uint32 chief_uid = 3;
}

//加入骑士团
message JoinKnightGroupReq{
    JoinKnightOrder join_order = 1;
    string deal_token = 2;
}

message JoinKnightGroupResp{
}

enum JoinKnightGroupSource {
    NORMAL  = 0;              //正常T豆开通
    FREE_CARD  = 1;           //骑士体验卡开通
}
message JoinKnightGroupEvent {
    string order_id = 1;
    uint32 anchor_uid = 2; //主播UID
    uint32 knight_uid = 3;
    uint32 channel_id = 4;
    uint32 channel_type = 5;
    uint32 guild_id = 6;
    uint32 price = 7;           //如果是骑士体验卡开通price为0
    uint32 create_time = 8;
    uint32 begin_time  = 9;
    uint32 expire_time = 10;
    string deal_token  = 11;
    uint32 source      = 12;    //开通来源类型:JoinKnightGroupSource
    uint32 total_day   = 13;    //总开通天数
}


message SetFirstChiefReq {
    uint32 channel_id = 1;
    uint32 anchor_uid = 2;
    uint32 knight_uid = 3;
    uint32 expire_time = 4;
    bool   force_send_msg = 5;
}

message SetFirstChiefResp{}

message BatchGetMemberReq{
    uint32 index=1;
    uint32 off=2;
    uint32 count=3;
}
message BatchGetMemberResp{
    repeated KnightMember knight_member_list = 1;
}

//检查是否能使用开通骑士团
message CheckCanJoinKnightGroupReq {
    uint32 channel_id = 1;
    uint32 anchor_uid = 2;
    uint32 knight_uid = 3;
}
message CheckCanJoinKnightGroupRsp {
    uint32 guild_id = 1;
    uint32 channel_type = 2;
}

//对账接口
//获得订单数据
message TimeRangeReq {
  int64 begin_time = 1;
  int64 end_time = 2;
  string params = 3;
}

//响应order_id个数
message CountResp {
  uint32 count = 1;
  uint32 value = 2; //总价值，如果没有填0
}

//响应orderId详情
message OrderIdsResp {
  repeated string order_ids = 1;
}

//for test
message ModifyMemberReq{
    uint32 knight_uid = 1;
    uint32 anchor_uid = 2;
    uint32 channel_id = 3;
    uint32 expire_time = 4;
    uint32 begin_time = 5;
}

message ModifyMemberResp{
}

//查询用户开通的所有骑士信息
message GetKnightAllAnchorInfosReq {
	uint32 knight_uid = 1;
}
message GetKnightAllAnchorInfosRsp {
	repeated KnightMember info_list = 2;
}

//查询所有有骑士的主播信息
message GetAllAnchorHasKnightReq {
}

message GetAllAnchorHasKnightRsp {
	repeated uint32 anchor_uids = 1;       //主播列表
}