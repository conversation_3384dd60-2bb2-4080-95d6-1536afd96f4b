syntax = "proto3";

package social_community_encourage;

option go_package = "golang.52tt.com/protocol/services/social-community-encourage";

service SocialCommunityEncourage {

  rpc CreateInvitationCode(CreateInvitationCodeRequest) returns (CreateInvitationCodeResponse) {}
  rpc CheckInvitationCode(CheckInvitationCodeRequest) returns (CheckInvitationCodeResponse) {}
  rpc GetInvitationCodeDetail(GetInvitationCodeDetailRequest) returns (GetInvitationCodeDetailResponse) {}
  rpc GetInvitationCodeLatestBeInviteUser(GetInvitationCodeLatestBeInviteUserRequest) returns (GetInvitationCodeLatestBeInviteUserResponse) {}

  rpc IsValidUser(IsValidUserRequest) returns (IsValidUserResponse) {}

  rpc GenShareText(GenShareTextReq) returns (GenShareTextResp) {}

  rpc ActivateJoinAward(ActivateJoinAwardReq) returns (ActivateJoinAwardResp) {} // 激活加入奖励

  rpc GetMissionDetail(GetMissionDetailReq) returns (GetMissionDetailResp) {} // 获取我的任务信息

  rpc ComeBack(ComeBackReq) returns (ComeBackResp) {} // 回到社群
  rpc TestComeBack(TestComeBackReq) returns (TestComeBackResp) {}

  rpc GetFundPool(GetFundPoolReq) returns (GetFundPoolResp) {} // 获取基金池信息
  rpc ReceiveFundPoolAward(ReceiveFundPoolAwardReq) returns (ReceiveFundPoolAwardResp) {} // 领取基金池奖励
  rpc GetWithdrawQuota(GetWithdrawQuotaReq) returns (GetWithdrawQuotaResp) {} // 获取提现额度
  rpc BindWechat(BindWechatReq) returns (BindWechatResp) {} // 绑定微信
  rpc GetBindWechat(GetBindWechatReq) returns (GetBindWechatResp) {} // 获取绑定微信状态
  rpc Withdraw(WithdrawReq) returns (WithdrawResp) {} // 提现
  rpc GetWithdrawAmount(GetWithdrawAmountReq) returns (GetWithdrawAmountResp) {} // 获取提现金额信息

  rpc CanSendTaskReminderToday(CanSendTaskReminderTodayReq) returns (CanSendTaskReminderTodayResp) {}
  rpc GetMySocialCommunityAward(GetMySocialCommunityAwardReq)returns(GetMySocialCommunityAwardResp){}
  rpc GetCaptainInviteMemberAmounts(GetCaptainInviteMemberAmountsReq)returns(GetCaptainInviteMemberAmountsResp){}
  rpc GetEncourageDetailsList(GetEncourageDetailsListReq)returns(GetEncourageDetailsListResp){}

  rpc ListWithdrawRecord(ListWithdrawRecordReq) returns (ListWithdrawRecordResp) {} // 提现记录
  rpc WithdrawFrequencyLimit(WithdrawFrequencyLimitReq) returns (WithdrawFrequencyLimitResp) {} // 提现频率是否达到上限
  rpc GetWithdrawCountInPeriod(GetWithdrawCountInPeriodReq) returns (GetWithdrawCountInPeriodResp) {} // 获取本周提现次数
  rpc WechatWithdrawCallback(WechatWithdrawCallbackReq) returns (WechatWithdrawCallbackResp) {} // 提现回调
  rpc UserHasWithdrawInProcessing(UserHasWithdrawInProcessingReq) returns (UserHasWithdrawInProcessingResp) {} // 用户是否有提现中的订单

  rpc SetRiskMngUser(SetRiskMngUserReq) returns (SetRiskMngUserResp) {}
  rpc TestExpireRecycleBalance(TestExpireRecycleBalanceReq)returns(TestExpireRecycleBalanceResp){}
}

message TestExpireRecycleBalanceReq{

}
message TestExpireRecycleBalanceResp{

}

message IsValidUserRequest {
  uint32 uid = 1;
}

message IsValidUserResponse {
  bool is_close = 1;
  bool is_valid = 2;

  enum UserType {
    NewReg = 0;
    Recall = 1;
    Wait = 2;
  }
  UserType user_type = 3;
}

message CheckInvitationCodeRequest {
  uint32 uid = 1;
  string invitation_code = 2;
  string social_community_id = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CheckInvitationCodeResponse {
  bool isValid = 1;
}



message GenShareTextReq {
  uint32 uid = 1;
  string social_community_id = 2;
  string social_community_name = 3;
  string long_url = 4;
  string tt_url = 5;
  string background_url = 6;
  string invitation_code = 7;
  string content = 8;
}

message GenShareTextResp {
  string content = 1; // 整个的文本内容
  string short_url = 2; // 短连接
  string pass_code = 3; // 口令码
}

message CreateInvitationCodeRequest {
  uint32 uid = 1;
  string social_community_id = 2;
}

message CreateInvitationCodeResponse {
  string invitation_code = 1;
}


enum FundPoolSegmentLockStatus{
  FundPoolUnLock = 0;
  FundPoolLock = 1;
  FundPoolInProgress = 2; // 进行中
}

// 社群奖励基金池分段配置
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message FundPoolSegment{
  uint32 max_member_count = 1; // 人数上限
  uint32 Amount = 2; // 金额 单位 分
  uint32 unlock_status = 3; // 是否解锁 FundPoolSegmentLockStatus
}

message FundPoolInfoByMonth{
  uint32 unlock_status = 1; // 是否解锁 FundPoolSegmentLockStatus
  uint32 has_received_fee = 2; // 已领取金额 单位 分[向下取]
  repeated FundPoolSegment segments = 3; // 分段配置
  uint32 month = 4; // 月份
  uint32 remain_days = 5; // 剩余天数
  uint32 member_count_to_unlock = 6; // 还差xx人解锁
  bool can_receive = 7; // 是否可以领取
}

// 获取基金池信息
message GetFundPoolReq{
  uint32 uid = 1;
  string social_community_id = 2;
}

message GetFundPoolResp{
  repeated FundPoolInfoByMonth fund_pool_info_list = 1;
}

// 领取基金池奖励
message ReceiveFundPoolAwardReq{
  uint32 uid = 1;
  string social_community_id = 2;
}

message ReceiveFundPoolAwardResp{
  uint32 amount = 1; // 领取金额 单位分
}

// 提现额度
message GetWithdrawQuotaReq{
  uint32 uid = 1;
}

message FirstWithdrawQuota{
  int32 amount = 1; // 首次提现金额 单位分
  bool  is_present = 2;
}

message WithdrawQuota{
  int32 amount = 1; // 提现金额 单位分
  bool unlock_status = 2;   //true解锁，false 未解锁
  bool is_present = 3;
}

message GetWithdrawQuotaResp{
  FirstWithdrawQuota first_withdraw = 1;// 首次提现
  repeated WithdrawQuota withdraw = 2;
}

// 绑定微信
message BindWechatReq{
  uint32 uid = 1;
  string code = 2;
}

message BindWechatResp{
}

message GetBindWechatReq{
  uint32 uid = 1;
}

message GetBindWechatResp{
  bool is_bind = 1;
}

message WithdrawReq{
  uint32 uid = 1;
  int32 amount = 2; // 提现金额 单位分
  string order_id = 3; //订单号
  string reason_detail = 4; //理由
  uint32 server_time = 5;
  bool is_rollback = 6; // 是否回滚
}

message WithdrawResp{
}

// 获取提现金额信息
message GetWithdrawAmountReq{
  uint32 uid = 1;
}

message GetWithdrawAmountResp{
  uint32 total_amount = 1; // 累计激励金 单位分
  uint32 balance = 2; // 激励金余额 单位分
  uint32 total_withdraw_amount = 3; // 累计提现金额 单位分
}

// 提现记录
message ListWithdrawRecordReq{
  uint32 uid = 1;
  uint32 offset_id = 2;
  uint32 size = 3;
}

enum WithdrawRecordStatus{
  WithdrawRecordStatusInProgress = 0; // 提现中
  WithdrawRecordStatusSuccess = 1;
  WithdrawRecordStatusFail = 2;
}

message WithdrawRecord{
  uint64 id = 1;
  string order_id= 2;
  int32 amount = 3; // 提现金额 单位分
  uint32 status = 4; // WithdrawRecordStatus
  uint32 create_time = 5;
  string order_msg = 6; // 提现失败原因
}

message ListWithdrawRecordResp{
  repeated WithdrawRecord records = 1;
}

// 每周提现次数
message GetWithdrawCountInPeriodReq{
  uint32 uid = 1;
  string period = 2; // 周期: 10min hourly daily weekly monthly
}

message GetWithdrawCountInPeriodResp{
  uint32 total_count = 1;
  uint32 used_count = 2;
}

// 提现频率是否达到上限
message WithdrawFrequencyLimitReq{
  uint32 uid = 1;
}

message WithdrawFrequencyLimitResp{
  bool can_withdraw = 1; // 是否可以提现 true:可以提现 false:不可以提现
}

// 提现回调
message WechatWithdrawCallbackReq{
  uint32 uid = 1;
  string order_id = 2;
  float amount = 3;
  string order_status = 4;
  string order_msg = 5; // 提现失败原因
}

message WechatWithdrawCallbackResp{
  string result = 1; // 成功：success 失败：fail
}

message ActivateJoinAwardReq {
  uint32 uid = 1;
  uint32 user_type = 2;
  string invitation_code = 3;
  string social_community_id = 4;
  string social_community_full_name = 5;
  uint32 captain_uid = 6;
  bool is_auto_input = 7;
}

message ActivateJoinAwardResp {

}

message GetInvitationCodeDetailRequest {
  string invitation_code = 1;
}

message GetInvitationCodeDetailResponse {
  uint32 create_uid = 1;
  string social_community_id = 2;
  int64 create_ts = 3;
}

message CanSendTaskReminderTodayReq{
      uint32 uid=1;
      uint32 to_uid=2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CanSendTaskReminderTodayResp{
    bool canSendToday=1;
}

message GetMySocialCommunityAwardReq{
    uint32 uid=1;
    string social_community_id=2;
}

message GetMySocialCommunityAwardResp{
  uint32 fee=1;
  uint32 invite_amount=2;
  uint32 balance=3;
  uint32 fund_pool_amount=4; // 基金池金额
}

message GetCaptainInviteMemberAmountsReq{
  uint32 uid=1;
  string social_community_id=2;
}

message GetCaptainInviteMemberAmountsResp{
  uint32 member_invite_amount=1;
}

message ComeBackReq {
  uint32 uid = 1;
  uint32 captain_uid = 2;
  bool inviter_in_social = 3;
}

message ComeBackResp {
}

message TestComeBackReq {
  uint32 uid = 1;
  int32 sub_update_day = 2;
  int32 sub_create_day = 3;
}

message TestComeBackResp {
}

message GetMissionDetailReq {
  uint32 uid = 1;
}

message GetMissionDetailResp {
  uint32 uid = 1;
  string social_community_id = 2;
  string invite_code = 3;
  uint32 invite_uid = 4;
  uint32 day = 5;
  int64 day_ts = 6;
  bool expired = 7;
  bool complete = 8;
}

enum RecruitSchedule{
  RecruitSchedule_NotStart = 0;
  RecruitSchedule_InProgress = 1;
  RecruitSchedule_Finished = 2;
}
message  GetEncourageDetailsListReq{
  uint32 uid=1;
  string social_community_id=2;
  int32 offset_id=3;// 主键ID
  int32 limit=4;
  int32  recruit_schedule=5;
 }

 message GetEncourageDetailsListResp{
   repeated UserEncourageRecord record=1;
   int64 count=2;
 }

message UserEncourageRecord{
  uint64 id=1;
  uint32 uid=2;
  uint64 create_time=3;
  int32  rewards=4;  //奖励
  string title=5;    //大标题
  string social_community_id=6;
  repeated TaskInfo  extra_task=7;     //
  int32  user_type=8; //用户类型
  string source=9;  //用户来源
}

message TaskInfo{
  // buf:lint:ignore ONEOF_LOWER_SNAKE_CASE
  oneof Task{
    OverMultipleDaysTask  over_multiple_days_task=1;
  }
}

message OverMultipleDaysTask{
  string front_content=1;
  string progress_content=2;
  string back_content=3;
}


message SetRiskMngUserReq{
  uint32 uid=1;
  string reason=2;
}

message SetRiskMngUserResp{

}

// 用户是否有提现中的订单
message UserHasWithdrawInProcessingReq{
  uint32 uid=1;
}

message UserHasWithdrawInProcessingResp{
  bool has_withdraw_in_processing=1;
}

message GetInvitationCodeLatestBeInviteUserRequest {
  string invitation_code = 1;
}

message GetInvitationCodeLatestBeInviteUserResponse {
  uint32 uid = 1;
}