syntax = "proto3";
option go_package = "golang.52tt.com/protocol/services/usermodify-subscriber";
package usermodifysubscriber;

service UserModifySubscriber {
  //通过下发用户更改信息
  rpc PushUserGenderModify(PushUserGenderModifyReq) returns (PushUserGenderModifyResp) {}
}

message PushUserGenderModifyReq {
    uint32 uid = 1;  
	uint32 gender = 2;
	uint32 gender_audio = 3;
    uint32 update_time = 4; //更新时间	
}

message PushUserGenderModifyResp {

}


