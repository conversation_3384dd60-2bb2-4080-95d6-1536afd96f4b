syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-scheme-conf-mgr";
package channel_scheme_conf_mgr;

service ChannelSchemeConfBase{
  //给运营后台用的增删改查
  rpc CreateChannelScheme(CreateChannelSchemeReq) returns (CreateChannelSchemeResp){}
  rpc ModifyChannelScheme(ModifyChannelSchemeReq) returns (ModifyChannelSchemeResp){}
  rpc DeleteChannelScheme(DeleteChannelSchemeReq) returns (DeleteChannelSchemeResp){
  }
  rpc GetChannelSchemeBaseConfList(GetChannelSchemeBaseConfListReq) returns (GetChannelSchemeBaseConfListResp) {
  }

  //cache接口
  rpc GetChannelSchemeConfCache (GetChannelSchemeConfCacheReq) returns (GetChannelSchemeConfCacheResp){
  }
  rpc GetAllChannelSchemeConfCache(GetAllChannelSchemeConfCacheReq) returns (GetAllChannelSchemeConfCacheResp) {
  }

  ///////////////废弃//////////////////////////////////////////////////////////////////////////////////////////////////
  /*rpc CreateChannelSchemeBaseConf(CreateChannelSchemeBaseConfReq) returns (CreateChannelSchemeBaseConfResp){
  }
  rpc ModifyChannelSchemeBaseConf(ModifyChannelSchemeBaseConfReq) returns (ModifyChannelSchemeBaseConfResp){
  }
  //扩展配置的增删改查
  rpc CreateChannelSchemeExtraConf(CreateChannelSchemeExtraConfReq) returns (CreateChannelSchemeExtraConfResp){
  }
  rpc ModifyChannelSchemeExtraConf(ModifyChannelSchemeExtraConfReq) returns (ModifyChannelSchemeExtraConfResp){
  }
  rpc GetChannelSchemeExtraConfList(GetChannelSchemeExtarConfListReq) returns (GetChannelSchemeExtarConfListResp) {
  }*/
  ///////////////废弃//////////////////////////////////////////////////////////////////////////////////////////////////

  //下面的接口后续逐渐废弃
  //rpc GetChannelSchemeBaseConfCache (GetChannelSchemeBaseConfCacheReq) returns (GetChannelSchemeBaseConfCacheResp){
  //}
  //rpc GetChannelSchemeExtraConfCache(GetChannelSchemeExtraConfCacheReq) returns (GetChannelSchemeExtraConfCacheResp){
  //}
  rpc GetAllChannelSchemeBaseConfCache(GetAllChannelSchemeBaseConfCacheReq) returns (GetAllChannelSchemeBaseConfCacheResp){
  }
  rpc GetAllChannelSchemeExtraConfCacheV2(GetAllChannelSchemeExtraConfCacheV2Req) returns (GetAllChannelSchemeExtraConfCacheV2Resp){
  }
  rpc GetAllSpecSchemeDetailTypeConfCache(GetAllSpecSchemeDetailTypeConfCacheReq) returns (GetAllSpecSchemeDetailTypeConfCacheResp) {
  }
}

message CreateChannelSchemeReq {
  uint32 scheme_id = 1;                                      //对应之前topic-channel的tabid
  string scheme_name = 2;
  string scheme_icon = 3;                                    //非必填
  uint32 scheme_type = 4;                                    //玩法类型，枚举:开黑,小游戏,音乐等。(对应之前旧配置的TabType,定义在channel-scheme_.proto SchemeType枚举)
  uint32 business_category = 5 ;                //参考channel-scheme_.proto的SchemeBusinessCategory枚举
  uint32 default_scheme_detail_type = 6;        //默认玩法详细类型，区分是你行你唱还是pia戏等，定义在channel-scheme_.proto的枚举类型SchemeDetailType
  uint32 third_bind_id = 7;                     //该玩法绑定的第三方id(例如是小游戏玩法，则这个值填对应的小游戏id)
}
message CreateChannelSchemeResp {
  uint32 scheme_id = 1;
  uint32 mic_mode = 2;    //兼容旧接口用
}
message ModifyChannelSchemeReq {
  uint32 scheme_id = 1;                                      //对应之前topic-channel的tabid
  string scheme_name = 2;
  string scheme_icon = 3;                                    //非必填
  uint32 scheme_type = 4;                                    //玩法类型，枚举:开黑,小游戏,音乐等。(对应之前旧配置的TabType,定义在channel-scheme_.proto SchemeType枚举)
  uint32 business_category = 5 ;                //参考channel-scheme_.proto的SchemeBusinessCategory枚举
  uint32 default_scheme_detail_type = 6;        //默认玩法详细类型，区分是你行你唱还是pia戏等，定义在channel-scheme_.proto的枚举类型SchemeDetailType
  uint32 third_bind_id = 7;                     //该玩法绑定的第三方id(例如是小游戏玩法，则这个值填对应的小游戏id)
}
message ModifyChannelSchemeResp {
  uint32 scheme_id = 1;
  uint32 mic_mode = 2;    //兼容旧接口用
}
message DeleteChannelSchemeReq {
  uint32 scheme_id = 1;
}
message DeleteChannelSchemeResp {
}
message GetChannelSchemeBaseConfListReq {
  string scheme_name = 1;      //模糊匹配
  uint32 page_size = 2;
  uint32 page_num = 3;
}
message GetChannelSchemeBaseConfListResp {
  repeated ChannelSchemeBaseConf base_conf_list = 1;
  uint32 total = 2;
}
///////////////////////////////////运营后台接口 end


message GetChannelSchemeConfCacheReq {
  uint32 scheme_id = 1;
  uint32 scheme_detail_type = 2;  //如果为0，则返回该玩法默认的玩法类型的配置
}
message GetChannelSchemeConfCacheResp {
    ChannelSchemeConf conf = 1;
}
message GetAllChannelSchemeConfCacheReq {
}
message GetAllChannelSchemeConfCacheResp {
  repeated ChannelSchemeConf conf_list = 1;
}

message ChannelSchemeConf {
  ChannelSchemeBaseConf base_conf = 1;
  ChannelSchemeExtraConfV2 extra_conf =2;
}

//玩法基础信息
message ChannelSchemeBaseConf{
  uint32 scheme_id = 1;                                      //对应之前topic-channel的tabid
  string scheme_name = 2;
  string scheme_icon = 3;
  uint32 scheme_type = 4;                                    //玩法类型，枚举:开黑,小游戏,音乐等。(对应之前旧配置的TabType,定义在channel-scheme_.proto SchemeType枚举)
  SchemeChannelCategory scheme_channel_category = 5;         //废弃
  LayoutInfo layout = 6;                                     //布局配置
  MicAudioParam   mic_audio = 7;                             //麦位音频配置
  uint32 scheme_svr_detail_type = 8;            //废弃,用下面的default_scheme_detail_type
  uint32 business_category = 9 ;                //参考channel-scheme_.proto的SchemeBusinessCategory枚举
  uint32 default_scheme_detail_type = 10;       //默认玩法详细类型，区分是你行你唱还是pia戏等，定义在channel-scheme_.proto的枚举类型SchemeDetailType
  RelationSchemeDetailTypeList relation_scheme_detail_type_list = 11;          //该玩法关联的玩法类型(即在该玩法下可以玩的所有玩法类型)
  uint32 fallback_scheme_detail_type = 12 ;  //兜底玩法类型(客户端不认识玩法类型时兜底用的)
  uint32 third_bind_id = 13;                    //该玩法绑定的第三方id(例如是小游戏玩法，则这个值填对应的小游戏id)
}

message LayoutInfo {
  uint32 layout_type = 1;                  //布局类型，枚举，给客户端区分不同的房间布局用，类似于客户端之前对麦位模式的使用。服务端原则上不要用该字段做业务逻辑。定义在channel-scheme_.proto SchemeLayoutType枚举
  uint32 default_mic_size = 2;             //默认麦位数,以后可以通过这个麦位数来限制每个玩法可以上的麦位
  uint32 mic_mode = 3;                     //兼容老版本:麦位模式
}

//麦位音频参数
message MicAudioParam {
  uint32 mic_audio_type = 1;   //枚举，高音质，低音质等
  //音道等其他参数
  string mic_audio_sdk_info_mode = 2;  //音频sdk信息模式
}

//不管虚拟多少层，虚拟类型的枚举值都可以放在这里
enum SchemeVirtualType {
  SCHEME_VIRTUAL_TYPE_NO = 0 ;
  SCHEME_VIRTUAL_TYPE_GAME = 1;   //游戏开黑虚拟类型，包括开黑语音模式，开黑文字模式
}
/*虚拟类型下的玩法之间切换模式，是跟随玩法id还是跟随玩法类型
已开黑虚拟类型为例子，例如在王者玩法下，切到文字模式，如果是跟随玩法类型，则所有开黑类型的玩法都变成了文字模式
如果是跟随玩法id，则只有王者变成了文字模式*/
enum SchemeVirtualTypeSwitchMode {
  SCHEME_VIRTUAL_TYPE_SWITCH_UNKNOWN = 0;
  SCHEME_VIRTUAL_TYPE_SWITCH_FOLLOW_TYPE = 1;
  SCHEME_VIRTUAL_TYPE_SWITCH_FOLLOW_ID = 2;
}

//互相关联的玩法详细类型
message RelationSchemeDetailTypeList {
  SchemeVirtualType virtual_type = 1;      //相关联的玩法之间会构成一个虚拟玩法类型，例如开黑语音模式和开黑文字模式都属于开黑玩法
  SchemeVirtualTypeSwitchMode switch_mode                = 2;      //see SchemeVirtualTypeSwitchMode
  repeated RelationSchemeDetailType scheme_detail_type_list = 3;  //该玩法关联的玩法类型(即在该玩法下可以玩的所有玩法类型)
}
message RelationSchemeDetailType {     //可再继续扩展一层，应该就够了
  uint32 default_scheme_detail_type = 1;      //对应的实体类型列表...
}


//玩法扩展信息
message ChannelSchemeExtraConfV2{
  uint32 scheme_id = 1;
  string scheme_name = 2;
  uint32 max_member_size = 3;                           //玩法最大容量,服务端根据该容量来限制具体玩法的进房人数,且从大容量切到小容量房间时，客户端需要主动退房

  repeated ClientVersion min_enter_version_limit = 4;       //客户端最低版本要求
  repeated ClientVersion min_mic_version_limit = 5;         //废弃

  bool auto_exit_by_version = 6;       //是否根据最低版本要求检查是否自动退房
  bool auto_exit_by_max_size = 7;         //是否根据最大人数检查是否自动退房

  uint32 mic_control = 8;     //是否能换麦，抱麦，自动锁麦，切换玩法时是否能踢掉麦上人等
  bool use_new_control_when_switch = 9; //切换玩法时，是否用新的mic_control去控制是否踢掉麦上的人,是否恢复锁麦，闭麦

  //主持麦,mvp麦
  repeated uint32 chair_mic_id_list = 10 ;  //主持麦只有管理员才能上
  repeated uint32 mvp_mic_id_list = 11 ;    //mvp麦不能通过普通的上麦命令上麦,需要由对应玩法单独的接口上麦
}
message ClientVersion{
  uint32 client_type = 1; //和pkg/protocol/terminal.go的ClientType定义一样
  string version  = 2;    //格式:6.3.0
}

enum EMicControlBit {
  MIC_CONTROL_UNKNOWN        = 0;
  MIC_CONTROL_NOT_KICK_OUT_MIC = 1;     //切换玩法时，不踢掉麦上的人，默认是踢的
  MIC_CONTROL_NOT_UNLOCK_MIC = 2;       //切换玩法时，不解锁已上锁的麦位,默认是解的
  MIC_CONTROL_UNMUTE_MIC = 4;           //切换玩法时，恢复闭麦位,默认是不恢复的
  MIC_CONTROL_CAN_CHANGE_MIC = 8;
  MIC_CONTROL_CAN_TAKE_MIC = 16;
  MIC_CONTROL_FORBIDDEN_AUTO_LOCK_MIC = 32;   //禁止自动锁麦
}

//特殊的玩法类型信息：指只能通过和其他玩法类型进行关联绑定的玩法类型，例如开黑文字玩法类型绑定到开黑玩法类型下
message GetAllSpecSchemeDetailTypeConfCacheReq {
}
message SpeChannelSchemeTypeConf{
  uint32 scheme_detail_type = 1;
  LayoutInfo layout = 2;                                     //布局配置
  MicAudioParam   mic_audio = 3;                             //麦位音频配置
  uint32 max_member_size = 4;                           //玩法最大容量,服务端根据该容量来限制具体玩法的进房人数,且从大容量切到小容量房间时，客户端需要主动退房

  repeated ClientVersion min_enter_version_limit = 5;       //客户端最低版本要求
  repeated ClientVersion min_mic_version_limit = 6;         //麦位版本要求

  bool auto_exit_by_version = 7;       //是否根据最低版本要求检查是否自动退房
  bool auto_exit_by_max_size = 8;         //是否根据最大人数检查是否自动退房

  uint32 mic_control = 9;     //是否能换麦，抱麦，自动锁麦，切换玩法时是否能踢掉麦上人等
  bool use_new_control_when_switch = 10; //切换玩法时，是否用新的mic_control去控制是否踢掉麦上的人,是否恢复锁麦，闭麦

  //主持麦,mvp麦
  repeated uint32 chair_mic_id_list = 11 ;  //主持麦只有管理员才能上
  repeated uint32 mvp_mic_id_list = 12 ;    //mvp麦不能通过普通的上麦命令上麦,需要由对应玩法单独的接口上麦
  uint32 fallback_scheme_detail_type = 13 ;  //兜底玩法类型(客户端不认识玩法类型时兜底用的)
}
message GetAllSpecSchemeDetailTypeConfCacheResp {
  repeated SpeChannelSchemeTypeConf conf_list = 1;
}


///////////////////////////////////////////////////下面的后续逐渐废弃
/*message GetChannelSchemeBaseConfCacheReq {
  repeated uint32 scheme_id_list = 1;
}
message  GetChannelSchemeBaseConfCacheResp {
  repeated ChannelSchemeBaseConf base_conf_list = 1;
}

message GetChannelSchemeExtraConfCacheReq {
  repeated uint32 scheme_id_list = 1;
}
message GetChannelSchemeExtraConfCacheResp {
  repeated ChannelSchemeExtraConfV2 extra_conf_list = 1;
}*/

message GetAllChannelSchemeBaseConfCacheReq{
}
message GetAllChannelSchemeBaseConfCacheResp{
  repeated ChannelSchemeBaseConf base_conf_list = 1;
}

message GetAllChannelSchemeExtraConfCacheV2Req{
}
message GetAllChannelSchemeExtraConfCacheV2Resp{
  repeated ChannelSchemeExtraConfV2 extra_conf_list = 1;
}


/*
message CreateChannelSchemeBaseConfReq {
  ChannelSchemeBaseConf base_conf = 1;
}
message CreateChannelSchemeBaseConfResp {
  uint32 scheme_id = 1;
}
//覆盖式修改
message ModifyChannelSchemeBaseConfReq {
  ChannelSchemeBaseConf base_conf = 1;
}
message ModifyChannelSchemeBaseConfResp {
}

message CreateChannelSchemeExtraConfReq {
  ChannelSchemeExtraConfV2 extra_conf = 1;
}
message CreateChannelSchemeExtraConfResp {
  uint32 scheme_id = 1;
}
//覆盖式修改
message ModifyChannelSchemeExtraConfReq {
  ChannelSchemeExtraConfV2 extra_conf = 1;
}
message ModifyChannelSchemeExtraConfResp {
}*/


/*
message GetChannelSchemeExtarConfListReq {
  repeated uint32 scheme_id_list = 1;
}
message GetChannelSchemeExtarConfListResp {
  repeated ChannelSchemeExtraConfV2 extra_conf_list = 1;
}
*/


//废弃，以后新的玩法定义在channel-scheme_.proto的枚举类型SchemeDetailType，这里不在加新的玩法
enum SchemeSvrDetailType {
  SCHEME_SVR_DETAIL_TYPE_UNKNOWN = 0 ;
  SCHEME_SVR_DETAIL_TYPE_FUN = 1;    //娱乐玩法
  SCHEME_SVR_DETAIL_TYPE_GAME = 2;   //游戏开黑玩法
  SCHEME_SVR_DETAIL_TYPE_LIVE = 3;    // 直播
  SCHEME_SVR_DETAIL_TYPE_DATING = 4;          // 相亲
  SCHEME_SVR_DETAIL_TYPE_MINI_GAME = 5;        // 小游戏
  SCHEME_SVR_DETAIL_TYPE_MASKED_DATING = 6;    // 1V1语音聊天（蒙面聊天）
  SCHEME_SVR_DETAIL_TYPE_CP = 7;               // 活动大房cp玩法
  SCHEME_SVR_DETAIL_TYPE_IDOL = 8;             // 活动大房偶像玩法
  SCHEME_SVR_DETAIL_TYPE_CP_BATTLE_GAME = 9;   // cp战玩法
  SCHEME_SVR_DETAIL_TYPE_SING_A_ROUND = 10;    // 接歌抢唱玩法
  SCHEME_SVR_DETAIL_TYPE_KTV = 11;             //ktv玩法
  SCHEME_SVR_DETAIL_TYPE_ROLE_PLAY = 12;     //角色扮演玩法
  SCHEME_SVR_DETAIL_TYPE_LISTENING = 13;  //挂房听歌玩法
  SCHEME_SVR_DETAIL_TYPE_MULTI_BATTLE = 14; //团战玩法
  SCHEME_SVR_DETAIL_TYPE_MUSIC_NEST = 15;   //乐窝玩法
  SCHEME_SVR_DETAIL_TYPE_RAP = 16;         //rap玩法
  SCHEME_SVR_DETAIL_TYPE_PGC_WEREWOLVES = 17;  //PGC狼人杀玩法
  SCHEME_SVR_DETAIL_TYPE_PIA_XI = 18;       //pia戏玩法
  SCHEME_SVR_DETAIL_TYPE_WEREWOLVES_GAME = 19;  //9麦狼人杀玩法
  SCHEME_SVR_DETAIL_TYPE_MULTI_MIC_MINI_GAME = 20; //小游戏21麦玩法
  SCHEME_SVR_DETAIL_TYPE_MUSIC_CONCERT = 21; // 乐队玩法
  SCHEME_SVR_DETAIL_TYPE_PIA_V2 = 22; //pia戏 v2版本玩法
  SCHEME_SVR_DETAIL_TYPE_ESCAPE_GAME = 23;  //迷境玩法
  SCHEME_SVR_DETAIL_TYPE_GAME_RACE = 24;    //赛事玩法
  SCHEME_SVR_DETAIL_TYPE_OFFICIAL_CHANNEL = 25 ;   //官频布局模式
  //SCHEME_SVR_DETAIL_TYPE_PERFECT_COUPLE_GAME = 26; //天配玩法26，已废弃，这里保留占位(6.21.0客户端带上了未完成的玩法, 后续也不能使用26这个数值)
  SCHEME_SVR_DETAIL_TYPE_PERFECT_COUPLE_GAME = 27; //天配玩法27 (6.26.0天配上线，使用该枚举数值)
  SCHEME_SVR_DETAIL_TYPE_OFFER_CHANNEL = 28;   //拍卖房玩法(公会公开房里的玩法)
  SCHEME_SVR_DETAIL_TYPE_COMMUNITY_CHAT = 29; // 社群聊天室玩法
  SCHEME_SVR_DETAIL_TYPE_COMMUNITY_EXPAND_SOCIAL_CHAT = 30; // 社群扩列聊天玩法
}

//废弃
enum SchemeChannelCategory {
  CHANNEL_TYPE_UNKNOWN = 0;
  CHANNEL_TYPE_USER = 1;
  CHANNEL_TYPE_PUBLIC_GUILD = 2;
  CHANNEL_TYPE_PGC_OTHER = 3;  //cpl,直播，管频，公会开黑等
  CHANNEL_TYPE_COMMUNITY = 4;  //社群房
}