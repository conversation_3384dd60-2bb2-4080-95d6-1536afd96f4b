syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-scheme-conf-mgr";
package channel_scheme_conf_mgr;

service ChannelSchemeConfExternal{
  //跟随外显
  rpc ModifyFollowDisplayConf(ModifyFollowDisplayConfReq) returns (ModifyFollowDisplayConfResp) {
  }
  rpc BatGetModifyFollowDisplayConf(BatGetModifyFollowDisplayConfReq) returns (BatGetModifyFollowDisplayConfResp) {
  }

  //游戏绑定
  rpc ModifyGameBindConf(ModifyGameBindConfReq) returns (ModifyGameBindConfResp) {
  }

  //pgc房列表

}

message ModifyFollowDisplayConfReq {
  FollowDisplayConf conf = 1;
}
message ModifyFollowDisplayConfResp {
}

message BatGetModifyFollowDisplayConfReq {
  repeated uint32 scheme_id_list = 1;
}
message BatGetModifyFollowDisplayConfResp {
  repeated FollowDisplayConf conf_list = 1;
}

message ModifyGameBindConfReq{
}
message ModifyGameBindConfResp{
}

//跟随外显
message FollowDisplayConf {
  uint32 scheme_id = 1;
  string scheme_name = 2;
  string follow_label_img = 3; // 跟随好友头像标头jpg图
  string follow_label_text = 4; // 跟随好友头像标头文案
}

//搜索外显

//推荐,快速匹配相关
message QuickMatchConf{
  uint32 scheme_id = 1;
  string scheme_name = 2;
  //个人房还是临时房
}

//配图相关: 切换玩法配图，最近在玩配图等

//游戏绑定相关
message GameBindConf{
  uint32 scheme_id = 1;
  string scheme_name = 2;
  string bind_game_name = 3;
  uint32 bind_game_card_id = 4;
  uint32 u_game_id = 5;
  bool is_minority_game = 6;   //是否小众游戏
}

//临时房

//首页展示

//房间发布

//viewtype等

//创建，发布时的房间名列表配置
