syntax = "proto3";

package channel_wedding_plan;
import "tt/quicksilver/extension/options/options.proto";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";
import "tt/quicksilver/channel-operate-permission-checker/channel-operate-permission-checker.proto";

option go_package = "golang.52tt.com/protocol/services/channel-wedding-plan";

service ChannelWeddingPlan {
  option (service.options.service_ext) = {
    service_name: "channel-wedding-plan"
  };
  // 购买婚礼
  rpc BuyWedding (BuyWeddingRequest) returns (BuyWeddingResponse) {}
  // 取消婚礼
  rpc CancelWedding (CancelWeddingRequest) returns (CancelWeddingResponse) {}
  // 获取我的婚礼信息
  rpc GetMyWeddingInfo (GetMyWeddingInfoRequest) returns (GetMyWeddingInfoResponse) {}
  // 通过id获取我的婚礼信息
  rpc GetMyWeddingInfoById (GetMyWeddingInfoByIdRequest) returns (GetMyWeddingInfoByIdResponse) {}
  // 获取所有主题配置
  rpc GetAllThemeCfg (GetAllThemeCfgRequest) returns (GetAllThemeCfgResponse) {}
  // 添加主题配置
  rpc AddThemeCfg (AddThemeCfgRequest) returns (AddThemeCfgResponse) {}
  // 更新主题配置
  rpc UpdateThemeCfg (UpdateThemeCfgRequest) returns (UpdateThemeCfgResponse) {}
  // 删除主题配置
  rpc DeleteThemeCfg (DeleteThemeCfgRequest) returns (DeleteThemeCfgResponse) {}
  // 根据themeId获取Cfg
  rpc GetThemeCfgById (GetThemeCfgByIdRequest) returns (GetThemeCfgByIdResponse) {}


  // 获取我的预约信息
  rpc GetMyWeddingReserveInfo (GetMyWeddingReserveInfoRequest) returns (GetMyWeddingReserveInfoResponse) {}
  // 获取选中日期/房间的预约信息
  rpc GetWeddingReserveInfo (GetWeddingReserveInfoRequest) returns (GetWeddingReserveInfoResponse) {}
  // 保存预约信息
  rpc SaveWeddingReserveInfo (SaveWeddingReserveRequest) returns (SaveWeddingReserveInfoResponse) {}
  // 修改预约信息
  rpc ChangeWeddingReserveInfo (ChangeWeddingReserveRequest) returns (ChangeWeddingReserveInfoResponse) {}
  // 获取伴郎伴娘信息
  rpc GetGroomsmanAndBridesmaidInfo (GetGroomsmanAndBridesmaidInfoRequest) returns (GetGroomsmanAndBridesmaidInfoResponse) {}
  // 获取亲友团信息
  rpc GetWeddingFriendInfo (GetWeddingFriendInfoRequest) returns (GetWeddingFriendInfoResponse) {}
  // 邀请嘉宾
  rpc InviteWeddingGuest (InviteWeddingGuestRequest) returns (InviteWeddingGuestResponse) {}
  // 处理婚礼邀请
  rpc HandleWeddingInvite (HandleWeddingInviteRequest) returns (HandleWeddingInviteResponse) {}
  // 删除嘉宾
  rpc DelWeddingGuest (DelWeddingGuestRequest) returns (DelWeddingGuestResponse) {}
  // 查询预约列表
  rpc GetReservedWedding (GetReservedWeddingRequest) returns (GetReservedWeddingResponse) {}
  // 设置主持人
  rpc SetWeddingHost (SetWeddingHostRequest) returns (SetWeddingHostResponse) {}
  // 获取婚礼基础信息
  rpc GetWeddingPlanBaseInfo (GetWeddingPlanBaseInfoRequest) returns (GetWeddingPlanBaseInfoResponse) {}
  // 获取简单婚礼信息
  rpc GetSimpleWeddingPlanInfo (GetSimpleWeddingPlanInfoRequest) returns (GetSimpleWeddingPlanInfoResponse) {}
  // 获取求婚函信息
  rpc GetWeddingInviteInfo (GetWeddingInviteInfoRequest) returns (GetWeddingInviteInfoResponse) {}

  // 批量获取婚礼信息
  rpc BatGetWeddingInfoById (BatGetWeddingInfoByIdRequest) returns (BatGetWeddingInfoByIdResponse) {}
  // 分页获取未开始的婚礼列表
  rpc PageGetComingWeddingList (PageGetComingWeddingListRequest) returns (PageGetComingWeddingListResponse) {}
  // 订阅婚礼
  rpc SubscribeWedding (SubscribeWeddingRequest) returns (SubscribeWeddingResponse) {}
  // 批量获取婚礼订阅状态
  rpc BatGetWeddingSubscribeStatus (BatGetWeddingSubscribeStatusRequest) returns (BatGetWeddingSubscribeStatusResponse) {}
  // 获取婚礼大屏
  rpc GetWeddingBigScreen (GetWeddingBigScreenRequest) returns (GetWeddingBigScreenResponse) {}
  // 保存婚礼大屏
  rpc SaveWeddingBigScreen (SaveWeddingBigScreenRequest) returns (SaveWeddingBigScreenResponse) {}
  // 更新婚礼大屏审核状态
  rpc UpdateWeddingBigScreenReviewStatus (UpdateWeddingBigScreenReviewStatusRequest) returns (UpdateWeddingBigScreenReviewStatusResponse) {}
  // 申请结束婚礼关系
  rpc ApplyEndWeddingRelation (ApplyEndWeddingRelationRequest) returns (ApplyEndWeddingRelationResponse) {}
  // 取消结束婚礼关系
  rpc CancelEndWeddingRelation (CancelEndWeddingRelationRequest) returns (CancelEndWeddingRelationResponse) {}

  //获取求婚列表
  rpc GetProposeList (GetProposeListRequest) returns (GetProposeListResponse) {}
  //发送求婚
  rpc SendPropose (SendProposeRequest) returns (SendProposeResponse) {}
  // RevokePropose 撤销求婚
  rpc RevokePropose (RevokeProposeRequest) returns (RevokeProposeResponse) {}
  //处理求婚请求
  rpc HandlePropose (HandleProposeRequest) returns (HandleProposeResponse) {}
  //获取求婚函信息
  rpc GetProposeById (GetProposeByIdRequest) returns (GetProposeByIdResponse) {}
  //获取我发出的求婚函
  rpc GetSendPropose (GetSendProposeRequest) returns (GetSendProposeResponse) {}

  //批量获取结婚状态
  rpc BatchGetMarriageInfo (BatchGetMarriageInfoRequest) returns (BatchGetMarriageInfoResponse) {}

  // 设置用户关系隐藏开关状态
  rpc SetUserRelationHideStatus (SetUserRelationHideStatusRequest) returns (SetUserRelationHideStatusResponse) {}

  // 离婚
  rpc Divorce (DivorceRequest) returns (DivorceResponse) {}

  // 获取离婚状态
  rpc GetUserDivorceStatus (GetUserDivorceStatusRequest) returns (GetUserDivorceStatusResponse) {}

  // 获取婚姻关系状态
  rpc GetMarriageStatus (GetMarriageStatusRequest) returns (GetMarriageStatusResponse) {}

  // 手动通知婚礼开始
  rpc ManualNotifyWeddingStart (ManualNotifyWeddingStartRequest) returns (ManualNotifyWeddingStartResponse) {}
  // 更新婚礼计划状态
  rpc UpdateWeddingPlanStatus (UpdateWeddingPlanStatusRequest) returns (UpdateWeddingPlanStatusResponse) {}

  // 手动测试婚礼纪念弹窗
  rpc TestWeddingAnniversaryPopup (TestWeddingAnniversaryPopupRequest) returns (TestWeddingAnniversaryPopupResponse) {}

  // 获取婚礼角色
  rpc GetMyWeddingRole (GetMyWeddingRoleRequest) returns (GetMyWeddingRoleResponse) {}

  //  批量获取婚礼角色
  rpc BatchGetWeddingRole (BatchGetWeddingRoleRequest) returns (BatchGetWeddingRoleResponse) {}

  // 获取房间内预约信息
  rpc GetChannelReservedInfo (GetChannelReservedInfoRequest) returns (GetChannelReservedInfoResponse) {}

  // 咨询预约婚礼
  rpc ConsultWeddingReserve (ConsultWeddingReserveRequest) returns (ConsultWeddingReserveResponse) {}

  // 客服安排婚礼预约
  rpc ArrangeWeddingReserve (ArrangeWeddingReserveRequest) returns (ArrangeWeddingReserveResponse) {}

  // 获取热门婚礼
  rpc BatchHotWeddingPlan (BatchHotWeddingPlanRequest) returns (BatchHotWeddingPlanResponse) {}

  //
  rpc GetTodayAllComingWeddingList(GetTodayAllComingWeddingListRequest) returns (GetTodayAllComingWeddingListResponse) {}

  // T豆对账
  rpc GetTBeanTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetTBeanOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  rpc AdminCancelWedding(AdminCancelWeddingRequest) returns (AdminCancelWeddingResponse) {}

  // 获取公会后台房间预约时段配置
  rpc GetChannelReserveTimeSectionConf(GetChannelReserveTimeSectionConfRequest) returns (GetChannelReserveTimeSectionConfResponse) {}

  // 设置公会后台房间预约时段开关
  rpc SetAdminChannelReserveTimeSectionSwitch(SetAdminChannelReserveTimeSectionSwitchRequest) returns (SetAdminChannelReserveTimeSectionSwitchResponse) {}

  // 获取婚礼前置流程信息
  rpc GetWeddingPreProcessInfo(GetWeddingPreProgressInfoRequest) returns (GetWeddingPreProgressInfoResponse) {}

  // 开始婚礼
  rpc StartWedding(StartWeddingRequest) returns (StartWeddingResponse) {}

  // 取消婚礼
  rpc CancelPreparedWedding (CancelPreparedWeddingRequest) returns (CancelPreparedWeddingResponse) {}

  // 麦位检查
  rpc CheckOperatePermission (channel_operate_permission_checker.CheckOperatePermissionReq) returns (channel_operate_permission_checker.CheckOperatePermissionResp) {}

  // 获取婚礼前新人麦位上的人
  rpc GetNewComersMicUser (GetNewComersMicUserRequest) returns (GetNewComersMicUserResponse) {}

  // 获取付费婚礼房
  rpc GetPayWeddingChannelList (GetPayWeddingChannelListRequest) returns (GetPayWeddingChannelListResponse) {}
}


message BuyWeddingRequest {
  uint32 theme_id = 1; // 主题id
  uint32 buyer_uid = 2; // 购买者uid
  BuyReserveInfo buy_reserve_info = 3; // 购买预约信息
  uint32 source_msg_id = 4; // 从im过来的消息id
}

message BuyReserveInfo {
  uint32 reserve_date = 1; // 预约日期
  uint32 channel_id = 2; // 房间ID
  repeated string reserve_time = 3; // 预约时段, 连续
  bool is_hot = 4; // 是否热门
  uint32 gift_id = 5; // 礼物id
}

message BuyWeddingResponse {
  uint32 wedding_plan_id = 1; // 婚礼计划id
}

message GetMyWeddingReserveInfoRequest {
  uint32 wedding_plan_id = 1;
}

message GetMyWeddingReserveInfoResponse {
  uint32 reserve_date = 1; // 预约日期
  uint32 channel_id = 2; // 房间ID
  repeated string reserve_time_section = 3; // 预约时段
  uint32 remain_change_times = 4; // 剩余修改次数
  uint32 change_limit_time = 5; // 修改限制时间, 婚礼开始前x小时不能修改预约
  bool   in_change_time = 6; // 是否在可修改时间内
  uint32 reserve_start_time = 7; // 预约开始时间
  uint32 reserve_end_time = 8; // 预约结束时间
}

message ChannelInfo {
  uint32 channel_id = 1; // 房间ID
  uint32 manager_uid = 2; // 管理员uid
}

enum ThemeType {
  UNKNOWN = 0;
  FREE = 1; // 免费
  PAY = 2; // 付费
}

message GetWeddingReserveInfoRequest {
  uint32 theme_type = 1; // 主题类型 see ThemeType
  uint32 reserve_date = 2; // 预约日期
  uint32 channel_id = 3; // 预约渠道
}

message GetWeddingReserveInfoResponse {
  uint32 cur_channel_id = 1; // 当前房间ID
  uint32 cur_reserve_date = 2; // 当前预约日期
  repeated ChannelInfo channel_info = 3; // 预约渠道
  repeated ReserveTimeInfo reserve_time_info = 4; // 预约时段信息
  uint32 min_reserve_date = 5; // 最小预约日期
  uint32 max_reserve_date = 6; // 最大预约日期
}

message SaveWeddingReserveRequest {
  uint32 wedding_plan_id = 1; // 婚礼计划ID
  uint32 reserve_date = 2; // 预约日期
  uint32 channel_id = 3; // 房间ID
  repeated string reserve_time = 4; // 预约时段, 连续
}

message SaveWeddingReserveInfoResponse {}

message ChangeWeddingReserveRequest {
  uint32 wedding_plan_id = 1; // 婚礼计划ID
  uint32 reserve_date = 2; // 预约日期
  uint32 channel_id = 3; // 房间ID
  repeated string reserve_time = 4; // 预约时段, 连续
}

message ChangeWeddingReserveInfoResponse {}

message WeddingGuestInfo {
  uint32 uid = 1; // 用户ID
  uint32 invite_uid = 2; // 邀请用户ID
  uint32 create_time = 3; // 创建时间
}

// 获取伴郎伴娘信息 url: /tt-revenue-http-logic/channel_wedding/get_groomsman_and_bridesmaid_info
message GetGroomsmanAndBridesmaidInfoRequest {
  uint32 wedding_plan_id = 1; // 婚礼方案ID
}

message GetGroomsmanAndBridesmaidInfoResponse {
  repeated WeddingGuestInfo bridesmaid_list = 1; // 伴娘列表
  repeated WeddingGuestInfo groomsman_list = 2; // 伴郎列表
  repeated WeddingGuestInfo invited_list = 3; // 已邀请列表
  repeated WeddingGuestInfo agreed_list = 4; // 已接受列表
  repeated WeddingGuestInfo refused_list = 5; // 已拒绝列表
}

// 获取亲友团信息 url: /tt-revenue-http-logic/channel_wedding/get_wedding_friend_info
message GetWeddingFriendInfoRequest {
  uint32 wedding_plan_id = 1; // 婚礼方案ID
}

message GetWeddingFriendInfoResponse {
  repeated WeddingGuestInfo friend_list = 1; // 亲友团列表
  repeated WeddingGuestInfo invited_list = 2; // 已邀请列表
  repeated WeddingGuestInfo agreed_list = 3; // 已接受列表
  repeated WeddingGuestInfo refused_list = 4; // 已拒绝列表
  uint32 max_friend_num = 5;                  // 最大亲友团人数
}

// 邀请嘉宾 url: /tt-revenue-http-logic/channel_wedding/invite_wedding_guest
message InviteWeddingGuestRequest {
  uint32 wedding_plan_id = 1; // 婚礼计划ID
  uint32 wedding_guest_type = 2; // 嘉宾类型, see WeddingGuestType
  uint32 uid = 3; // 用户ID
  bool is_cancel = 4; // 是否取消邀请
}

message InviteWeddingGuestResponse {}

enum WeddingInviteStatus {
  WEDDING_INVITE_STATUS_UNKNOWN = 0; // 未知
  WEDDING_INVITE_STATUS_INVITED = 1; // 已邀请
  WEDDING_INVITE_STATUS_ACCEPTED = 2; // 已接受
  WEDDING_INVITE_STATUS_REFUSED = 3; // 已拒绝
}

message HandleWeddingInviteRequest {
  uint32 invite_id = 1; // 邀请ID
  uint32 handle_status = 2; // 处理状态, 2: 接受, 3: 拒绝 see WeddingInviteStatus
  uint32 uid = 3; // 用户ID
}

message HandleWeddingInviteResponse {}


// 删除嘉宾 url: /tt-revenue-http-logic/channel_wedding/del_wedding_guest
message DelWeddingGuestRequest {
  uint32 wedding_plan_id = 1; // 婚礼计划ID
  uint32 wedding_guest_type = 2; // 嘉宾类型, see WeddingGuestType
  uint32 uid = 3; // 用户ID
}

message DelWeddingGuestResponse {}

// 预约时段信息
message ReserveTimeInfo {
  string reserve_time_section = 2; // 预约时段
  bool is_fully_reserved = 3; // 是否已满
  uint32 groom_uid = 4; // 新郎uid
  uint32 bride_uid = 5; // 新娘uid
  bool is_hot = 6; // 是否热门
}

message GetAllThemeCfgRequest {
}

message WeddingDressPreview {
  uint32 level = 1;  // 等级 see WeddingLevelType
  uint32 guess_type = 2;  // 猜测类型, see WeddingGuestType
  WeddingAnimationInfo wedding_dress = 3;
  string dress_text = 4; // 服装文案
}

message WeddingDressPreviewList {
  // 初中高三个等级资源, 免费只有一个
  repeated WeddingDressPreview dress_preview_list = 1;
}

message FinishWeddingAward {
  WeddingAnimationInfo award_animation = 1;  // 动画资源
  string top_text = 2; // 顶部文案
  string bottom_text = 3; // 底部文案
}

message WeddingPriceInfo {
  uint32 price = 1;  // 价格
  uint32 price_type = 2;  // 价格类型, see channel_wedding_logic.proto WeddingPriceType
}

message ThemeCfg {
  uint32 theme_id = 1;
  string theme_name = 2;
  WeddingPriceInfo price_info = 3;
  repeated WeddingScenePreview scene_preview_list = 4;
  map<uint32, WeddingDressPreviewList> dress_preview_map = 5;  // 等级服装预览,  key: see WeddingGuestType
  uint32 max_show_groomsman_num = 6;  // 最大展示 伴郎（娘）人数
  uint32 max_show_family_num = 7;  // 最大展示 亲友团人数
  string selected_icon = 8;  // 选中图标
  string unselected_icon = 9;  // 未选中图标
  // 礼成奖励
  repeated FinishWeddingAward reward_info_list = 10;
  uint32 max_big_screen_num = 11; // 最大展示大屏幕图片数量
  string theme_background = 12;  // 背景图片
  string theme_preview_text = 13;  // 左上角固定文案
}


message AddThemeCfgRequest {
  ThemeCfg theme_cfg = 1; // theme_id为0
}

message AddThemeCfgResponse {
}

message UpdateThemeCfgRequest {
  ThemeCfg theme_cfg = 1;
}

message UpdateThemeCfgResponse {
}

message DeleteThemeCfgRequest {
  uint32 theme_id = 1;
}

message DeleteThemeCfgResponse {
}

// 动画资源
message WeddingAnimationInfo {
  uint32 animation_type = 1;  // 动画类型, see WeddingAnimationType
  string animation_resource = 2;  // 动画资源
  string animation_resource_md5 = 3;
  string animation_png = 4;  // 动画png资源
}


message WeddingScenePreview {
  uint32 level = 1;  // 等级 see WeddingLevelType
  WeddingAnimationInfo scene_animation = 2;  // 场景动画
  string small_icon = 3;  // 小图标
  WeddingAnimationInfo zoom_animation = 4; // 放大动画
}

message GetAllThemeCfgResponse {
  repeated ThemeCfg theme_cfg = 1;
}

message CancelWeddingRequest {
  uint32 uid = 1;
  uint32 wedding_plan_id = 2; // 婚礼计划id
}

message CancelWeddingResponse {
}

message AdminCancelWeddingRequest {
  uint32 wedding_plan_id = 1;
}

message AdminCancelWeddingResponse {
}

message GetMyWeddingInfoRequest {
  uint32 uid = 1;
}

message PartnerInfo {
  uint32 uid = 1;
  bool is_accept = 2; // 是否接受
  uint32 together_time = 3; // 在一起时间
}

message GetMyWeddingInfoResponse {
  PartnerInfo partner_info = 1; // 如果为空，说明还没邀请
  uint32 wedding_plan_id = 2; // 婚礼计划id， 购买后返回
  uint32 theme_id = 3;
  uint32 buyer_uid = 4;
  bool is_channel_buy = 5;
}

message GetMyWeddingInfoByIdRequest {
  uint32 uid = 1;
  uint32 wedding_plan_id = 2;
}

message GetMyWeddingInfoByIdResponse {
  PartnerInfo partner_info = 1; // 如果为空，说明还没邀请
  uint32 wedding_plan_id = 2; // 婚礼计划id， 购买后返回
  uint32 theme_id = 3;
  uint32 buyer_uid = 4;
  bool is_channel_buy = 5;
}

enum WeddingPlanStatus {
  WEDDING_PLAN_STATUS_INIT = 0; // 未开始
  WEDDING_PLAN_STATUS_PLAYING = 1; // 进行中
  WEDDING_PLAN_STATUS_FINISH = 2; // 已结束
  WEDDING_PLAN_STATUS_CANCEL = 3; // 已取消
}

message WeddingPlanInfo {
  uint32 wedding_plan_id = 1;
  uint32 groom_uid = 2;
  uint32 bride_uid = 3;
  uint32 theme_id = 4;
  bool is_theme_free = 5;
  WeddingPlanReserveInfo reserve_info = 6;
  repeated uint32 groomsman_uid_list = 7;
  repeated uint32 bridesmaid_uid_list = 8;
  uint32 host_uid = 9;
  repeated BigScreenItem big_screen_list = 10;
  uint32 status = 11; // see WeddingPlanStatus
  bool is_hot = 12; // 是否热门
}

message WeddingPlanReserveInfo {
  uint32 channel_id = 1;
  uint32 start_ts = 2;
  uint32 end_ts = 3;
}

message BatGetWeddingInfoByIdRequest {
  repeated uint32 wedding_plan_id_list = 1;
}

message BatGetWeddingInfoByIdResponse {
  map<uint32, WeddingPlanInfo> wedding_info_map = 1;
}

message GetTodayAllComingWeddingListRequest {
}

message  GetTodayAllComingWeddingListResponse {
  repeated WeddingPlanInfo wedding_list = 1;
}


message PageGetComingWeddingListRequest {
  uint32 page_num = 1;
  uint32 page_size = 2;
  int64 now_ts = 3;
}

message PageGetComingWeddingListResponse {
  repeated WeddingPlanInfo wedding_list = 1;
  bool has_more = 2;
}

message SubscribeWeddingRequest {
  uint32 uid = 1;
  uint32 wedding_plan_id = 2;
}

message SubscribeWeddingResponse {
}

message BatGetWeddingSubscribeStatusRequest {
  uint32 uid = 1;
  repeated uint32 wedding_plan_id_list = 2;
}

message BatGetWeddingSubscribeStatusResponse {
  map<uint32, bool> subscribe_status_map = 1; // key: wedding_plan_id, value: 是否订阅wedding_plan_id
}

enum ReviewStatus {
  REVIEW_STATUS_REVIEWING = 0; // 审核中
  REVIEW_STATUS_PASS = 1; // 通过
  REVIEW_STATUS_REJECT = 2; // 拒绝
}

message BigScreenItem {
  string img_url = 1;
  ReviewStatus review_status = 2;
  uint32 upload_by_uid = 3;
}

message GetWeddingBigScreenRequest {
  uint32 wedding_plan_id = 1;
}

message GetWeddingBigScreenResponse {
  repeated BigScreenItem big_screen_list = 1;
}

message SaveWeddingBigScreenRequest {
  uint32 wedding_plan_id = 1;
  repeated BigScreenItem big_screen_list = 2 [deprecated = true];
  string img_url = 3;
  bool is_del = 4;
  uint32 uid = 5;
}

message SaveWeddingBigScreenResponse {
}

message UpdateWeddingBigScreenReviewStatusRequest {
  uint32 wedding_plan_id = 1;
  string img_url = 2;
  ReviewStatus review_status = 3;
  uint32 uid = 4;
}

message UpdateWeddingBigScreenReviewStatusResponse {
}

message ApplyEndWeddingRelationRequest {
  uint32 uid = 1;
  uint32 partner_uid = 2;
  uint32 source = 3; // 申请来源
}

message ApplyEndWeddingRelationResponse {
  int64 end_relationship_deadline = 1;  // 结束关系截止时间戳,单位秒
}

message CancelEndWeddingRelationRequest {
  uint32 uid = 1;
  uint32 partner_uid = 2;
  uint32 source = 3; // 申请来源
}

message CancelEndWeddingRelationResponse {
}

enum WeddingReserveDataType {
  WEDDING_RESERVE_DATA_TYPE_ALL = 0; // 全部
  WEDDING_RESERVE_DATA_TYPE_NOT_START = 1; // 未开始
  WEDDING_RESERVE_DATA_TYPE_END = 2; // 已结束
}

message GetReservedWeddingRequest {
  repeated uint32 channel_id = 1; // 房间ID
  uint32 page_num = 2; // 页码
  uint32 page_size = 3; // 每页数量
  uint32 start_time = 4; // 开始时间
  uint32 end_time = 5; // 结束时间
  uint32 data_type = 6; // 数据类型 0:全部 1:未开始 2:已结束
}

message GetReservedWeddingResponse {
  repeated ReservedWeddingInfo reserved_wedding_list = 1; // 预约婚礼列表
  uint32 total = 2;
}

message ReservedWeddingInfo {
  uint32 wedding_plan_id = 1; // 婚礼计划ID
  uint32 create_time = 2; // 创建时间
  uint32 channel_id = 3; // 房间ID
  uint32 reserve_start_time = 4; // 预约开始时间
  uint32 reserve_end_time = 5; // 预约结束时间
  uint32 groom_uid = 6; // 新郎ID
  uint32 bride_uid = 7; // 新娘ID
  uint32 host_uid = 8; // 主持人ID
  string theme_name = 9; // 主题名称
  uint32 buyer_uid = 10; // 购买者ID
  uint32 channel_manager_uid = 11;
  uint32 buy_price = 12; // 购买t豆价格
  bool   is_hot = 13;    // 是否热门
  bool   is_admin_cancel = 14; // 是否是管理员取消
}

message SetWeddingHostRequest {
  uint32 wedding_plan_id = 1; // 婚礼计划ID
  uint32 host_uid = 2; // 主持人ID
  uint32 operator_uid = 3; // 操作者ID
  uint32 channel_id = 4; // 房间ID
}

message SetWeddingHostResponse{}

// 获取基础信息 url: /tt-revenue-http-logic/channel_wedding/get_wedding_plan_base_info
message GetWeddingPlanBaseInfoRequest {}

message GetWeddingPlanBaseInfoResponse {
  bool has_mate = 1; // 有无对象
  bool is_pay = 2; // 是否付费
  bool is_reserve = 3; // 是否预约
}

message GetThemeCfgByIdRequest {
  uint32 theme_id = 1;
}

message GetThemeCfgByIdResponse {
  ThemeCfg theme_cfg = 1;
}

//获取求婚列表请求
message GetProposeListRequest {
  repeated uint32 target_uid_list = 1; //用户id列表
}

// 求婚对象信息
message ProposeUser {
  uint32 target_uid = 1;           //  用户信息
  uint32 status = 2;              // see ProposeStatus
  uint32 fellow_point = 3;      // 挚友值
  string propose_id = 4;         //求婚邀请函id
}

//获取求婚列表响应
message GetProposeListResponse {
  repeated ProposeUser propose_list = 1; // 求婚列表
  repeated string tips = 2;     //tips
}

message SendProposeRequest {
  uint32 target_uid = 1;  // 用户ID
  string tips = 2;        //tips
  uint32 from_uid = 3;  // 发起用户ID
}

message SendProposeResponse {
  string propose_id = 1; //求婚邀请函id
}

//处理求婚请求
message HandleProposeRequest{
  string propose_id = 1;  //求婚邀请函id
  bool is_accept = 2;    // 是否接受求婚
  uint32 handle_uid = 3;  // 处理用户ID
}

message HandleProposeResponse{
}


// 求婚函信息
message WeddingProposeInfo {
  string propose_id = 1;            //求婚邀请函id
  uint32 from_user = 2;             //  用户信息
  uint32 target_user = 3;          //  对方用户信息
  uint32 status = 4;               // 求婚函当前状态，req不用填 see ProposeStatus
  uint32 create_time = 5;          // 求婚邀请函发送时间
  uint32 end_time = 6;             // 求婚邀请的到期时间
  uint32 expire_day = 7;           // 过期天数
  string tips = 8;                 //tips
}

message GetProposeByIdRequest{
  string propose_id = 1; //求婚id
}

message GetProposeByIdResponse{
  WeddingProposeInfo propose = 2; //求婚函信息
}

//我发出的求婚函
message GetSendProposeRequest{
  uint32 from_uid = 1; //发起用户ID
}

message GetSendProposeResponse{
  WeddingProposeInfo propose = 1;
}

message BatchGetMarriageInfoRequest {
  repeated uint32 uid_list = 1;
}

message BatchGetMarriageInfoResponse {
  map<uint32, uint32> marriage_info_map = 1; // key: uid, value: 结婚状态
}

message GetSimpleWeddingPlanInfoRequest {
  uint32 wedding_plan_id = 1;
}

message GetSimpleWeddingPlanInfoResponse {
  uint32 groom_uid = 1; // 新郎uid
  uint32 bride_uid = 2; // 新娘uid
  repeated uint32 groomsman_list = 3; // 伴郎列表
  repeated uint32 bridesmaid_list = 4; // 伴娘列表
  repeated string big_screen_list = 5; // 大屏幕图片列表
  uint32 reserve_start_time = 6; // 预约开始时间
  uint32 reserve_end_time = 7; // 预约结束时间
  bool is_hot = 8; // 是否热门
  uint32 buyer_uid = 9; // 购买者uid
  uint32 gift_id = 10; // 礼物id
  uint32 theme_id = 11; // 婚礼主题id
}

message BatchHotWeddingPlanRequest {
  repeated uint32 wedding_plan_id = 1;
  bool with_cache = 2; // 是否使用缓存
}

message BatchHotWeddingPlanResponse {
  map<uint32, bool> hot_wedding_map = 1; // key: wedding_plan_id, value: 是否热门
}


message InviteCard {
  uint32 invite_id = 2; // 邀请ID
  uint32 wedding_plan_id = 3; // 婚礼ID
  uint32 theme_id = 4; // 婚礼主题
  uint32 groom_uid = 5; // 新郎uid
  uint32 bride_uid = 6; // 新娘uid
  uint32 wedding_datetime = 7; // 婚礼日期时间
  uint32 wedding_channel_id = 8; // 婚礼房间
  repeated WeddingGuestGift gift_list = 9; // 礼物列表
  string invite_card_bg = 10; // 邀请卡片背景
  uint32 wedding_invite_type = 11 ; // 邀请类型, see WeddingInviteType
}

message WeddingGuestGift {
  string gift_url = 1; // 礼物url
  string gift_desc = 2; // 礼物描述
}

// 获取邀请信息(点击im)
message GetWeddingInviteInfoRequest {
  uint32 wedding_plan_id = 1; // 婚礼id
  uint32 uid = 2; // 用户uid
  uint32 wedding_invite_type = 3; // 邀请类型, see WeddingInviteType
  uint32 invite_id = 4; // 邀请ID
}

message GetWeddingInviteInfoResponse {
  InviteCard invite_card = 1; // 邀请卡片
  uint32 invite_status = 2; // 邀请状态, see WeddingInviteStatus
}

enum HideOpType {
  HIDE_OP_TYPE_UNEXPECTED = 0; // 未知
  HIDE_OP_TYPE_HIDE = 1; // 隐藏
  HIDE_OP_TYPE_SHOW = 2; // 显示
}

message SetUserRelationHideStatusRequest{
  uint32 uid = 1;
  uint32 op_type = 2; // see HideOpType
}

message SetUserRelationHideStatusResponse{
  uint32 hide_status = 1; // see HideOpType
}

message DivorceRequest {
  uint32 uid = 1;
  uint32 partner_uid = 2;
}

message DivorceResponse {
}

message GetUserDivorceStatusRequest {
  uint32 uid = 1;
}

message GetUserDivorceStatusResponse {
  int64 divorce_deadline = 1; // 离婚截止时间, 0表示没有离婚
  int64 auto_divorce_day = 2; // 自动离婚时长, 单位天
}

message MarriageRelationInfo {
  uint32 uid = 1; // 用户ID
  uint32 partner_uid = 2; // 伴侣ID
  int64 relation_ctime = 3; // 关系创建时间
}

message GetMarriageStatusRequest {
  uint32 uid = 1;
}

message GetMarriageStatusResponse {
  MarriageRelationInfo relation_info = 1;
  int64 divorce_deadline = 2; // 离婚截止时间, 0表示没有离婚
  int64 auto_divorce_day = 3; // 自动离婚时长, 单位天
  bool is_hide = 4; // 是否隐藏关系
}

message ManualNotifyWeddingStartRequest {
  uint32 wedding_plan_id = 1;
}

message ManualNotifyWeddingStartResponse {}

message UpdateWeddingPlanStatusRequest {
  uint32 wedding_plan_id = 1;
  bool is_force = 2; // 是否强制更新
}

message UpdateWeddingPlanStatusResponse {}

message TestWeddingAnniversaryPopupRequest {
  int64 target_time = 1; // 目标时间
  enum OpType {
    OP_TYPE_UNEXPECTED = 0; // 未知
    OP_TYPE_GEN_POPUP = 1; // gen 目标时间的弹窗信息
    OP_TYPE_ONLINE_EVENT = 2; // 模拟用户当天上线操作
    OP_TYPE_PUSH = 3;       // 手动推送纪念日弹窗, 仅测试用
  }
  OpType op_type = 2; // 操作类型
  uint32 uid = 3;
  uint32 day = 4;
}

message TestWeddingAnniversaryPopupResponse {}

message GetMyWeddingRoleRequest {
  uint32 uid = 1; // 用户ID
}

message GetMyWeddingRoleResponse {
  uint32 wedding_role = 1; // 婚礼角色, see channel_wedding_logic.proto WeddingGuestType
  bool in_change_time = 2; // 是否在可修改时间内
}

message BatchGetWeddingRoleRequest {
  repeated uint32 uid_list = 1;
  uint32 plan_id = 2;
}

message BatchGetWeddingRoleResponse {
  map<uint32, uint32> wedding_role_map = 1; // key: uid, value: see channel_wedding_logic.proto WeddingGuestType
}

// 获取可预约信息 url: /tt-revenue-http-logic/channel_wedding/get_channel_reserved_info
message GetChannelReservedInfoRequest {
  uint32 reserve_date = 1; // 预约日期
  uint32 channel_id = 2; // 房间ID
  uint32 theme_type = 3; // 主题类型 1.免费 2.付费
}

message GetChannelReservedInfoResponse {
  repeated ChannelReserveTimeInfo reserve_time_info_list = 1; // 预约时段信息
  uint32 min_reserve_date = 2; // 最小可预约日期
  uint32 max_reserve_date = 3; // 最大可预约日期
}

// 预约时段信息
message ChannelReserveTimeInfo {
  string reserve_time = 1; // 预约时段, 连续
  uint32 groom_uid = 2; // 新郎uid
  uint32 bride_uid = 3; // 新娘uid
  bool is_hot = 4; // 是否热门
  uint32 theme_id = 5; // 主题id
}

// 咨询婚礼预约
message ConsultWeddingReserveRequest {
  uint32 channel_id = 1; // 房间ID
  uint32 reserve_date = 2; // 预约日期
  repeated string reserve_time = 3; // 预约时段, 连续
  uint32 theme_id = 4; // 主题id
  uint32 manager_uid = 5; // 客服uid
}

message ConsultWeddingReserveResponse {}

message ArrangeWeddingReserveRequest {
  uint32 channel_id = 1; // 房间ID
  uint32 reserve_date = 5; // 预约日期
  repeated string reserve_time = 2; // 预约时段, 连续
  bool is_hot = 3; // 是否热门
  uint32 gift_id = 4; // 礼物id
  uint32 source_msg_id = 6; // 原消息id
  uint32 theme_id = 7; // 主题id
  uint32 target_uid = 8; // 目标用户id
}

message ArrangeWeddingReserveResponse {}

message RevokeProposeRequest {
  uint32 uid = 1; // 用户ID
}

message RevokeProposeResponse {
}

message GetChannelReserveTimeSectionConfRequest {
  uint32 date = 1; // 日期时间戳, 0点
  uint32 channel_id = 2; // 房间ID列表
}

message GetChannelReserveTimeSectionConfResponse {
  repeated AdminChannelReserveTimeSectionInfo reserve_time_section_info_list = 1; // 预约时段信息
}

enum AdminReservableSwitch {
  RESERVABLE_SWITCH_UNEXPECTED = 0; // 未知
  RESERVABLE_SWITCH_OPEN = 1; // 开启
  RESERVABLE_SWITCH_CLOSE = 2; // 关闭
}

message AdminChannelReserveTimeSectionInfo {
  uint32 reserve_st = 1; // 预约开始时间
  uint32 reserve_et = 2; // 预约结束时间
  uint32 reserve_uid = 3; // 预约人uid
  uint32 reservable_switch = 4; // 是否可预约 see AdminReservableSwitch
}

message SetAdminChannelReserveTimeSectionSwitchRequest {
  uint32 channel_id = 1;
  uint32 reserve_st = 2;
  uint32 reserve_et = 3;
  uint32 reservable_switch = 4; // 是否可预约 see AdminReservableSwitch
}

message SetAdminChannelReserveTimeSectionSwitchResponse {}

message WeddingGiftValue {
  uint32 uid = 1; // 用户ID
  uint32 value = 2; // 礼物值
}

message WeddingPreProgressInfo {
  uint32 stage = 1; // 阶段, see WeddingPreProgressStage
  uint32 wedding_plan_id = 2; // 婚礼计划ID
  repeated WeddingGiftValue wedding_gift_value_list = 3; // 当前礼物值
}

message GetWeddingPreProgressInfoRequest {
  uint32 channel_id = 1;  // 房间ID
}

message GetWeddingPreProgressInfoResponse {
  WeddingPreProgressInfo pre_progress_info = 1; // 婚礼预热进度信息
}

// 开始婚礼
message StartWeddingRequest {
  uint32 channel_id = 1; // 房间ID
  uint32 wedding_plan_id = 2; // 婚礼计划ID
}

message StartWeddingResponse {}

// 取消婚礼
message CancelPreparedWeddingRequest {
  uint32 channel_id = 1; // 房间ID
  uint32 wedding_plan_id = 2; // 婚礼计划ID
}

message CancelPreparedWeddingResponse {}

message GetNewComersMicUserRequest {
  uint32 channel_id = 1; // 房间ID
}

message GetNewComersMicUserResponse {
  repeated uint32 uid_list = 1; // 新omer用户ID列表
}

message GetPayWeddingChannelListRequest {
}

message GetPayWeddingChannelListResponse {
  repeated PayWeddingChannelItem pay_wedding_channel_list = 1; // 婚礼房间列表
}

message PayWeddingChannelItem {
  uint32 channel_id = 1; // 房间ID
  uint32 newcomers_cnt = 2; // 新人数量
}