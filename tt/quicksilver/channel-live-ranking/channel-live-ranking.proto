syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-live-ranking";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";
package channelliveranking;


service ChannelLiveRanking {

    rpc GetRankingList (GetRankingListReq) returns (GetRankingListResp) {
    }

    rpc GetAnchorHonorNameplate (GetAnchorHonorNameplateReq) returns (GetAnchorHonorNameplateResp) {
    }

    rpc BatchGetAnchorHonorNameplate (BatchGetAnchorHonorNameplateReq) returns (BatchGetAnchorHonorNameplateResp) {
    }

    rpc GiveAnchorHonorNameplate (GiveAnchorHonorNameplateReq) returns (GiveAnchorHonorNameplateResp) {
    }


    rpc GetLiveFansWeekRank(GetLiveFansWeekRankReq) returns (GetLiveFansWeekRankResp) {}
    rpc SettleLiveFansWeekRank(SettleLiveFansWeekRankReq) returns (SettleLiveFansWeekRankResp) {}
    rpc SettleLiveFansWeekRankYKW(SettleLiveFansWeekRankYKWReq) returns (SettleLiveFansWeekRankYKWResp) {}
    rpc HandleFansWeekRank(HandleFansWeekRankReq) returns (HandleFansWeekRankResp) {}
    
  
    rpc FixFansWeekRankOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}
    rpc GetFansWeekRankOrderCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetFansWeekRankOrderList(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

    rpc TestSettleLiveFansWeekRank(TestSettleLiveFansWeekRankReq) returns (TestSettleLiveFansWeekRankResp) {}
    rpc TestSettleLiveFansWeekRankYKW(TestSettleLiveFansWeekRankYKWReq) returns (TestSettleLiveFansWeekRankYKWResp) {}



}

enum ChannelLiveRankingType {
    RankingType_Unknown = 0;
    RankingType_FansLove = 1;               // 亲密榜
    RankingType_NewStart_Hardworking = 2;   // 勤奋新星榜
    RankingType_NewStart_Popularity = 3;    // 人气新星榜
    RankingType_High_Quality_Actor = 4;   // 优质歌手榜
    RankingType_Star_Quality_Actor = 5;   // 星级主播榜
    RankingType_New_Quality_Actor = 6;   // 新星主播榜
}

enum QueryTimeType {
    QueryTimeType_ThisWeek = 0;   // 本周（实时）
    QueryTimeType_LastWeek = 1;   // 上周
}

message ChannelLiveRankingMember {
    enum LiveStatus {
        None = 0;
        Living = 1; // 直播中
    }

    uint32 actor_uid = 1;
    uint32 score = 2;       // 榜单分数值
    uint32 rank = 3;        // 名次
    uint32 sub_score = 4;   // 勤奋新星榜中score用于直播有效天，sub_score用于直播时长
    repeated string actor_tag_list = 5;    // 主播标签列表
}

message MyChannelLiveRank {
    ChannelLiveRankingMember rank_info = 1;
    uint32 last_rank_difference_score = 2;      // 与上一名的分数差
    uint32 last_rank_difference_sub_score = 3;  // 与上一名的分数差(用于勤奋新星榜的直播时长)
    bool show_valid = 4; // 是否需要展示我的排名信息
}

message GetRankingListReq {
    uint32 actor_uid = 1;
    uint32 ranking_type = 2;    //see ChannelLiveRankingType
    uint32 query_time_type = 3; //see QueryTimeType
}

message GetRankingListResp {
    repeated ChannelLiveRankingMember ranking_list = 1;
    MyChannelLiveRank my_rank_info = 2;
}

message HonorNameplate {
    uint32 uid = 1;
    uint32 honor_id = 2;
    string honor_name = 3;
    string honor_url = 4;
    string ranking_enter_honor_url = 5; // 榜单入口荣誉铭牌图标
    uint32 expired_time = 6;
}

message GetAnchorHonorNameplateReq {
    uint32 actor_uid = 1;
}

message GetAnchorHonorNameplateResp {
    HonorNameplate honor_nameplate = 1;
}

message BatchGetAnchorHonorNameplateReq {
    repeated uint32 actor_uid_list = 1;
}

message BatchGetAnchorHonorNameplateResp {
    map<uint32, HonorNameplate> map_honor_nameplate = 1;
}

message GiveAnchorHonorNameplateReq {
    uint32 actor_uid = 1;
    uint32 honor_id = 2;
    uint32 expired_time = 3;
}

message GiveAnchorHonorNameplateResp {}


enum ConsumeStatusType
{
    ENUM_DELAY = 0;  //延迟第二天凌晨3点
    ENUM_ABANDON = 1;  //丢弃
    ENUM_COMMIT = 2;  //提交
}
enum FansRankValSourceType {
    UNSPECIFIED =0; // 无效值
    SEND_GIFT=1;//送礼
    ACTIVITY=2;//活跃值
    KNIGHT=3;// 
}

message GetLiveFansWeekRankReq {
    uint32 op_uid=1;
    uint32 anchor_uid=2;

    // 获取本周周榜才传以下字段
    uint32 begin_id = 3;  // 开始查询的索引编号 用于翻页 第一页可以填0
    uint32 req_cnt = 4;  // 每次获取列表最多需要多少条 一般填50

    bool only_lastweek=5;
}
message GetLiveFansWeekRankResp {
 
    repeated LiveFansWeekRankInfo last_week_top3=1; // 上周TOP3
    repeated LiveFansWeekRankInfo this_week_rank=2; // 本周周榜

    LiveFansWeekRankInfo my_info = 3; // 我的信息

    bool in_settle=4;
}
message LiveFansWeekRankInfo {
    uint32 uid=1;
    uint32 rank=2; // 我的排名，为0则不上榜
    uint32 rank_value=3;  // 榜单数值
    uint32 d_value=4; // 差值

    bool is_show_anchor_card=5;
}
message FansWeekRankSettleInfo {
    uint32 anchor_uid=1;
    repeated LiveFansWeekRankInfo rank_top3=2;
}

message SettleLiveFansWeekRankReq {
    bool certain = 1;
    uint32 anchor_uid=2;
    uint32 settle_ts=3;
}
message SettleLiveFansWeekRankResp {

}


message SettleLiveFansWeekRankYKWReq {
}
message SettleLiveFansWeekRankYKWResp {
}

message TestSettleLiveFansWeekRankReq{
    bool certain = 1;
    uint32 anchor_uid=2;
    uint32 delay_sec=4;
}
message TestSettleLiveFansWeekRankResp{}

message TestSettleLiveFansWeekRankYKWReq {
    bool certain = 1;
    uint32 anchor_uid=2;
    uint32 fans_uid=3;
}
message TestSettleLiveFansWeekRankYKWResp{}

message HandleFansWeekRankReq {
    uint32 anchor_uid=1;
    uint32 fans_uid=2;
    uint32 channel_id=3;
}
message HandleFansWeekRankResp {
}


