syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/business-push-policy/comm-pop-up-window-push-server";
package comm_pop_up_window_push_server;

service CommPopUpWindowPushServer {
  rpc BatchPushPwnKRaceToUser(BatchPushPwnKRaceToUserReq) returns (BatchPushPwnKRaceToUserResp) {}

  // 模拟进房消息推送
  rpc FakeChannelEnterMsgPush(FakeChannelEnterMsgReq) returns (FakeChannelEnterMsgResp) {}
}

// 请求来源
enum PopUpWindowReqSource {
  Unknown = 0;
  MIN_GAME = 1;                //小游戏平台
  MIN_GAME_PWNK_ENTRY_RESULT = 2;  // pwnk参赛结果
  Web_H5 = 3;  // web 前端 H5
  NewUserBusinessTest = 4;  // 新用户的业务测试
  LowVersionUpdate = 5;           // 低版本更新通知
  NewbieLoginTip = 6; // 萌新军登录弹窗提示
  Soulmate = 7; // 灵魂伴侣
}

// 震动类型
enum ShakeType {
  NotShake = 0;            // 默认不震动
  GeneralSingleShake = 1;  // 普通单次震动
  ImportantMultiShake = 2; // 重要多次震动
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchPushPwnKRaceToUserReq {
  repeated uint32 uids = 1;
  PushItem push_Item = 2;
  uint32 req_source = 3;      // PopUpWindowReqSource
  string business_identity = 4; // 业务标识，非必填
  PopUpWindowRetainInfo retain_info = 5;   // 挽回弹窗信息
}

// 弹窗二次挽留信息
message PopUpWindowRetainInfo {
  string retain_title = 1;     // 挽回标题
  string retain_content = 2;   // 挽回文案
  string retain_icon_url = 3;  // 挽回配置图
  string retain_button_content = 4; // 挽回按钮文案
  string retain_cancel_button = 5; // 挽回取消按钮
}


message PushItem {
  string title = 1;
  string content = 2;
  string icon_url = 3;
  string jump_url = 4;
  int64 begin_at = 5;   //开始时间戳
  int64 expire_at = 6;  //结束时间戳
  repeated string highlight_content_list = 7;     // 高亮的内容
  string highlight_color = 8;     // 高亮的颜色
  bool is_not_offline_push = 9;       //  是否不需要离线推送，因之前的版本默认 false:推送离线 ,为 true 时不用推离线推送
  uint32 show_time = 10;          // 展示时间，单位 秒
  string background_image_url = 11;  //背景图
  string button_content = 12;     // 按钮文案
  string button_background_color = 13;  // 按钮背景颜色
  string button_content_color = 14;    //按钮文字颜色
  string push_content_color = 15;      // 推送内容颜色
  bool is_not_delay_push = 16;  // 默认需要离线后的在线再次推送， true 时不需要
  ShakeType shake_type = 17;
}

message BatchPushPwnKRaceToUserResp {

}

message FakeChannelEnterMsgReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 channel_type = 3;
}

message FakeChannelEnterMsgResp {

}
