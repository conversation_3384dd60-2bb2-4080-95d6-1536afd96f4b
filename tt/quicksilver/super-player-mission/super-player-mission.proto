syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/super-player-mission";
package super_player_mission;

message GetSuperPlayerMissionReq{
  uint32 uid = 1;
}

message GetSuperPlayerMissionResp{
  repeated MissionDetail mission = 1;
}

message GetSuperPlayerMissionByIdReq{
  uint32 uid = 1;
  uint32 mission_id = 2;
}

message GetSuperPlayerMissionByIdResp{
  MissionDetail mission = 1;
}

message MissionDetail{
  uint32 id = 1;     // 任务id
  string name = 2;  // 任务名称
  MissionType mission_type = 3;   // 任务类型
  repeated AwardDetail award_list = 4;  // 任务奖励列表
  uint32 required_count = 5;   // 任务需求计数
  uint32 finish_count = 6;    // 任务已完成计数
  MissionStatus mission_status = 7;  // 任务目前状态
  string mission_icon = 8; // 任务图标
  string huanyou_name = 9;  // 任务名称 - 对应欢游
}

enum MissionType{
  Unknown = 0;
  Daily = 1;  // 日常任务
  Open = 2;   // 开通任务
}

enum MissionStatus{
  Incomplete = 0;   // 未完成
  Completed = 1;    // 已完成，尚未发奖
  Finished = 2;     // 已完成，发奖完毕
}

message AwardDetail{
  AwardType type = 1;
  uint32 value = 2;
}

enum AwardType{
  Grow = 0; // 成长值
  RedDiamond = 1; // 红钻
}

service SuperPlayerMission
{
  rpc GetSuperPlayerMission(GetSuperPlayerMissionReq) returns (GetSuperPlayerMissionResp){
  }
  rpc GetSuperPlayerMissionById(GetSuperPlayerMissionByIdReq) returns (GetSuperPlayerMissionByIdResp){
  }
}