syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-open-game-playmate";

package channel_open_game_playmate;

service ChannelOpenGamePlaymate {
    rpc GetPlaymateList (GetPlaymateListReq) returns (GetPlaymateListResp) {
    }
}

message Playmate {
    uint32 uid = 1;
    int64 last_play_time = 2;   // 此值为0表示没玩过
}

message GetPlaymateListReq {
    uint32 uid = 1;

    int64 next = 2; // 首次给0，后续给返回值中的数值
    int64 count = 3; // 本次获取的数量

    uint32 game_id = 4; // 游戏标识
}

message GetPlaymateListResp {
    repeated Playmate playmate_list = 1;

    int64 next = 2; // 返回值 <= 0， 则表示无后续数据
}