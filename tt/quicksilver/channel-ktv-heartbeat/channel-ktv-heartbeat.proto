syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-ktv-heartbeat";
package channel_ktv_heartbeat;
import "tt/quicksilver/channel-ktv/channel-ktv.proto";

service ChannelKTVHeartbeat {
    rpc ReportKTVHeartBeat (ReportKTVHeartbeatReq) returns (ReportKTVHeartbeatResp) {
    }

    rpc ConfirmKTVHeartBeat(channel_ktv.ConfirmKTVHeartBeatReq) returns (channel_ktv.ConfirmKTVHeartBeatResp) {
    }
}

message ReportKTVHeartbeatReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
}

message ReportKTVHeartbeatResp{

}
