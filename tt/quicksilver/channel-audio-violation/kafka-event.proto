syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package channel_audio_violation.event;

option go_package = "golang.52tt.com/protocol/services/channel-audio-violation/event";

import "tt/quicksilver/channel-audio-violation/channel-audio-violation.proto";

message ChannelAudioViolationEvent {
  string uuid = 1;                     // 审判uuid
  uint32 uid = 2;                      // 违规用户uid
  uint32 cid = 3;                      // 违规房间cid
  uint64 start_time = 4;               // 审判开始时间，Unix时间戳
  uint64 close_time = 5;               // 审判结束时间，Unix时间戳
  repeated ViolationEvent events = 6;  // 违规事件
  uint32 punishment_duration = 7;      // 惩罚时长，单位秒
}