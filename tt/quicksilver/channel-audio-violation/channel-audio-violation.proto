syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package channel_audio_violation;

option go_package = "golang.52tt.com/protocol/services/channel-audio-violation";

import "tt/quicksilver/extension/options/options.proto";

service ChannelAudioViolation {
  option (service.options.service_ext) = {
    service_name: "channel-audio-violation"
  };

  // 发起新的违规审判
  rpc NewJudgment(NewJudgmentReq) returns (NewJudgmentResp) {}

  // 为指定审判投票
  rpc JudgmentVote(JudgmentVoteReq) returns (JudgmentVoteResp) {}

  // 结束违规审判
  rpc JudgmentClose(JudgmentCloseReq) returns (JudgmentCloseResp) {}

  // 对审判结果发起申诉
  rpc JudgmentAppeal(JudgmentAppealReq) returns (JudgmentAppealResp) {}

  // 获取审判详细信息
  rpc GetJudgmentInfo(GetJudgmentInfoReq) returns (GetJudgmentInfoResp) {}

  // 获取用户历史审判结果
  rpc GetUserHistoryJudgmentResult(GetUserHistoryJudgmentResultReq) returns (GetUserHistoryJudgmentResultResp) {}

  // 关联审判和违规的音频文件
  rpc SetJudgmentAudioFile(SetJudgmentAudioFileReq) returns (SetJudgmentAudioFileResp) {}
}

// 违规事件
message ViolationEvent {
  uint64 unix_timestamp = 1;              // 触发违规时间，unix时间戳
  repeated string violation_actions = 2;  // 违规行为，可以有多个
}

message NewJudgmentReq {
  uint32 cid = 1;
  uint32 uid = 2;            // 被审判用户uid
  ViolationEvent event = 3;  // 用户违规事件
  uint32 invited_num = 4;    // 邀请投票的人数
}
message NewJudgmentResp {
  string judgment_uuid = 1;         // 本次审判的uuid
  uint64 judgment_start_time = 2;   // 启动审判的时间
  bool is_already_in_judgment = 3;  // 是否用户当前已在审判中，此时返回的是旧的uuid
}

enum JudgmentVoteResult {
  JUDGMENT_VOTE_RESULT_UNSPECIFIED = 0;
  JUDGMENT_VOTE_RESULT_YES = 1;              // 有违规
  JUDGMENT_VOTE_RESULT_NO = 2;               // 无违规
  JUDGMENT_VOTE_RESULT_NOT_SURE = 3;         // 不知道
  JUDGMENT_VOTE_RESULT_NOT_PARTICIPATE = 4;  // 不参与投票
}

message JudgmentVoteReq {
  uint32 cid = 1;
  uint32 uid = 2;
  string judgment_uuid = 3;
  string not_participate_reason = 4;  // 不参与投票时填写
  JudgmentVoteResult result = 5;      // 投票结果
}
message JudgmentVoteResp {
  uint32 invited_num = 1;
  repeated JudgmentVoteResult results = 2;
  uint32 judgment_uid = 3;  // 被审判用户uid
}

message JudgmentResult {
  bool is_violated = 1;            // 是否违规
  bool is_punished = 2;            // 是否被惩罚
  uint32 punishment_duration = 3;  // 惩罚时长，单位秒
}

message JudgmentCloseReq {
  string judgment_uuid = 1;
  JudgmentResult result = 2;
}
message JudgmentCloseResp {
  JudgmentInfo detail = 1;
}

message JudgmentAppealReq {
  string judgment_uuid = 1;
  string reason = 2;  // 申诉理由
}
message JudgmentAppealResp {
  JudgmentInfo detail = 1;
}

message JudgmentHistory {
  uint32 cid = 1;
  string judgment_uuid = 2;
  uint64 start_time = 3;
  uint64 close_time = 4;
  JudgmentResult result = 5;
}

message GetUserHistoryJudgmentResultReq {
  uint32 uid = 1;
}
message GetUserHistoryJudgmentResultResp {
  repeated JudgmentHistory results = 1;
}

message AudioFileInfo {
  string file_url = 1;         // 录音文件url
  uint64 begin_timestamp = 2;  // 开始录音时间，unix时间戳
  uint64 stop_timestamp = 3;   // 停止录音时间，unix时间戳
}

message SetJudgmentAudioFileReq {
  string judgment_uuid = 1;            // 审判uuid
  AudioFileInfo audio_file = 2;        // 违规音频文件信息
  repeated ViolationEvent events = 3;  // 违规事件，一段录音里可能有多个
}
message SetJudgmentAudioFileResp {
  JudgmentInfo detail = 1;
}

message AppealInfo {
  string reason = 1;
  uint64 unix_timestamp = 2;  // 申诉unix时间戳
}

message JudgmentInfo {
  string uuid = 1;
  uint32 uid = 2;
  uint32 cid = 3;
  uint32 invited_num = 4;
  uint64 start_time = 5;
  uint64 close_time = 6;
  repeated ViolationEvent events = 7;
  repeated JudgmentVoteResult votes = 8;
  repeated AudioFileInfo audio = 9;
  JudgmentResult result = 10;
  AppealInfo appeal = 11;
}

message GetJudgmentInfoReq {
  string uuid = 1;
}

message GetJudgmentInfoResp {
  JudgmentInfo detail = 1;
}