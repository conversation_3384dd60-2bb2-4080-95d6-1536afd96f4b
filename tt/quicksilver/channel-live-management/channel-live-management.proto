syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-live-management";
package channellivemanagement;


service ChannelLiveManagement {
    rpc SetOfficialChRegisterInfo (SetOfficialChRegisterInfoReq) returns (SetOfficialChRegisterInfoResp) {
    }
    rpc GetOfficialChRegisterInfo (GetOfficialChRegisterInfoReq) returns (GetOfficialChRegisterInfoResp) {
    }
    rpc GetCancelRegisterInfoList (GetCancelRegisterInfoListReq) returns (GetCancelRegisterInfoListResp) {
    }
    rpc AddOfficialChWhite(AddOfficialChWhiteReq) returns (AddOfficialChWhiteResp) {
    }
    rpc DelOfficialChWhite(DelOfficialChWhiteReq) returns (DelOfficialChWhiteResp) {
    }
    rpc GetOfficialChWhiteList(GetOfficialChWhiteListReq) returns (GetOfficialChWhiteListResp) {
    }
    rpc CheckUserRegisterEntry(CheckUserRegisterEntryReq) returns (CheckUserRegisterEntryResp) {
    }
    rpc GetRegisterOfficialChList(GetRegisterOfficialChListReq) returns (GetRegisterOfficialChListResp) {
    }
    rpc RegisterOfficialChannel(RegisterOfficialChannelReq) returns (RegisterOfficialChannelResp) {
    }
    rpc CancelRegisterOfficialCh(CancelRegisterOfficialChReq) returns (CancelRegisterOfficialChResp) {
    }
}

// 官频报名信息
message OfficialChRegisterInfo {
    uint32 channel_id = 1; 
    string act_time = 2;  // 活动时间
    int64  register_ts = 3;  // 报名时间
    string register_cond = 4;  // 报名条件
    uint32 sort_weight = 5;  // 排序权重 数字越小排序越前
    string action_example = 6;  // 操作示例链接
    string audio_example = 7;  // 音频实例链接
    string intro_exemple = 8;  // 个人介绍填写示例
}

// 设置官频报名信息
message SetOfficialChRegisterInfoReq {
   OfficialChRegisterInfo info = 1;
}
message SetOfficialChRegisterInfoResp {
}

// 获取官频报名信息
message GetOfficialChRegisterInfoReq {
   uint32 channel_id = 1; 
}
message GetOfficialChRegisterInfoResp {
   OfficialChRegisterInfo info = 1;
}

// 取消报名信息
message CancelRegisterInfo {
   string ttid = 1;
   uint32 uid = 2;
   string nickname = 3;  
   string ch_name = 4;  // 官频名称
   int64 match_date = 5;  // 场次日期
   int64 match_schedule_begin = 6;  // 场次排班开始时间
   int64 match_schedule_end = 7;  // 场次排班结束时间
   string introduction = 8;  // 报名填写内容
   string match_name = 9; // 场次名称
}

// 获取取消报名信息列表
message GetCancelRegisterInfoListReq {
   uint32 page = 1; // 从1开始
   uint32 page_size = 2;  
   uint32 begin_ts = 3; 
   uint32 end_ts = 4; 
   uint32 channel_id =5; 
}
message GetCancelRegisterInfoListResp {
   repeated CancelRegisterInfo info_list = 1;
   uint32 next_page = 2; // 为0表示结束
   uint32 total_cnt = 3;  // 总数
}

// 白名单主播信息
message WhiteAnchorInfo {
   uint32 uid = 1;
   string ttid = 2;
   string nickname = 3;
   int64  operate_ts = 4;
}

// 增加官频报名白名单
message AddOfficialChWhiteReq {
   uint32 channel_id = 1;
   repeated uint32 uid_list = 2;
} 
message AddOfficialChWhiteResp {
   repeated uint32 err_list = 1;
} 

// 删除官频报名白名单
message DelOfficialChWhiteReq {
   uint32 channel_id = 1;
   repeated uint32 uid_list = 2;
}
message DelOfficialChWhiteResp {
}

// 获取官频报名白名单列表
message GetOfficialChWhiteListReq {
   uint32 channel_id = 1;
}
message GetOfficialChWhiteListResp {
   repeated WhiteAnchorInfo info_list = 1;
}


// 场次排班信息
message MatchSchedule {
   int64 begin_ts = 1; // 开始时间
   int64 end_ts = 2;  // 结束时间
   uint32 uid = 3; // 排班uid
   uint32 schedule_id = 5; // 环节id
}

// 场次信息
message MatchInfo {
   int64 match_ts = 1;  // 场次时间
   string match_name = 2;  // 场次名称
   repeated MatchSchedule schedule_list = 3;  // 排班信息
   uint32 match_id = 4;  // 场次id
}

// 报名状态类型
enum RegisterStatus {
   RegisterStatusInvalid = 0 ;  // 无效
   RegisterStatusNo = 1; // 没有报名
   RegisterStatusSuccess = 2; // 报名成功
   RegisterStatusCancel = 3;  // 已取消报名
   RegisterStatusNoOnTimeLive = 4; // 报名了未按时开播
   RegisterStatusReview = 5;  // 报名审核中
}

// 官频信息
message OfficialChannelInfo {
    OfficialChRegisterInfo info = 1;
    uint32 register_status = 2;  // 报名状态
    repeated MatchInfo register_match_list = 3; // 正在生效的报名成功的场次信息
}

// 检查用户是否有报名
message CheckUserRegisterEntryReq {
   uint32 uid = 1;
}
message CheckUserRegisterEntryResp {
   bool is_has_entry = 1;  // 是否有报名入口
}

// 获取报名页的官频列表
message GetRegisterOfficialChListReq {
   uint32 uid = 1;
   uint32 cid = 2;  // 指定房间id查询
}
message GetRegisterOfficialChListResp {
   repeated OfficialChannelInfo  info_list = 1;
   repeated OfficialChannelInfo my_register_list = 2;  // 主播正在生效的报名成功的官频列表
}

// 报名官频
message RegisterOfficialChannelReq {
   uint32 uid = 1;
   uint32 channel_id = 2; 
   string introduction = 3; // 个人介绍
   MatchInfo  match_info = 4;  // 报名的场次信息
}
message RegisterOfficialChannelResp {
}

// 取消报名
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CancelRegisterOfficialChReq {
   uint32 uid = 1;
   uint32 channel_id = 2;   
   int64  registerTs = 3;  // 报名时间
   MatchInfo  match_info = 4;  // 报名的场次信息
}
message CancelRegisterOfficialChResp {
}



