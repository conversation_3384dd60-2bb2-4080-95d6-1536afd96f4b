syntax="proto3";

option go_package = "golang.52tt.com/protocol/services/channel-present-runway";

package channel_present_runway;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list
service ChannelPresentRunway {
    rpc GetChannelRunwayList ( GetChannelRunwayListReq ) returns( GetChannelRunwayListResp ) {
    }
	rpc GetUserRunwayInfo ( GetUserRunwayInfoReq ) returns( GetUserRunwayInfoResp ) {
    }
}


message RunwayConfig
{
	uint32 level = 1;
	uint32 begin_value = 2;
	uint32 end_value = 3;
}

message RunwayEffect
{
	uint32 runway_present_base = 1;	// 单次每送出指定值的礼物，可额外增加跑道时间
	uint32 runway_add_seconds = 2;	// 跑道在礼物的播放时间基础上额外再增加的时间（秒）
	uint32 max_runway_add_seconds = 3; // 跑道最多可增加的时间（秒）
}

message UserRunwayBrief
{
	uint32 uid = 1;
	uint32 cur_value = 2;
	uint32 runway_level = 3;
	uint32 expired_time = 4;
	uint32 start_time = 5;
	uint32 cont_launch_times = 6;	// 连续火箭冲天次数
}

message UserRunwayInfo
{
	UserRunwayBrief brief_info = 1;
	RunwayConfig runway_cfg = 2;
}

// 获取房间的火箭跑道信息
message GetChannelRunwayListReq
{
	uint32 channel_id = 1;
    uint32 uid = 2;
}

message GetChannelRunwayListResp
{
	repeated UserRunwayInfo runway_list = 1;
	RunwayEffect runway_effect = 2;
}

// 获取用户在房间的火箭跑道信息
message GetUserRunwayInfoReq
{
	uint32 uid = 1;
	bool query_ready_info = 2;	// 未出现火箭跑道时，查询用户的当前累计送礼值
}

message GetUserRunwayInfoResp
{
	UserRunwayInfo runway_info = 1;
}

message UserPresentRecord
{
	uint32 uid = 1;
	uint32 present_value = 2;
	uint64 micro_ts = 3;
}


