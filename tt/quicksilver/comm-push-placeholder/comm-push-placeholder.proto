syntax = "proto3";
package comm_push_placeholder;
option go_package = "golang.52tt.com/protocol/services/comm-push-placeholder";

service CommPushPlaceholder {
    rpc GetPlaceHold(GetPlaceHoldReq) returns (GetPlaceHoldResp) {}

    // 根据文案库 id 和 文案 id 获取用户对应的一条替换好的文案
    rpc BatchGetUserPushCopyWriting(BatchGetUserPushCopyWritingReq) returns(BatchGetUserPushCopyWritingResp){}

    // 根据文案库 id 获取文案列表模板 （不替换的）
    rpc GetCopyWritingTemplateByLibId(GetCopyWritingTemplateByLibIdReq) returns (GetCopyWritingTemplateByLibIdResp){}
}

/*
name:{nickname} -> key:uid
name:{city}-> key:uid
name:{ugcchannelname} -> key:uid
name:{ugcchanneltab} -> key:uid
name:{pgcchanneltab} -> key:pgcCid
name:{feedattach} -> key:postId
name:{hashtag} -> key:postId
*/
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message PlaceHoldParam {
    enum KeyType {
        ENull = 0;
        EUidInt = 1;
        EPgcCidInt = 2;
        EPostIdString = 3;
    }

    string name = 1;
    KeyType keyType = 2;
    string key = 3;
    string reqId = 4;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message PlaceHoldItem {
    string name = 1;
    string val  = 2;
    string reqId = 3;
}

message GetPlaceHoldReq {
    repeated PlaceHoldParam params = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetPlaceHoldResp {
    repeated PlaceHoldItem retItem = 1;
}

message UidPlaceHoldItem {
    uint32 uid = 1;      // 要推给哪个用户
    string content_id = 2;   // 文案id
    repeated ItemPlaceHold title_place_hold_param = 3;    // 用于替换标题占位符的, 该用户可能有多个占位符参数
    repeated ItemPlaceHold content_place_hold_param = 4;    // 用于替换内容占位符的
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ItemPlaceHold {
    string key = 1;                      // 如是 uint 类型，转为 string 传过来，根据 keyType 来定
    PlaceHoldParam.KeyType keyType = 2;
}

message BatchGetUserPushCopyWritingReq {
    string lib_id = 1;    // 文案库ID,用于获取原始文案
    repeated UidPlaceHoldItem uid_place_hold_items = 2;  //批量用户及其占位符参数
}

message CopyWriting {
    string content_id = 1;    // 文案id
    string title = 2;
    string content = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CopyWritingList{
    repeated CopyWriting copyWritings = 1;
}

message BatchGetUserPushCopyWritingResp {
    map<uint32,CopyWriting> uid_copy_writing_map = 1;   // uid 对应的 替换好的一条文案
}

message GetCopyWritingTemplateByLibIdReq {
    string lib_id = 1;    // 文案库id
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetCopyWritingTemplateByLibIdResp {
    repeated CopyWriting copyWritings = 1;    // 文案库 id 下的所有文案模板（未替换的）
}