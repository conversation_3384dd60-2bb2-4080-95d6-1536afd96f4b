syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/sub-new-user-recall";
package sub_new_user_recall;


service SubNewUserRecall {
    //获取过滤名单 .
    rpc BatchFilterUids (BatchFilterUidsReq) returns (BatchFilterUidsResp) {
    }
    rpc RecordNewUserTimeCount(RecordNewUserTimeCountReq)returns (RecordNewUserTimeCountResp) {
    }

    //获取次新召回 源数据的明细(指定日期yyyymmdd)
    rpc GetRecallDataSrc(GetRecallDataSrcReq) returns (GetRecallDataSrcResp) {}

    //获取指定推送时间点的推送队列长度
    rpc GetPushQueueLen(GetPushQueueLenReq) returns (GetPushQueueLenResp) {}

    //设置已经推送结果
    rpc SetAlreadyPush(SetAlreadyPushReq) returns(SetAlreadyPushResp){}

    //获取算法召回源数据的明细
    rpc GetAlgoRecallDataSrc(GetAlgoRecallDataSrcReq) returns (GetAlgoRecallDataSrcResp) {}

    // 批量获取次新用户召回数据
    rpc BatchGetUserRecallItem(BatchGetUserRecallItemReq) returns (BatchGetUserRecallItemResp) {}

    // 批量获取用户的当天的已推送用户
    rpc BatchGetTodayPushFilter(BatchGetTodayPushFilterReq) returns (BatchGetTodayPushFilterResp) {}

    // 批量设置用户当天已推送的用户
    rpc BatchSetTodayPushFilter(BatchSetTodayPushFilterReq) returns (BatchSetTodayPushFilterResp) {}
}

message BatchSetTodayPushFilterReq {
    map<uint32,uint32> uid_al_push_uid_map = 1;   // 用户对应的已推送的用户
}

message BatchSetTodayPushFilterResp {

}

message BatchGetTodayPushFilterReq {
    repeated uint32 uids = 1;
}

message TodayPushFilterInfo {
    repeated uint32 filter_uid = 1;
}

message BatchGetTodayPushFilterResp {
    map<uint32,TodayPushFilterInfo> push_filter_map = 1;
}

message BatchFilterUidsReq {
    enum TestType {
        TestAll = 0;//所有类型
        Test0 = 1;//对照组
        TestA = 2;//实验组
        
    }
    enum ActionType {
        All = 0;//所有类型
        Reg = 1;//注册
        Login = 2;//登录
    }
    repeated uint32  uids = 1;
    int64 date_unix = 2;//日期 格式为 要查日期的unix秒时间戳
    TestType test_type = 3;//对照组或实验组
    ActionType action_type = 4;//登录 或注册

}


message BatchFilterUidsResp { 
    repeated uint32  uids = 1;//返回过滤后的列表
}


message RecordNewUserTimeCountReq {
    // buf:lint:ignore MESSAGE_PASCAL_CASE
    message Counter_info {
        uint32 channel_type = 1;//上报的房间类型
        uint32 second = 2;//每次上报时的页面时间   
    }
    uint32 uid = 1;
    Counter_info ugc_info = 2;//每次上报时的ugc页面时间 
    Counter_info pgc_info = 3;//每次上报时的pgc页面时间
    uint32 im_second = 4;//每次上报时的im页面时间
    uint32 dynamics_second = 5;//每次上报时的动态页面时间
}

message RecordNewUserTimeCountResp {

}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetRecallDataSrcReq {
    string dataDate = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetRecallDataSrcResp {
    uint32 hasImChatRecallUidCnts = 1;
    uint32 hasFriendsRecallUidCnts = 2;
    uint32 hasFollowUserRecallUidCnts = 3;
    uint32 hasMatchPlayerRecallUidCnts = 4;
    uint32 hasMatchRoomOwnerUidCnts = 5;
    uint32 hasPgcChannelCnts = 6;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetPushQueueLenReq {
    uint32 moment = 1;
    uint32 pushCarry = 2; //100-离线push, 101-短信
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetPushQueueLenResp {
    uint32 queueLen = 1;
}


// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SetAlreadyPushReq {
    repeated uint32 uids = 1;//此次推送的人
    uint32 pushType = 2;//此次推送的push类型
}

message SetAlreadyPushResp {
    
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAlgoRecallDataSrcReq {
    string dataDate = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAlgoRecallDataSrcResp {
    map<uint32, uint32> moment2Cnt = 1;
}

message BatchGetUserRecallItemReq {
    repeated uint32 uids = 1;
}

message UserRecallItem {
    uint32 uid = 1;
    repeated uint32 im_chat_uids = 2;
    repeated uint32 friend_uids = 3;
    repeated uint32 follow_uids = 4;
    repeated uint32 match_playmate_uids = 5;
    uint32 recent_pgc_cid = 6;
    uint32 recent_pgc_creator_id = 7;
    repeated uint32 match_room_owner_uids = 8;
    repeated uint32 send_gift_uids = 9;
    repeated uint32 recv_gift_uids = 10;
    repeated uint32 fans_uids = 11;
    repeated uint32 send_gift_pgc_cids = 12;
    repeated uint32 convene_pgc_cids = 13;
    repeated uint32 recent_pgc_cids = 14;
    string material_label = 15;
    repeated string screen_playing_channel_tabs = 16;
}

message BatchGetUserRecallItemResp {
    map<uint32,UserRecallItem> user_recall_item_map = 1;
}

