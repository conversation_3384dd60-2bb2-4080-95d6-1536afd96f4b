syntax = "proto3";

package roommasterapprentice;
option go_package = "golang.52tt.com/protocol/services/roommasterapprentice";


service RoomMasterApprentice {
    /* 师徒列表模块 */
    // 批量添加师傅
    rpc BatchAddMaster (BatchAddMasterReq) returns (BatchAddMasterResp) {}
    // 批量删师傅
    rpc BatchDelMaster (BatchDelMasterReq) returns (BatchDelMasterResp) {}
    // 是师父&在收徒时间内
    rpc IsMasterInValidTime (IsMasterInValidTimeReq) returns (IsMasterInValidTimeResp) {}
    // web 首页数据
    rpc MasterInitForWeb(MasterInitForWebReq)returns (MasterInitForWebResp){}
    // 是否有收徒资格
    rpc IsMaster (IsMasterReq) returns (IsMasterResp) {}
    // 完成任务的rpc
    rpc RecordPlayRoom(RecordPlayRoomReq)returns(RecordPlayRoomResp){}
    // 判断输入用户列表
    rpc CheckUserTypeList (CheckUserTypeListReq) returns (CheckUserTypeListResp) {}

     //获取历史徒弟列表
    rpc GetHistoryApprentice(GetHistoryApprenticeReq)returns (GetHistoryApprenticeResp){}
    /* 师徒列表模块 end */



   

    /*佣金模块 begin*/
    //用户提现
    rpc DrawBalance(DrawBalanceReq)returns (DrawBalanceResp){}
    //获取余额
    rpc GetUserBalanceByUserID(GetUserBalanceByUserIDReq)returns (GetUserBalanceByUserIDResp){}
    //获取其订单列表
    rpc GetOrderListByUserID(GetOrderListByUserIDReq)returns (GetOrderListByUserIDResp){}
    //获取订单信息
    rpc GetOrderInfoByUserIDOrderID(GetOrderInfoByUserIDOrderIDReq)returns (GetOrderInfoByUserIDOrderIDResp){}
    //获取订单信息 供佣金平台反查
    rpc GetOrderInfoByOrderID(GetOrderInfoByOrderIDReq)returns (GetOrderInfoByOrderIDResp){}
    //佣金平台更新佣金信息 将订单状态标记
    rpc UpdateOrderInfoByOrderID(UpdateOrderInfoByOrderIDReq)returns (UpdateOrderInfoByOrderIDResp){}
    //绑定微信信息
    rpc BindWXUserPayInfo(BindWXUserPayInfoReq)returns (BindWXUserPayInfoResp){}
    //查询微信绑定信息
    rpc GetBindingInfo(GetBindingInfoReq)returns(GetBindingInfoResp){}

    /*佣金模块 end*/
    rpc GetActConfig (GetActConfigReq) returns (GetActConfigResp) {}

    // 监控 师父的收益和提现
    rpc MasterMonitorInToday(MasterMonitorInTodayReq)returns (MasterMonitorInTodayResp){}
    // 活动数据统计（活动成本和师父收益）
    rpc GetActivityStatistics(GetActivityStatisticsReq) returns (GetActivityStatisticsResp){}

}



/* 师徒列表模块 */

message BatchAddMasterReq{
    repeated uint32 uids = 1;
}
message BatchAddMasterResp{}

message BatchDelMasterReq{
    repeated uint32 uids = 1;
}
message BatchDelMasterResp{}

// 是师父&在收徒时间内
message IsMasterInValidTimeReq{
    uint32 uid = 1;
}
message IsMasterInValidTimeResp{
    bool is_master = 1;
}

message IsMasterReq{
    uint32 uid = 1;
}
message IsMasterResp{
    bool is_master = 1;
}

/* 师徒列表模块 end */



/* 佣金模块 begin */

message DrawBalanceReq {
    uint32 user_id = 1;
    int64 draw_balance = 2;
}

message DrawBalanceResp {
    string message = 1;
    string order_id = 2;
}


message GetUserBalanceByUserIDReq {
    uint32 user_id = 1;
}



message GetUserBalanceByUserIDResp {
    int64 total_balance = 1;
    int64 available_balance = 2;//
    int64 unavailable_balance = 3;//目前不可用的
    int64 status  = 4;//status 1 为正常状态 2为提现中 3为冻结
}


message OrderInfo {
    uint32 user_id = 1;
    string order_id = 2;
    int64 status  = 3;//提现订单 1初始化 2提现中 3提现成功 4提现失败
    int64 now_balance = 4; //创建订单前的余额
    int64 amount = 5;
    int64 type = 6;
    string describe = 7;
    string reason = 8;
    int64 create_time = 9;
    int64 update_time = 10;
}



message GetOrderListByUserIDReq {
    uint32 user_id = 1;
    int64 limit = 2;
    int64 last_id = 3;
}

message LoadMore {
    bool has_next = 1;
    int64 last_id = 2;
    int64 limit = 3;
}

message GetOrderListByUserIDResp {
    uint32 user_id = 1;
    repeated OrderInfo order_list  = 2;
    LoadMore load_more = 3;
}


message GetOrderInfoByUserIDOrderIDReq {
    uint32 user_id = 1;
    string order_id = 2;
}

message GetOrderInfoByUserIDOrderIDResp {
    OrderInfo order_info  = 1;
}

message GetOrderInfoByOrderIDReq {
    string order_id = 1;
}

message GetOrderInfoByOrderIDResp {
    OrderInfo order_info  = 1;
}

message UpdateOrderInfoByOrderIDReq {
    string order_id = 1;
    int64 status = 2;
    string reason = 3;
}

message UpdateOrderInfoByOrderIDResp {
    string message = 1;
}


message BindWXUserPayInfoReq {
    uint32 user_id = 1;
    string code = 2;
    string state = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BindWXUserPayInfoResp {
    string status = 1;//绑定状态, 若为 UNBANDIND 则为未绑定微信OPENID, 若为 BANDING 则已绑定微信OPENID
    string openId = 2;
    string nick_name = 3;
    string message = 4;
}

message GetBindingInfoReq {
    uint32 user_id = 1;
    string code = 2;
    string state = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetBindingInfoResp {
    string status = 1;//绑定状态, 若为 UNBANDIND 则为未绑定微信OPENID, 若为 BANDING 则已绑定微信OPENID
    string openId = 2;
    string nick_name = 3;
    string message = 4;
}


message GetActConfigReq{}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetActConfigResp{
    string begin_time = 1; // 活动开始时间
    string end_time = 2; // 活动结束时间
    string EndGetApprenticeTime = 3; // 收徒弟结束时间
    string EndTaskTime = 4; // 带徒弟结束时间
}

// web 首页数据
message MasterInitForWebReq{
    uint32 master_uid = 1;
}

message ApprenticeInfo {
    uint32 uid  = 1;
    string nickname = 2;
    int32 sex = 3;
}

message MasterInitForWebResp{
    repeated ApprenticeInfo already_player_list = 1;
    uint32 recruit_time = 2;
    uint32 withdrawable_income = 3;
    uint32 total_income = 4;
    uint32 available_num = 5;
    uint32 additional_income = 6;
    uint32 today_income = 7; // 获取师父今日已赚
}

message GetHistoryApprenticeReq {
    uint32 uid = 1;
    int64 limit = 2;
    int64 last_id = 3;
}

message DayApprenticeInfo {
    string month_day = 1;
    repeated ApprenticeInfo apprentice_list = 2;
    int64 income = 3;
}
message GetHistoryApprenticeResp {
    repeated DayApprenticeInfo  apprentice_list = 1;
    LoadMore load_more = 2;
}



// 监控 师父的收益和提现
message MasterMonitorInTodayReq{
    uint32 limit_income = 1;
    uint32 limit_withdraw = 2;
    int64 check_ts = 3;
}
message MasterLimitInfo{
    uint32 master_uid = 1;
    uint32 amount = 2;
}
message MasterMonitorInTodayResp{
    repeated MasterLimitInfo exceed_income_masters = 1;
    repeated MasterLimitInfo exceed_withdraw_masters = 2;
}

// 活动数据统计（活动成本和师父收益）
message GetActivityStatisticsReq{
    string date = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ActivityStatistics{
    int64 TotalBalance = 1;     // 累计发放金额
    int64 TotalWithdraw = 2;    // 累计成功提现金额
    int64 AvailableBalance = 3; // 累计余额
    int64 AwardInDate = 4;      // 今日发放金额
    int64 WithdrawInDate = 5;   // 今日提现金额
    int64 AvgAward = 6;         // 师父累计平均收益
    int64 AvgAwardInDate = 7;   // 师父今日平均收益
}
message GetActivityStatisticsResp{
    ActivityStatistics statistics = 1;
}



message RecordPlayRoomReq {
    uint32 room_owner_uid = 1;//房主
    repeated uint32 player_list = 2;//在线房间的用户信息
    int64 timestamp = 3;//上报的时间戳
    string extra = 4;//备注信息
}

message RecordPlayRoomResp {
}

message CheckUserTypeListReq {
    repeated uint32 uid_list  =1;
}

message UserTypeChecker {
    uint32 user_id = 1;
    uint32 user_type = 2; //0 普通用户 1 是师傅 2是徒弟 
}

message CheckUserTypeListResp {
    repeated UserTypeChecker checkers = 1;
}