syntax = "proto3";
package real_name_black_list;
option go_package = "golang.52tt.com/protocol/services/real-name-black-list";


service RealNameBlackListServer{
  rpc GetBlackList(GetBlackListReq)returns(GetBlackListResp){

  };
  rpc IsBlackList(IsBlackListReq)returns(IsBlackListResp){

  };
  rpc AddBlackList(AddBlackListReq)returns(AddBlackListResp){

  };
  rpc DelBlackList(DelBlackListReq)returns(DelBlackListResp){

  };
  rpc AddSuspectedBlackList(AddSuspectedBlackListReq)returns(AddSuspectedBlackListResp){

  };
  rpc GetSuspectedBlackList(GetSuspectedBlackListReq)returns(GetSuspectedBlackListResp){

  };

}

message BlackListInfo{
  string id_number = 1; //身份证号
  string reason = 2; //封禁原因
  uint32 begin_time = 3; //开始时间
  uint32 end_time = 4; //结束时间
  repeated uint32 uid = 5; //关联的uid
  bool ok = 6; //身份证号是否处于黑名单列表中,仅获取黑名单时使用
}

message GetBlackListReq{
  uint32 page = 1; //页数，默认0则返回1-10条
  uint32 page_size = 2; //每页返回数量。默认为10。
  repeated uint32 uid = 3; //uid
  repeated string id_number = 4; //身份证号
}

message GetBlackListResp{
  repeated BlackListInfo info = 1; //黑名单信息，可重复
  uint32 total = 2; // 黑名单总数
}

message IsBlackListReq{
  string id_number = 1; //身份证号
}

message IsBlackListResp{
  bool ok = 1; //是否处于黑名单
  BlackListInfo info = 2; //黑名单信息
}


message AddBlackListReq{
  repeated BlackListInfo info = 1; //黑名单信息
}

message AddBlackListResp{
}


message DelBlackListReq{
  repeated string id_number = 1; //身份证号，可批量
}

message DelBlackListResp{
}

message AddSuspectedBlackListReq{
  SuspectedNote note = 1;  //疑似黑名单记录
}

message AddSuspectedBlackListResp{
}

//疑似黑名单记录
message SuspectedNote{
  string id_number = 1; //身份证号
  uint32 time = 2; //记录时间
  string ttid = 3; //TTid
  string nickname = 4; //用户昵称
  string device_id = 5; //设备号
  uint32 uid = 6; //Uid
}

message GetSuspectedBlackListReq{
  repeated string id_number = 1;  //身份证号，留空则查询全部记录
  uint32 page = 2; //页数，默认0则返回1-10条
  repeated uint32 uid = 3;  //uid，留空则查询全部记录
  uint32 page_size = 4; //每页返回数量。默认为10。
}

message GetSuspectedBlackListResp{
  repeated SuspectedNote note = 1;  //疑似黑名单记录
  uint32 total = 2;  //疑似黑名单记录总数
}



