syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/super-star-protector";
package super_star_protector;


service SuperStarProtector {
    rpc GetAllStarList (GetAllStarListReq) returns (GetAllStarListResp) {
    }
}

message GetAllStarListReq{
}

//业务类型
enum BusinessType{
	  NONE     = 0;
    COMMON   = 1;
}
message StarItem {
	uint32   type            =  1 ;
	repeated uint32 uid_list =  2 ;
	repeated uint32 cid_list =  3 ;
}
message GetAllStarListResp {
	repeated StarItem start_list = 1 ;
}