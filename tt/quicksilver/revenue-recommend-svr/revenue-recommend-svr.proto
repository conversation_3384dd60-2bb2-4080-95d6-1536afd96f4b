syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/revenue-recommend-svr";

package revenue_recommend_svr;

service RevenueRecommendService {
    // 获取ugc的推荐营收房
    rpc GetRevenueRecList(GetRevenueRecListReq) returns(GetRevenueRecListResp) {}

    // 获取用户营收推荐内容
    rpc GetUserRevenueRec(GetUserRevenueRecReq) returns(GetUserRevenueRecResp) {}

    // 添加顶部浮窗推荐跟随信息
    rpc AddTopOverlayFollowInfo(AddTopOverlayFollowInfoReq) returns(AddTopOverlayFollowInfoResp){}
}

message RevenueRecInfo {
   uint32 marshal_type = 1; // 字段类型 ugc推荐需要用到 see ga.channel_play.MarshalType
   bytes data = 2; // 数据 see revenue_recommend_logic.proto RevenueRecInfo
   bool is_show = 3;  // 是否展示 true才展示
}


// 获取营收推荐列表
message GetRevenueRecListReq {
   // 请求来源
   enum ReqSource
   {
      Req_Source_Invalid = 0;  // 无效
      Req_Source_Ugc_Recommend = 1;  // ugc首页推荐
      Req_Source_Ugc_King = 2;  // ugc首页游戏tab请求
   }

   uint32 req_source = 1; // see ReqSource
   uint32 uid = 2;  
   uint32 count = 3;  // 请求的数量
   uint32 tab_id = 4;  // 游戏tabId
}
message GetRevenueRecListResp {
   repeated RevenueRecInfo info_list = 1;
   repeated uint32 cid_list = 2;  // 推荐的房间id列表
} 


// 获取用户营收推荐内容
message GetUserRevenueRecReq {
   // 推荐内容类型
   enum RecType
   {
      Req_Type_Invalid = 0;  // 无效
      Req_Type_Top_Over_Lay = 1;  // 顶部浮窗 see channel_recommend_logic_.proto ChannelTopOverLay 
   }

   uint32 rec_type = 1; 
   uint32 uid = 2; 
   uint32 refresh_cnt = 3; // 请求下发次数
   map<uint32,uint32> map_type_cnt = 4;   // 请求下发次数，客户端本地存储 type see channel_recommend_logic_.proto EOverLayType
}

message GetUserRevenueRecResp {
   bytes rec_info = 1; 
   uint32 interval_ts = 2;  // 下次查询间隔时间，单位秒
}


message AddTopOverlayFollowInfoReq {
   uint32 uid = 1;
   uint32 cid = 2;
   uint32 follow_uid = 3;
   string follower_txt = 4;
}
message AddTopOverlayFollowInfoResp {

}