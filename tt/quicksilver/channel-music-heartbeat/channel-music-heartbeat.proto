syntax = "proto3";

package channel_music_heartbeat;
option go_package = "golang.52tt.com/protocol/services/channel_music_heartbeat";

service ChannelMusicHeartbeat {
//    rpc ChannelMusicHeartBeat(ChannelMusicHeartBeatRequest) returns (ChannelMusicHeartBeatResponse);
    rpc RefreshChannelHeartbeatTime(RefreshChannelHeartbeatTimeRequest) returns (RefreshChannelHeartbeatTimeResponse);
    rpc RemoveChannelHeartbeat(RemoveChannelHeartbeatRequest) returns (RemoveChannelHeartbeatResponse);
    rpc GetHeartbeatTimeoutCount(GetHeartbeatTimeoutCountRequest) returns (GetHeartbeatTimeoutCountResponse);
    rpc ClearHeartbeatTimeoutCount(ClearHeartbeatTimeoutCountRequest) returns (ClearHeartbeatTimeoutCountResponse);
}

//message ChannelMusicHeartBeatRequest {
//    uint32 uid = 1;
//    uint32 channel_id = 2;
//    int64 music_key = 3;
//    uint32 volume = 4;
//    uint32 percent = 5; // 播放进度,最大100
//    uint32 client_event = 6;  //客户端发过来的事件信息
//}
//
//message ChannelMusicHeartBeatResponse {
//    uint32 uid = 1;
//    uint32 channel_id = 2;
//    int64 music_id = 3;
//    uint32 volume = 4;
//}

message RefreshChannelHeartbeatTimeRequest {
    uint32 channel_id = 1;
    bool ignore_old_heartbeat = 2; // 是否忽略更新旧心跳存储，迁移兼容时期使用，全量后废弃，默认false即刷新旧存储(全量后通过配置关闭旧存储的更新)
}

message RefreshChannelHeartbeatTimeResponse {
}

message RemoveChannelHeartbeatRequest {
    uint32 set_id = 1;
    repeated string member_list = 2; // cid列表
    bool ignore_old_heartbeat = 3; // 是否忽略更新旧心跳存储，迁移兼容时期使用，全量后废弃，默认false即刷新旧存储(全量后通过配置关闭旧存储的更新)
}

message RemoveChannelHeartbeatResponse {
}

message GetHeartbeatTimeoutCountRequest {
    uint32 channel_id = 1;
}

message GetHeartbeatTimeoutCountResponse {
    uint32 count = 1;
}

message ClearHeartbeatTimeoutCountRequest {
    uint32 channel_id = 1;
}

message ClearHeartbeatTimeoutCountResponse {
}
