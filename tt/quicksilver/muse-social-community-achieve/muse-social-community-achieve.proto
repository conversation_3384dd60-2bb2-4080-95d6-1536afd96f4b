syntax = "proto3";

package muse_social_community_achieve;
option go_package = "golang.52tt.com/protocol/services/muse-social-community-achieve";

// buf:lint:ignore SERVICE_PASCAL_CASE
service muse_social_community_achieve {
  rpc GetLevelDetail(GetLevelDetailRequest)returns(GetLevelDetailResponse);
  rpc CheckIn(CheckInRequest)returns(CheckInResponse);
  rpc GetCheckInSimple(GetCheckInSimpleRequest)returns(GetCheckInSimpleResponse);
  rpc GetSocialCommunityUpdateLevelTip(GetSocialCommunityUpdateLevelTipRequest)returns(GetSocialCommunityUpdateLevelTipResponse);
  rpc GetRightMicList(GetRightMicListRequest)returns(GetRightMicListResponse);
  rpc GetRightMicTabList(GetRightMicTabListRequest)returns(GetRightMicTabListResponse);
  rpc SetChannelCurrentMic(SetChannelCurrentMicRequest)returns(SetChannelCurrentMicResponse);
  rpc GetChannelCurrentMic(GetChannelCurrentMicRequest)returns(GetChannelCurrentMicResponse);
  rpc ClearContentTaskInteraction(ClearContentTaskInteractionRequest)returns(ClearContentTaskInteractionResponse);
  rpc ClearExpireTaskRecord(ClearExpireTaskRecordRequest)returns(ClearExpireTaskRecordResponse);
  //新增房间用户在房停留任务处理
  rpc AddChannelUserStayTask(AddChannelUserStayTaskRequest)returns(AddChannelUserStayTaskResponse);

  rpc BatchDoBehaviorTask(BatchDoBehaviorTaskRequest)returns(BatchDoBehaviorTaskResponse);

  rpc BatchDoStayTask(BatchDoStayTaskRequest)returns(BatchDoStayTaskResponse);

  rpc TestPrivateChatTask(TestPrivateChatTaskRequest)returns(TestPrivateChatTaskResponse);
}

message ClearExpireTaskRecordRequest{

}

message ClearExpireTaskRecordResponse{

}

message SetChannelCurrentMicRequest{
    uint32 channel_id=1;
    int32 mic_number=2;
    uint32 level=3;
}

message SetChannelCurrentMicResponse{

}

message GetChannelCurrentMicRequest{
    uint32 channel_id=1;
}

message GetChannelCurrentMicResponse{
  int32 mic_number=1;
  uint32 level=2;
}

message GetLevelDetailRequest{
  string social_community_id = 1;
  uint32 uid = 2;
  uint32 market_id = 3;
}

message GetLevelDetailResponse{
  string social_community_id = 1;
  string social_community_name = 2;
  string social_community_logo = 3;
  SocialCommunityLevelCard level_card = 4;
  SocialCommunityRightGroup right_group = 5;
  repeated SocialCommunityTaskGroup task_groups = 6;
  BrandMember member = 7;
  uint32 professionalism = 8;
}

message BrandMember{
  uint32 uid = 1;
  string role_text = 2;
  BrandMemberRole role = 3;
}

enum BrandMemberRole{
  Brand_Role_None = 0;
  Brand_Role_Captain = 1;//主理人
  Brand_Role_Kernel = 2;//核心成员
  Brand_Role_Vice_Captain = 3;//副主理人
  Brand_Producer = 4;//制作人
  Brand_Fans = 5;//粉丝
}

message SocialCommunityRightGroup{
  string title = 1;
  repeated SocialCommunityRight social_community_rights = 2;
}

message SocialCommunityLevelCard {
  uint32 level = 1;
  int64 cur_exp = 2;
  int64 next_level_exp = 3;
  string account = 4;
  int64 user_today_exp = 5;
  int64 user_total_exp = 6;
}

message SocialCommunityRight {
  string logo = 1;
  string title = 2;
  string desc = 3;
  SocialCommunityRightStatus status = 4;//1-生效中，0-上锁中
  bool new_flag = 5;
  string tips = 6;
}

enum SocialCommunityRightStatus{
  Status_Lock = 0;
  Status_Valid = 1;
}

message SocialCommunityTaskGroup{
  string title = 1;
  repeated SocialCommunityTask tasks = 7;
}

enum TaskViewType{
  TASK_VIEW_TYPE_NORMAL = 0;
  TASK_VIEW_TYPE_CHECK_IN = 1;
  TASK_VIEW_TYPE_STEP = 2;
}

enum TaskStatus{
  TASK_STATUS_DOING = 0;
  TASK_STATUS_DONE = 1;
}

message SocialCommunityTask {
  TaskViewType view_type = 1;//0-普通类型，1-签到样式，2-step类型

  oneof social_community_task_view{
    SocialCommunityNormalTaskView normal_task_view = 2;
    SocialCommunityCheckInTaskView check_in_task_view = 3;
    SocialCommunityStepTaskView step_task_view = 4;
  }

  TaskStatus status = 5;//0-未完成，1-已完成
  ActionType action_type = 6;//0-不需要点击，1-url,2-check_in
  string action_url = 7;
  repeated string bg_colors = 8;
  repeated  string button_colors = 9;
}

enum ActionType{
  ActionTypeNone = 0;
  ActionTypeUrl = 1;
  ActionTypeCheckIn = 2;
}

message SocialCommunityNormalTaskView {
  int64 award_exp = 1;//奖励经验值，用于左侧图标显示
  string title = 2;
  string desc = 3;
  string active_desc = 4;
  string button_text = 5;
}

message SocialCommunityCheckInTaskView {
  int64 award_exp = 1;//奖励经验值，用于左侧图标显示
  string title = 2;
  string desc = 3;
  string active_desc = 4;
  map<uint32, string> status_button_text = 5;//已完成，和未完成文案
}

message SocialCommunityStepTaskView {
  repeated SocialCommunityStep steps = 1;
  string title = 2;
  string button_text = 3;
  int64 score = 4;//目前获得值
  string desc = 5;
  string tips = 6;
}

message SocialCommunityStep{
  int64 award_exp = 1;//奖励经验值
  string desc = 2;
  int64 score = 3;//到这step需要值
}

message CheckInRequest{
  string social_community_id = 1;
}

message CheckInResponse{

}

message GetCheckInSimpleRequest{
    string social_community_id=1;
}

message GetCheckInSimpleResponse{
    uint32 check_in_status=1;
    uint32 check_in_exp=2;
}

message GetSocialCommunityUpdateLevelTipRequest{
    string social_community_id=1;
}

message GetSocialCommunityUpdateLevelTipResponse{
  string social_community_id=1;
  string social_community_name=2;
  string logo=3;
  SocialCommunityRightGroup right_group = 4;
  uint32 captain=5;
  uint32 level=6;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetRightMicListRequest {
  string social_community_id = 1;
  uint32 tabId = 2;
}

message GetRightMicListResponse {
  repeated uint32 mic_list = 1;
}

message GetRightMicTabListRequest {
}

message GetRightMicTabListResponse {
  repeated uint32 tab_list = 1;
}

message ClearContentTaskInteractionRequest {
  uint32 uid = 1;
  string social_community_id = 2;
  bool clear_interaction = 3;
  bool clear_task = 4;
}

message ClearContentTaskInteractionResponse {
}

message AddChannelUserStayTaskRequest{
  uint32 channel_id=1;
  uint32 owner_uid = 2;
}

message AddChannelUserStayTaskResponse{

}

message BehaviorTask {
  uint32 uid =1;
  string social_community_id =2;
  string behavior_id =3;
  uint32 group_id =4;

}
message BatchDoBehaviorTaskRequest{
    repeated BehaviorTask tasks =1;
}

message BatchDoBehaviorTaskResponse{

}

message StayTask {
  repeated uint32 uids =1;
  string social_community_id =2;
}

message BatchDoStayTaskRequest{
  uint32 owner_uid =1;
  repeated StayTask tasks =2;
  string behavior_id =3;
  uint32 stay_duration_min=4;

}

message  BatchDoStayTaskResponse{
}


message TestPrivateChatTaskRequest{
  // 通用IM事件
  message CommImEvent {
    enum EventType {
      EVENT_TYPE_UNSPECIFIED = 0;
      EVENT_TYPE_GROUP_IM = 1; // 群聊
      EVENT_TYPE_1V1_SEND_IM = 2; // 私聊 发送者
      EVENT_TYPE_1V1_RECV_IM = 3; // 私聊 接收者
      EVENT_TYPE_SENDIM_BOT = 4; // 运营后台助手推送
    }

    enum DataType {
      DATA_TYPE_UNSPECIFIED = 0;
      DATA_TYPE_TIMELINE_MSG = 1; // see Timeline.TimelineMsg
      DATA_TYPE_BATCH_IM_MSG = 2; // see Timeline.BatchImMsg
    }

    EventType event_type = 1; // 事件类型
    DataType data_type = 2; // 数据类型
    bytes data = 3;
    int64 create_ts = 4; // 事件创建时间戳
  }
    CommImEvent comm_im_event =1;
}

message TestPrivateChatTaskResponse{}