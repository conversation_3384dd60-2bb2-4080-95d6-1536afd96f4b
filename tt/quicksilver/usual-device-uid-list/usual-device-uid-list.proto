syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/usual-device-uid-list";
package usual_device_uid_list;

service UsualDeviceUidList {
  rpc InAuthWhiteList (InAuthWhiteListReq) returns (InAuthWhiteListResp) {}
  rpc UpdateAuthWhiteList (UpdateAuthWhiteListReq) returns (UpdateAuthWhiteListResp) {}
}

message InAuthWhiteListReq {
  uint64 uid = 1;
}

message InAuthWhiteListResp {
  bool in = 1;
}

message UpdateAuthWhiteListReq {
  bool is_del = 1;
  repeated uint64 uid_list = 2;
  string operator = 3;
  string reason = 4;
  uint64 update_at = 5;
}

message UpdateAuthWhiteListResp {

}