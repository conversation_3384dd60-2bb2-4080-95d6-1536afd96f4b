syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/black-white-box-supervise-subscriber";

package black_white_box_supervise_subscriber;

message SendFeiShuMessageReq {
  uint64 uid = 1;
  bytes device_id = 2;
  string phone = 3;

  uint32 terminal_type = 5;
  uint32 market_id = 6;
  uint32 client_version = 7;
  string client_ip = 8;
  string idfv = 9;
  string imei = 10;
  string oaid = 11;

  repeated string content = 12;
}

message SendFeiShuMessageResp {

}

service BlackWhiteBoxSuperviseSubscriber {

  rpc SendFeiShuMessage(SendFeiShuMessageReq) returns (SendFeiShuMessageResp) {}

}