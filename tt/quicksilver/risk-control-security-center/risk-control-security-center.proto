syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/risk-control-security-center";
package risk_control_security_center;

service RiskControlSecurityCenter {
  // 拉取所有帖子
  rpc GetAllSecurityCenterPost(GetAllSecurityCenterPostReq) returns (GetAllSecurityCenterPostResp) {}

  // 运营后台
  rpc GetAllConfigTab(GetAllConfigTabReq) returns(GetAllConfigTabResp) {}
  rpc UpdateSecurityCenterPost(UpdateSecurityCenterPostReq) returns (UpdateSecurityCenterPostResp) {}
  rpc GetSecurityCenterPost(GetSecurityCenterPostReq) returns (GetSecurityCenterPostResp) {}

  // 配置 tab 的接口，tab 相对固定，到时候只需要调用一次（与运营后台无关）
  rpc UpdateConfigTab(UpdateConfigTabReq) returns(UpdateConfigTabResp) {}
}

message SecurityCenterPostInfo {
  string post_id = 1;      // 帖子的id,创建时传空字符 ""
  string post_title = 2;  // 帖子的标题
  string post_cover_url = 3;  //帖子封面
  string post_content_url = 4; //帖子内容地址
  uint32 create_at = 5;    //创建时间
}

message SecurityCenterPostRespInfo {
  uint32 tab_id = 1;
  string tab_name = 2;
  repeated SecurityCenterPostInfo post_list = 3;
}

message GetAllSecurityCenterPostReq {
  uint32 app_id = 1;        // 哪个马甲包 app
  string search_text = 2;   // 搜索字段，"" 空字符串返回全部
}

message GetAllSecurityCenterPostResp {
  repeated SecurityCenterPostRespInfo post_resp_list = 1;    // tabName 帖子分类下的帖子列表
}

// 运营后台

message GetSecurityCenterPostInfo {
  string post_id = 1;      // 帖子的id,创建时传空字符 ""
  string post_title = 2;  // 帖子的标题
  repeated uint32 app_id_list = 3;  // 用于哪个app,多选
  string tab_name = 4; // 帖子分类 单选
  string post_cover_url = 5;  //帖子封面
  string post_content_url = 6; //帖子内容地址
  string updated_people = 7; // 更新者
  uint32 create_at = 8;  // 创建时间
  uint32 update_at = 9; // 更新时间
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetSecurityCenterPostReq {
  uint32 offset = 1;
  uint32 limit = 2;
  uint32 tabId = 3;
  string post_title = 4;
  repeated uint32 app_id_list = 5;
}

message GetSecurityCenterPostResp {
  repeated GetSecurityCenterPostInfo post_info_list = 1;
  uint32 total = 2;
}

message UpdateSecurityCenterPostReq {
  string post_id = 1;      // 帖子的id,创建时传空字符 ""
  string post_title = 2;  // 帖子的标题
  repeated uint32 app_id_list = 3;  // 用于哪个app,多选
  uint32 tab_id = 4; // 帖子分类 单选
  string post_cover_url = 5;  //帖子封面
  string post_content_url = 6; //帖子内容地址
  bool is_delete = 7;          //是否删除
  string updated_people = 8; // 更新者
}

message UpdateSecurityCenterPostResp {

}

message GetAllConfigTabReq {

}

message ConfigTabInfo {
  uint32 tab_id = 1;
  string tab_name = 2;
}

message GetAllConfigTabResp {
  repeated ConfigTabInfo tab_list = 1;
}

message UpdateConfigTabReq {
  uint32 tab_id = 1;
  string tab_name = 2;
  bool is_delete = 3;
}

message UpdateConfigTabResp {

}