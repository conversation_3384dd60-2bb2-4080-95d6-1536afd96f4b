syntax = "proto3";
package tt_regulation_config;

option go_package = "golang.52tt.com/protocol/services/tt-regulation-config";



service TtRegulationConfig {
  //搜索白名单后台相关
  rpc SwitchSearchWhiteList(SwitchSearchWhiteListReq) returns (SwitchSearchWhiteListResp){
  }
  rpc GetSearchWhiteListStatus(GetSearchWhiteListStatusReq) returns (GetSearchWhiteListStatusResp){
  }
  rpc BatchAddSearchWhiteList(BatchAddSearchWhiteListReq) returns (BatchAddSearchWhiteListResp){
  }
  rpc BatchDelSearchWhiteList(BatchDelSearchWhiteListReq) returns (BatchDelSearchWhiteListResp){
  }
  rpc GetSearchWhiteList(GetSearchWhiteListReq) returns (GetSearchWhiteListResp){
  }
  rpc ExportHotWord(ExportHotWordReq) returns (ExportHotWordResp){
  }

  rpc AddSearchCount(AddSearchCountReq) returns(AddSearchCountResp){
  }
  rpc GetSearchCount(GetSearchCountReq) returns(GetSearchCountResp){
  }

  rpc IsWordInWhiteList(IsWordInWhiteListReq) returns (IsWordInWhiteListResp){
  }

  // 给搜索调用的接口，一次性完成增加搜索次数、返回搜索次数、返回白名单情况的功能
  rpc AuthSearchEvent(AuthSearchEventReq) returns(AuthSearchEventResp){

  }

  // 获取用户受控信息
  rpc GetUserControlInfo(GetUserControlInfoReq) returns (GetUserControlInfoResp) {
  }

  // 解除受控用户的受控状态
  rpc BatRemoveUserControlled(BatRemoveUserControlledReq) returns (BatRemoveUserControlledResp) {
  }

   // 批量获取用户受控信息
  rpc BatGetUserControlInfo(BatGetUserControlInfoReq) returns (BatGetUserControlInfoResp) {
  }

  // 设置用户受控信息
  rpc SetUserControlInfo(SetUserControlInfoReq) returns (SetUserControlInfoResp) {
  }

}

// 白名单开关，true 开 false 关
message SwitchSearchWhiteListReq
{
  bool white_list_status = 1;
}

// 白名单开关，true 开 false 关
message SwitchSearchWhiteListResp
{
}

// 获取白名单开关状态
message GetSearchWhiteListStatusReq
{
}

message GetSearchWhiteListStatusResp
{
  bool white_list_status = 1;
}

// 添加白名单
// 不用填create_time，begin_time、end_time都为0则为永久白名单
message BatchAddSearchWhiteListReq{
  repeated SearchWhiteListItem item_list = 1;
}

message BatchAddSearchWhiteListResp{
  repeated SearchWhiteListFailResult fail_list = 1;
}

message SearchWhiteListItem{
  uint32 item_id = 1;
  string item_content = 2;
  string operater_id = 3;
  uint32 create_time = 4;
  uint32 begin_time = 5;
  uint32 end_time = 6;
}

message SearchWhiteListFailResult{
  uint32 item_id = 1;
  string item_content = 2;
  string fail_reason = 3;
}

// 删除白名单
// 只填id即可
message BatchDelSearchWhiteListReq{
  repeated SearchWhiteListItem item_list = 1;
}

message BatchDelSearchWhiteListResp{
  repeated SearchWhiteListFailResult fail_list = 1;
}

// 获取白名单
// content 要搜索的词汇
// begin_time、end_time 时间范围，都为0无范围
// page 、count 页码 每页个数

message GetSearchWhiteListReq{
  string content = 1;
  uint32 begin_time = 2;
  uint32 end_time = 3;
  uint32 page = 4;
  uint32 count = 5;
}

message GetSearchWhiteListResp{
  repeated SearchWhiteListItem item_list = 1;
  uint32 total_count = 2;
}

// 导出热词
message ExportHotWordReq{
  uint32 begin_time = 1;
  uint32 end_time = 2;
}

message ExportHotWordResp{
  repeated string word_list = 1;
}


enum SearchType{
  UNKNOWN = 0;
  USER = 1;
  CHANNEL = 2;
  GUILD = 3;
}

// 增加搜索次数计数
message AddSearchCountReq{
  uint32 uid = 1;
  uint32 search_type = 2;
}

message AddSearchCountResp{

}


// 获取次数计数
message GetSearchCountReq{
  uint32 uid = 1;
  uint32 search_type = 2;
}

message GetSearchCountResp{
  uint32 count = 1;
  bool can_search = 2;
  uint32 result_limit = 3;
}

// 某词是否命中白名单
message IsWordInWhiteListReq{
  uint32 uid = 1;
  string word = 2;
}

message IsWordInWhiteListResp{
  bool in_white_list = 1;
}

// 某词是否命中白名单
message AuthSearchEventReq{
  uint32 uid = 1;
  string word = 2;
  uint32 search_type = 3;
}

message AuthSearchEventResp{
  uint32 count = 1;
  bool can_search = 2;
  uint32 result_limit = 3;
  bool in_white_list = 4;
}

// 枚举定义, 按位递增 0,1,2,4,...
enum ControlType {
  ENUM_NO_CONTROL = 0;  // 无限制
  ENUM_CONTROL_BY_REGISTER_TS = 1;  // 注册时间限制
  ENUM_CONTROL_BY_UNUSAL_DEVICE_LOGIN = 2;  // 在非常用设备登录
  ENUM_CONTROL_BY_UNBIND_PHONE = 4;  // 换绑手机限制
  ENUM_CONTROL_BY_GAMESDK_FIRST_LOGIN = 8;  // 游戏SDK注册的账号首次在TT登录限制
}

message TriggerCondition {
  uint32 type = 1; // see ControlType
  uint32 trigger_cnt = 2;  // 触发次数
}

// 触发条件信息
message TriggerInfo {
  repeated TriggerCondition cond_list = 1; 
}

// 用户受控状态信息
message UserControlInfo {
   uint32 uid = 1;
   bool   is_controlled = 2;  // 是否受控
   TriggerInfo trigger_info = 3; 
}

// 获取用户受控信息
message GetUserControlInfoReq {
   uint32 uid = 1;
}
message GetUserControlInfoResp {
  UserControlInfo info = 1;
}

// 批量获取用户受控信息
message BatGetUserControlInfoReq {
   repeated uint32 uid_list = 1;
}
message BatGetUserControlInfoResp {
   repeated UserControlInfo info_list = 1;
}

// 解除受控用户的受控状态
message BatRemoveUserControlledReq {
   repeated uint32 uid_list = 1;
}
message BatRemoveUserControlledResp{
}

// 设置用户受控信息
message SetUserControlInfoReq {
   uint32 uid = 1;
   uint32 control_type = 2;   //see ControlType
}
message SetUserControlInfoResp {
}



