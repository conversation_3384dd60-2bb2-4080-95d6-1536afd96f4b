syntax = "proto3";

package muse_megaphone_event;

option go_package = "golang.52tt.com/protocol/services/muse-megaphone/event";

enum OptionType {
  OPTION_TYPE_UNSPECIFIED = 0;
  OPTION_TYPE_FRIEND = 1;
  OPTION_TYPE_FANS = 2;
}

message ModifyUserMegaphone {
  string megaphone_id = 1;
  string content = 2;
  uint32 option = 3;
  bool is_del = 4;
}

message BlockUserMegaphone {
  string megaphone_id = 1;
  int64 end_ts = 2;
}

message UserMegaphoneEvent {
  uint32 uid = 1;
  int64 create_at = 2;
  oneof event {
    ModifyUserMegaphone modify_user_megaphone = 3;
    BlockUserMegaphone block_user_megaphone = 4;
  }
}
