syntax = "proto3";

package game_ticket_recharge;
option go_package = "golang.52tt.com/protocol/services/game-ticket-recharge";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";
import "tt/quicksilver/unified-pay-rmb/unified-pay-rmb.proto";

service GameTicketRecharge {
    //消耗T豆，购买礼物包裹，同时赠送游戏券
    rpc DoRecharge(DoRechargeReq) returns (DoRechargeResp){}
    //根据GameType找到游戏券购买的配置
    rpc GetFreebieCfgByGameType(GetFreebieCfgByGameTypeReq) returns (GetFreebieCfgByGameTypeResp){}
    //设置游戏券购买的配置
    rpc SetGameTicketFreebieCfg(SetGameTicketFreebieCfgReq) returns (SetGameTicketFreebieCfgResp){}
    //获取所有游戏券购买的配置
    rpc GetALLFreebieDetailCfg(GetALLFreebieDetailCfgReq) returns (GetALLFreebieDetailCfgResp){}
    //删除游戏券购买的配置
    rpc DelGameTicketFreebieCfg(DelGameTicketFreebieCfgReq) returns (DelGameTicketFreebieCfgResp){}
    //根据freebie_id获取配置
    rpc GetGameTicketFreebieCfg(GetGameTicketFreebieCfgReq) returns (GetGameTicketFreebieCfgResp) {}

    /*********对账接口**********/
    // 发放包裹数据对账
    rpc GetGainPackTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetGainPackOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

    // 发放游戏券数据对账
    rpc GetGainTicketTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetGainTicketOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

    // 使用数据对账
    rpc GetCostTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetCostOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

    // 获取充值配置
    rpc GetMiJingPayConfig(GetMiJingPayConfigReq) returns (GetMiJingPayConfigResp) {}
    // 获取支付渠道
    rpc GetMiJingPayChannel(GetMiJingPayChannelReq) returns (GetMiJingPayChannelResp) {}
    // 下单
    rpc MiJingPay(MiJingPayReq) returns (MiJingPayResp) {}
    // 查询支付结果
    rpc GetMiJingPayResult(GetMiJingPayResultReq) returns (GetMiJingPayResultResp) {}
    // 支付结果回调
    rpc PayRmbResultNotify(PayRmbResultNotifyReq) returns (PayRmbResultNotifyResp) {}
    // 新增充值配置
    rpc AddMiJingPayConfig(AddMiJingPayConfigReq) returns (AddMiJingPayConfigResp) {}
    // 删除充值配置
    rpc DeleteMiJingPayConfig(DeleteMiJingPayConfigReq) returns (DeleteMiJingPayConfigResp) {}
}

// 支付结果回调
message PayRmbResultNotifyReq {
    unified_pay_rmb.RmbPayOrderInfo order_result = 1;
}
message PayRmbResultNotifyResp {}

// 新增充值配置
message AddMiJingPayConfigReq{
    PayOption opt = 1;
}
message AddMiJingPayConfigResp{}

// 删除充值配置
message DeleteMiJingPayConfigReq{
    uint32 opt_id = 1;
}
message DeleteMiJingPayConfigResp{}

// 获取充值配置
message GetMiJingPayConfigReq{
    uint32 os_type = 1;
}
message GetMiJingPayConfigResp{
    repeated PayOption opts = 1;
}

message PayOption{
    uint32 id = 1;
    uint32 os_type = 2;
    string apple_pay_product_id = 3; // ios商品id
    uint32 ticket_id = 4; // 密室券id
    uint32 ticket_num = 5; // 密室券数量
    uint32 t_bean_num = 6; // t豆数量
    string gift_name = 7; // 礼物名称
    string game_ticket_url = 8; // 密室券图
    string gift_url = 9; // 礼物图
    uint32 price = 10; // 金额，单位：分
    uint32 pack_id = 11; // 礼物id
    uint32 pack_num = 12; // 礼物数量
    string ticket_name = 13; // 密室券名称
    string game_tickets_url = 14; // 密室券图标
}

// 获取支付渠道
message GetMiJingPayChannelReq{
    uint32 os_type = 1;
}
message GetMiJingPayChannelResp{
    repeated string pay_channels = 1;
}

// 下单
message MiJingPayReq{
    uint32 uid = 1;
    uint32 opt_id = 2; // 配置id
    string pay_channel = 3;     //下单的渠道  必填
    string bundle_id = 4;     //IOS的produceID(appstore支付必传)
    string product_id = 5;    //IOS的product_id (appstore支付必传)
    string recharge_type = 6; // 充值类型
    string locale = 7; // 地区
}
message MiJingPayResp{
    string token = 1;
    string pay_order_id = 2; // 支付订单号
    string cli_order_title = 3; // 订单标题
    string tsk = 4; // 加密字符串
    string channel_map = 5; // 唤起支付渠道的参数, 用于安卓、IOS、前端使用
    uint32 order_time  = 6; // 下单时间
    string t_pay_order_no = 7; // 第三方订单号
}

// 订单状态
enum MiJingPayStatus{
    MI_JING_PAY_STATUS_UNSPECIFIED = 0;
    MI_JING_PAY_STATUS_PAYING =  1;    // 支付中
    MI_JING_PAY_STATUS_SUCCESS = 2;    // 支付成功
    MI_JING_PAY_STATUS_FAILED = 3;     // 支付失败
}

// 查询支付结果
message GetMiJingPayResultReq{
    string pay_order_id = 1; // 订单号
}
message GetMiJingPayResultResp{
    MiJingPayStatus status = 1; // 订单状态
    repeated MiJingProduct products = 2; // 发放的商品
}
message MiJingProduct{
    string name = 1; // 名称
    string icon = 2; // 图标
    uint32 num = 3; // 数量
}

enum GameType {
    UnknownGameType = 0;
    MysteryRealm = 1; // 谜境
}

// 充值游戏券赠送的礼物配置
message FreebieCfg {
    uint32 freebie_id = 1;
    uint32 pack_id = 2;
    uint32 price = 3;              // 包裹价值(待定义)
    uint32 ticket_id = 4;
    string ticket_name = 5;
    uint32 ticket_num_per_pack = 6; // 购买一个礼物得到 游戏券的数量
    uint32 game_type = 7;          //see GameType
}

message FreebieDetailCfg{
    FreebieCfg config = 1;
    uint32 gift_id = 2;
    uint32 gift_type = 3;
    string gift_name = 4;
    string gift_url = 5;
    uint32 gift_num = 6;
}

// 购买游戏券指定礼物
message DoRechargeReq {
    uint32 uid = 1;
    uint32 freebie_id = 2;
    uint32 num = 3;
    uint32 outside_time = 4;  // 为 0 即为当前时间
    uint32 game_type = 5;  //see GameType
    uint32 cost = 6;
}

message DoRechargeResp {
    uint32 pack_id = 1;     // 发放包裹的id
    uint32 ticket_id = 2;   // 游戏券id
    uint32 ticket_num = 3;  // 发放游戏券数量
    uint32 balance = 4;     // T豆余额
}

// 获取游戏券指定的礼物id
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetFreebieCfgByGameTypeReq{
    uint32 gameType = 1;    // see GameType
}

message GetFreebieCfgByGameTypeResp{
    FreebieDetailCfg config = 1;
    uint32 recharge_limit = 2; // 单次充值限制（T豆）
}

//设置游戏券礼物配置
message SetGameTicketFreebieCfgReq{
    FreebieCfg config = 1;
}
message SetGameTicketFreebieCfgResp{
}

message DelGameTicketFreebieCfgReq{
    uint32 freebie_id = 1;
}

message DelGameTicketFreebieCfgResp{
}

message GetALLFreebieDetailCfgReq{
}

message GetALLFreebieDetailCfgResp{
    repeated FreebieDetailCfg config_list = 1;
}

message GetGameTicketFreebieCfgReq{
    uint32 freebie_id = 1;
}

message GetGameTicketFreebieCfgResp{
    FreebieDetailCfg config = 1;
}