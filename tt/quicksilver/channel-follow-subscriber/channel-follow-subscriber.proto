syntax = "proto3";

package channel_follow_subscriber;
option go_package = "golang.52tt.com/protocol/services/channel-follow-subscriber";

service ChannelFollowSubscriber {
    rpc HandleSvipOnlineEvent(HandleSvipOnlineEventReq) returns (HandleSvipOnlineEventResp) {}
}

// 在线隐身状态
enum OnlineSwitch
{
  ENUM_ONLINE_SWITCH_UNSPECIFIED = 0;
  ENUM_ONLINE_SWITCH_ONLINE = 1; // 在线
  ENUM_ONLINE_SWITCH_STEALTH = 2; // 隐身
}

message SvipOnlineEvent
{
  enum ChangeType
  {
    ENUM_CHANGE_TYPE_UNSPECIFIED = 0;
    ENUM_CHANGE_TYPE_MANUAL = 1; // 手动操作
    ENUM_CHANGE_TYPE_MORROW = 2; // 次日自动结束隐身
    ENUM_CHANGE_TYPE_SVIP_EXPIRED = 3; // SVIP到期结束隐身
  }
  uint32 uid = 1;
  OnlineSwitch online_switch = 2; // 在线状态
  ChangeType change_type = 3; // 变更类型
  int64 change_time = 4; // 变更时间
}

message HandleSvipOnlineEventReq {
    SvipOnlineEvent event = 1;
}

message HandleSvipOnlineEventResp {
}

