syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/tmp-channel-open-game";

package tmp_channel_open_game;

service TmpChannelOpenGame {

    rpc AllocChannel (AllocChannelReq) returns (AllocChannelResp) {

    }

    rpc ReleaseChannel (ReleaseChannelReq) returns (ReleaseChannelResp) {

    }

    //分配游戏位
    //超时时间后未调用 JoinBegin 则视为放弃游戏位
    //调用 JoinBegin 后锁定游戏位
    rpc AllocJoystick (AllocJoystickReq) returns (AllocJoystickResp) {

    }

    rpc RandomAllocJoystick (RandomAllocJoystickReq) returns (RandomAllocJoystickResp) {

    }

    rpc QueryJoystick (QueryJoystickReq) returns (QueryJoystickResp) {

    }

    //获取房间游戏数据
    rpc GetChannelInfo (GetChannelInfoReq) returns (GetChannelInfoResp) {

    }

    //开始加入游戏
    //超时时间后未调用 JoinEnd 则视为放弃游戏位（兼容旧版，超时时间后若用户未准备则视为放弃游戏位）
    rpc JoinBegin (JoinBeginReq) returns (JoinBeginResp) {

    }

    //结束加入游戏
    rpc JoinEnd (JoinEndReq) returns (JoinEndResp) {

    }

    //查询倒计时
    rpc QueryTick (QueryTickReq) returns (QueryTickResp) {

    }

    rpc BatchJoinBegin (BatchJoinBeginReq) returns (BatchJoinBeginResp) {

    }
}

message Joystick {
    enum JoystickStatus {
        IDLE = 0; //未分配
        Allocated = 1; //已分配
        Confirmed = 2; //已确认
    }

    uint32 channel_id = 1; //游戏位所属房间
    uint32 seq = 2; //游戏位序号
    uint32 status = 3; //游戏位状态
    uint32 uid = 4; //游戏位玩家
}

message Channel {
    enum Status {
        IDLE = 0; //空闲
        Playing = 1; //游戏中
        Close = 2; //关闭，房间被回收时的状态
    }

    uint32 channel_id = 1; //房间标识
    uint32 status = 2; //房间状态

    repeated Joystick joystick = 3;
}

message AllocChannelReq {
    uint32 game_id = 1; //游戏标识，用于分配时区分游戏
    string game_mode = 2; //游戏模式，用于分配时区分游戏模式
    string channel_name = 3; //房间名
}
message AllocChannelResp {
    uint32 channel_id = 1;
}

message ReleaseChannelReq {
    uint32 channel_id = 1;
}
message ReleaseChannelResp {
}

message AllocJoystickReq {
    uint32 channel_id = 1; //房间
    uint32 uid = 2; //玩家
}
message AllocJoystickResp {
    Joystick joystick = 1;
}

message RandomAllocJoystickReq {
    uint32 game_id = 1; //游戏标识，用于分配时区分游戏
    string game_mode = 2; //游戏模式，用于分配时区分游戏模式
    string channel_name = 3; //房间名

    uint32 uid = 4; //玩家
}
message RandomAllocJoystickResp {
    Joystick joystick = 1;
}

message QueryJoystickReq {
    uint32 channel_id = 1;
    uint32 uid = 2;
}
message QueryJoystickResp {
    Joystick joystick = 1;
}

message GetChannelInfoReq {
    uint32 channel_id = 1;
}
message GetChannelInfoResp {
    Channel info = 1;
}

message JoinBeginReq {
    enum SceneType {
        DEFAULT = 0;
        READY_AND_LOAD = 1;
    }

    uint32 channel_id = 1;
    uint32 game_id = 3;
    uint32 scene = 4;

    uint32 uid = 2;
}
message JoinBeginResp {
    uint32 seq = 1;
}

message BatchJoinBeginReq {
    uint32 channel_id = 1;
    uint32 game_id = 3;
    uint32 scene = 4;

    repeated uint32 uid = 2;
}
message BatchJoinBeginResp {

}

message JoinEndReq {
    uint32 channel_id = 1;
    uint32 uid = 2;
    uint32 game_id = 3;
}
message JoinEndResp {

}

message Tick {
    enum TickType {
        UNKNOWN = 0;
        READY = 1;
        LOADING = 2;
    }

    uint32 tick_type = 1;
    uint32 rest_time = 2;
}

message QueryTickReq {
    uint32 channel_id = 1;
    uint32 uid = 2;
    int64 load_seq = 3;
}

message QueryTickResp {
    repeated Tick ticks = 1;
}