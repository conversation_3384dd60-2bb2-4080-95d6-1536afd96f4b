syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/music-nest-vote-watering";
package music_nest_vote_watering;

service MusicNestVoteWatering {
  // 创建投票灌水
  rpc CreateVoteWatering (CreateVoteWateringReq) returns (CreateVoteWateringResp) {}

  // 获取投票灌水记录
  rpc ListVoteWateringInfo (ListVoteWateringInfoReq) returns (ListVoteWateringInfoResp) {}
}

message CreateVoteWateringReq {
  uint32 channel_id = 1;
  uint32 channel_display_id = 2;
  uint32 vote_cnt = 3; // 投票数
  uint32 interval = 4; // 完成投票用时
  uint32 uid = 5; // 被投票的指定uid
  string ttid = 6; // 被投票的ttid
  string nick_name = 7; // 被投票者的昵称
  string maintainer = 8; // 运营人员维护者
  string channel_view_id=9;//原display_id
}

message CreateVoteWateringResp {
}

message ListVoteWateringInfoReq {
  uint32 channel_id = 1;  // 为0的时候，默认返回24小时的灌水投票;其他值默认返回近一月的记录流水
}

message ListVoteWateringInfoResp {
  message WateringInfo {
    uint32 channel_id = 1;
    uint32 channel_display_id = 2;
    uint32 vote_cnt = 3; // 投票数
    uint32 interval = 4; // 完成投票用时
    uint32 uid = 5; // 被投票的指定uid
    string ttid = 6; // 被投票的ttid
    string nick_name = 7; // 被投票者的昵称
    string maintainer = 8; // 运营人员维护者
    uint32 state = 9; // 灌水状态
    int64 operate_time  = 10; //操作的时间戳
    string channel_view_id=11;//原display_id
  }
  repeated WateringInfo watering_infos = 1;
}



