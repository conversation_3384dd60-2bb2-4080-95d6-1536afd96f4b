syntax = "proto3";

package authority_permission;

option go_package = "golang.52tt.com/protocol/services/authority-permission";

service PermissionServer{
    // buf:lint:ignore RPC_PASCAL_CASE
    rpc getAppId(appIdReq) returns (appId){}
    // buf:lint:ignore RPC_PASCAL_CASE
    rpc watch(appIdReq) returns(stream appIdResp){}
}

service PermissionMngServer{
    // buf:lint:ignore RPC_PASCAL_CASE
    rpc getAll(appIdReq) returns (appIdResp){}
    // buf:lint:ignore RPC_PASCAL_CASE
    rpc createAppId(appIdGenReq) returns (appId){}
    //rpc deleteAppId(appId) returns (appIdsResp){}
    // buf:lint:ignore RPC_PASCAL_CASE
    rpc createSecret(secretGenReq) returns(secret){}
    // buf:lint:ignore RPC_PASCAL_CASE
    rpc deleteSecret(secret) returns (secret){}
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message appIdReq{
    string appId = 1;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message appIdGenReq{
    string des = 1;
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum updateType{
    ReplaceAll = 0;
    AddOrUpdate = 1;
    Reduce = 2;
}
// buf:lint:ignore MESSAGE_PASCAL_CASE
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message appIdResp{
    appId appId = 1;
    updateType type = 2;
}
// buf:lint:ignore MESSAGE_PASCAL_CASE
message appIdsResp{
    repeated appId appids = 1;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message secretGenReq{
    appId appId = 1;
    repeated string permissions = 2;
    string des = 3;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message appId{
    uint64 id =1;
    string appId = 2;
    string des = 3;
    repeated secret secrets = 4;

    bool enable = 5;
    uint64 created_at =6;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message secret{
    uint64 id = 1;
    string appId = 2;
    string secret = 3;
    string des = 4;
    uint64 expired_at = 5;
    uint64 created_at = 6;
    repeated string permissions = 7;
}
