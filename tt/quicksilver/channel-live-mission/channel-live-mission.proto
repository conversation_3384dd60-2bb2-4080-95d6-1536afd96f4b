syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-live-mission";
package channel_live_mission;
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service ChannelLiveMission {

    rpc GetUserMission (GetUserMissionReq) returns (GetUserMissionResp) {
    }

    rpc HandleUserMission (HandleUserMissionReq) returns (HandleUserMissionResp) {
    }

    rpc GetFansMission (GetFansMissionReq) returns (GetFansMissionResp) {
    }

    rpc HandleFansMission (HandleFansMissionReq) returns (HandleFansMissionResp) {
    }

    rpc GetActorMission (GetActorMissionReq) returns (GetActorMissionResp) {
    }

    rpc IncrActorLiveTimeCnt (IncrActorLiveTimeCntReq) returns (IncrActorLiveTimeCntResp) {
    }

    rpc SwitchTimeUserMissionTs (SwitchTimeUserMissionTsReq) returns (SwitchTimeUserMissionTsResp) {
    }
    
    //rpc GetProcessActorMissionDesc (GetProcessActorMissionDescReq) returns (GetProcessActorMissionDescResp) {
    //}

    /*********对账接口V2**********/
    // 发放主播奖励积分对账
    rpc GetAwardAnchorScoreCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetAwardAnchorScoreOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
    // 补单 放在上游
    rpc FixAnchorScoreByOrderId(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}
    /*********对账接口V2**********/


    rpc GrantLiveMissionAward(GrantLiveMissionAwardReq) returns (GrantLiveMissionAwardResp) {}

    //修改某个粉丝任务的更新时间
    rpc UpdateFansMissionTime(UpdateFansMissionTimeReq) returns (UpdateFansMissionTimeRsp){}

    //触发定时任务
    rpc TriggerTimer(TriggerTimerReq) returns (TriggerTimerResp) {}
}

enum MissionStatus {
    Processing = 0; // 进行中
    Finish = 1;     // 完成
}

//直播任务相关
message ChannelLiveMissionInfo {
    enum MissionType {
        Unknown = 0;
        Daily = 1;          // 每日任务
        Accumulative = 2;   // 累计任务（一次性，完成后便结束）
        Continuation = 3;   // 连续任务（完成后重新开始）
    }

    // 点击‘去完成’的操作类型
    enum MissionOperType {
        None = 0;
        GiftRack = 1;           // 弹出礼物架
        ShareLiveChannel = 2;   // 分享直播间
        JumpFansGift = 3;       // 弹出礼物架粉丝团页
    }

    uint32 mission_id = 1;
    string mission_desc = 2;
    uint32 mission_type = 3;
    string mission_name = 4;
    string mission_icon_url = 5;
    int64 update_time = 6;
    uint32 status = 7;      // 任务状态 see MissionStatus
    uint32 finish_cnt = 8;  // 当前完成次数
    uint32 goal_cnt = 9;    // 完成次数的上限 （任务进度 = finish_cnt/goal_cnt）

    uint32 actor_uid = 10;  // 关注的主播的UID，仅粉丝任务有效
    uint32 oper_type = 11;  // 粉丝任务点击‘去完成’的操作，仅粉丝任务有效 see MissionOperType

    uint32 award_num = 12;     //（用户任务）奖励礼物个数 （粉丝任务）任务完成奖励的亲密值
    string progress_desc = 13; //（用户任务）任务进度描述 （粉丝任务）进度描述
    string knight_desc = 14;  //(粉丝任务) 开通骑士团的任务描述，为空不用展示
    string bubble_desc = 15;  //(粉丝任务) 气泡描述，为空不用展示？
}

message GetUserMissionReq {
    uint32 uid = 1;
}

message GetUserMissionResp {
    repeated ChannelLiveMissionInfo mission_list = 1;
}

message HandleUserMissionReq {
    uint32 uid = 1;
    uint32 mission_id = 2;
    uint32 channel_id = 3;
    uint32 incr_finish_cnt = 4; // 需要增加的完成次数
    uint32 anti_repeated_mem = 5; // 某些任务不能重复执行, 例如关注主播，观看三个不同的直播间， 此字段为对象id
}

message HandleUserMissionResp {
    uint32 status = 1;      // 任务状态 see MissionStatus
}

message SwitchTimeUserMissionTsReq {
    uint32 uid = 1;
    bool switch = 2; // true：开  false：关
}

message SwitchTimeUserMissionTsResp {}

message GetFansMissionReq {
    uint32 uid = 1;
    uint32 actor_uid = 2;
    uint32 channel_id = 3;
}

message GetFansMissionResp {
    repeated ChannelLiveMissionInfo mission_list = 1;
}

message HandleFansMissionReq {
    uint32 uid = 1;
    uint32 actor_uid = 2;
    uint32 channel_id = 3;
    uint32 mission_id = 4;
    uint32 incr_finish_cnt = 5; // 需要增加的完成次数
}

message HandleFansMissionResp {
    uint32 status = 1;      // 任务状态 see MissionStatus
}

// 主播任务
message ActorMissionInfo {
    enum MissionType {
        UnknownMission = 0;
        DayIncomeMission = 1;      // 日直播流水任务
        DayTimeLengthMission = 2;  // 日直播时长任务
        WeekIncomeMission = 3;     // 周直播流水任务
        WeekTimeLengthMission = 4; // 周直播时长任务
        MonthIncomeMission = 5; // 月直播流水任务
    }

    string mission_name = 1;    // 任务名
    uint32 mission_level = 2;   // 任务等级
    string award_desc = 3;      // 奖励描述
    uint32 mission_type = 4;    // 任务类型
    uint32 mission_status = 5;  // 任务状态（当子任务都完成时，状态才为完成）see MissionStatus
    repeated ActorSubMissionInfo sub_missions = 6; // 子任务
    bool  is_current_show = 7;  // 是否是当前展示任务
}

// 主播子任务
message ActorSubMissionInfo {
    string sub_name = 1;        // 子任务名
    string progress_desc = 2;   // 子任务进度描述
    uint32 status = 3;          // 子任务进度 see MissionStatus
}

message GetActorMissionReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
}

message GetActorMissionResp {
    repeated ActorMissionInfo mission_list = 1;
    string mission_desc = 2;  // 任务说明
}

// 进行中的任务相关描述
message ProcessActorMissionDesc {
    string progress_desc = 1;   // 任务进度描述
    string award_desc = 2;      // 任务奖励描述
}

// 获取进行中的主播任务浮层（客户端每五分钟调用一次）
/*message GetProcessActorMissionDescReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
}*/

message GetProcessActorMissionDescResp {
    repeated ProcessActorMissionDesc process_mission_list = 1;
}

// 根据主播心跳每次增加直播时长次数（一次5s）
message IncrActorLiveTimeCntReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint64 channel_live_id = 3;
    bool is_living = 4; // 直播是否在直播中，非直播中不增加直播时长只更新当前时间
}

message IncrActorLiveTimeCntResp {
}

message MissionAwardInfo {
   enum MissionType {
      Mission_Invalid = 0;  // 无效
      Mission_Fans = 1;   // 粉丝任务
   }
   uint32 mission_type = 1; // see MissionType
   uint32 anchor_uid = 2;  // 主播uid
   uint32 award_conf_id = 3;   // 奖励配置id
   uint32 begin_ts = 4;   // 生效开始时间
   uint32 end_ts = 5;     // 生效结束时间
}

// 发放直播间任务奖励配置
message GrantLiveMissionAwardReq {
   MissionAwardInfo award_info = 1;
}
message GrantLiveMissionAwardResp {
}


//修改某个粉丝任务的更新时间
message UpdateFansMissionTimeReq {
    uint32 uid = 1;
    uint32 anchor_uid = 2;
    uint32 mission_id = 3;
    uint32 update_time = 4;
}
message UpdateFansMissionTimeRsp {
}

//触发定时任务
message TriggerTimerReq {
   // 定时任务类型
   enum TimerType
   {
      Timer_Type_Invalid = 0;  // 无效
      Timer_Type_AwardWeekActorMission = 1;  // 主播周任务结算
      Timer_Type_AwardMonthActorMission = 2;  // 主播月任务结算
   }

   TimerType timer_type = 1; // 定时任务类型
   uint32 settle_ts = 2;  // 结算时间
}
message TriggerTimerResp {
}