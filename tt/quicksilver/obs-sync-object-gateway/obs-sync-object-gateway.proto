syntax = "proto3";

package obs_sync_object_gateway;

option go_package = "golang.52tt.com/protocol/services/obs-sync-object-gateway";

service ObsSyncObjectGateway {
    rpc SyncUpload(SyncUploadReq)returns(SyncUploadResp){}
    rpc SyncDelete(SyncDeleteReq)returns(SyncDeleteResp){}
    rpc Freeze(FreezeReq)returns(FreezeResp){}
    rpc Unfreeze(UnfreezeReq)returns(UnfreezeResp){}
    rpc DeleteFrozen(DeleteFrozenReq) returns(DeleteFrozenResp){}
}

message SyncUploadReq {
    string app = 1;
    string scope = 2;
    string key = 3; //
    BucketInstance bucket = 4;
}
message SyncUploadResp {
    EC err_code = 1;
    string err_msg = 2;
}

//usages:
// 1. delete oid (app+scope or provider+bucket)
// 2. delete bucket.slaves 
// 3. delete all, bucket.master + bucket.slaves 
message SyncDeleteReq {
    ObjectId oid = 1;
    BucketInstance bucket = 2; //optional 
    bool with_master = 3; //master 也删除?
}
message SyncDeleteResp {
    EC err_code = 1;
    string err_msg = 2;
}
message FreezeReq {
    ObjectId oid = 1;
    BucketInstance bucket = 2; //optional
}
message FreezeResp {
    EC err_code = 1;
    string err_msg = 2;
}
message UnfreezeReq {
    ObjectId oid = 1;
    BucketInstance bucket = 2; //optional
}
message UnfreezeResp {
    EC err_code = 1;
    string err_msg = 2;
}

message DeleteFrozenReq {
    ObjectId oid = 1;
}

message DeleteFrozenResp {
    EC err_code = 1;
    string err_msg = 2;
}

message BucketInstance {
    BucketId master = 1;
    repeated BucketId slaves = 2;
}
message BucketId {
    string provider = 1;
    string name = 2;
}
message ObjectId {
    string app = 1;
    string scope = 2;
    string provider = 3; //
    string bucket = 4;
    string key = 5; //
}

//error code
enum EC {
    Ok = 0;
    InvalidArgument = 1;
    ScopeNotExists = 2;
    BucketNotExists = 3;
    RemoteError = 4;
}

