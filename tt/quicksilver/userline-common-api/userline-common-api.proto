syntax = "proto3";

package userline_common_api;
option go_package = "golang.52tt.com/protocol/services/userline-common-api";

service UserlineCommonApi{
  /* usertag服务迁移的接口 */
  rpc GetUserTag (GetUserTagReq) returns (GetUserTagResp) {}
  rpc BatGetUserTag (BatGetUserTagReq) returns (BatGetUserTagResp) {}
  rpc GetSimpleGameTag (GetSimpleGameTagReq) returns (GetSimpleGameTagResp) {}
  rpc GetTagConfigList (GetTagConfigListReq) returns (GetTagConfigListResp) {}
  /* usertag服务迁移的接口 */

  // 麦位用户扩展信息
  rpc GetMicUserExtInfo(GetMicUserExtInfoRequest) returns(GetMicUserExtInfoResponse);
  rpc BatchGetMicUserExtInfo(BatchGetMicUserExtInfoRequest) returns(BatchGetMicUserExtInfoResponse);
}

message UserTagList
{
  uint32 uid = 1;
  repeated UserTagBase tag_list = 2;
}

message UserTagBase {
  uint32 tag_type = 1;   // ga::UserTagType 1年龄段标签 2找什么人标签 4游戏标签 5个性标签 6生日日期标签
  string tag_name = 2;
  uint32 tag_id = 3;
  bytes  tag_info = 4;   // 扩展信息 结构体Ext字段，根据tag_type不同有不同类型 比如 tag_type=2时为 UserFindWhoTagExt / tag_type=4时为 UserGameTagExt
  bool   is_del = 5;
}

//选项二级属性
message GameOptSecondProp{
  string opt_prop = 1;
  repeated string value_list = 2;
}

message GameScreenShot{
  uint32 audit_status = 1;           //图片审核状态，参考EGameScreenStatus
  string img_url = 2;                  //原图
  string img_url_invisible_gamenick = 3 ;  //昵称打码图
  uint32 begin_audit_time = 4 ;            //开始审核时间，超过多久没审核，则在客户端主动拉取游戏卡时，设置为审核失败
  uint32 index = 5 ;                      //游戏截图索引(坑位)，从0开始
  uint32 upload_time = 6;                    //上传时间截
}

message GetUserTagReq {
  uint32 target_uid = 1;    // 要获取谁的标签列表，如果是获取自己的 那么填自己的UID
  bool is_need_tag_ext = 2; // 是否需要填充 tag的ext信息字段
}

message GetUserTagResp {
  UserTagList user_taglist = 1;
}

message BatGetUserTagReq {
  repeated uint32 uid_list = 1;
  bool is_need_tag_ext = 2; // 是否需要填充 tag的ext信息字段
}

message BatGetUserTagResp {
  repeated UserTagList usertaglist_list = 1;
}

message GetSimpleGameTagReq {
  repeated uint32 uid_list = 1;
  string game_name = 2;
}

message SimpleGameTagExt
{
  uint32 game_id = 1;                    // 关联的游戏ID
  string game_name = 2;
  string game_nickname = 3;               // 用户游戏昵称
  string game_area = 4;                   // 游戏区服
  string game_dan = 5;                    // 游戏段位
  uint32 uid = 6;
  string game_dan_url_for_mic = 7;            // 麦下展示的游戏段位图
  string game_role = 8;                // 游戏角色
  repeated string game_position = 9;            //游戏位置
  repeated string game_hero_list = 10; //想玩英雄列表
  string game_screenshot = 11;         //游戏上分图(已经审核通过的图片)
  repeated string game_style = 12;     //游戏风格
  string game_level_url = 13 ;         //段位图
  uint32 tag_id = 14 ;
  bool is_completed = 15;               //游戏卡是否完善(指所有必填字段都已填完整)
}

message GetSimpleGameTagResp {
  repeated SimpleGameTagExt game_ext_list = 1;
}

// GetTagConfigList这个接口的数据是用ConfGameTagExt这个解析UserTagBase.TagInfo
message ConfGameTagExt
{
  uint32 game_id = 1;                    // 关联的游戏ID
  string back_img_url = 2;                    //旧版的背景图
  repeated ConfGameTagOpt opt_list = 3;    // 选项列表
  string thumb_img_url = 4;                // 缩略图 (旧版本的缩略图，已废弃)
  string game_nickname = 5;                // 游戏的昵称 由用户自己填写
  GameScreenShot game_screenshot = 6;      //游戏上分图,用户上传
  string game_card_img_url = 7 ;           //游戏缩略图
  string game_back_img = 8 ;               //新版的背景图(大尺寸)
  string game_icon_img = 9 ;               //新建游戏卡时的小icon图片
  string game_corner_mark_img = 10;        //游戏卡角标图
  string game_no_level_img_url = 11;       //无段位时的填充图，旧版的类型图也用了该张图
  repeated LevelImg level_img_url = 12 ;     //旧版段位图
  repeated LevelImg mic_level_img_url = 13 ; //麦位-段位图*/
  string game_back_img_mini = 14 ;               //新版的背景图(小尺寸)
  uint32 game_back_color_num = 15 ;             //背景底色值
  uint32 game_extra_opt_switch = 16;                       //是否有游戏昵称，截图等选项等，参考EGameExtraOpt，有则或上去
  repeated LevelImg level_img_url_new = 17;          //新版段位图
  string game_no_level_img_url_new = 18;           //新版游戏无端位图
  uint32 u_game_id = 19;
}

message LevelImg{
  string level_name = 1;
  string img_url = 2;
}

message ConfGameTagOpt    //配置模板
{
  string opt_name = 1;
  uint32 opt_id = 2;
  bool is_support_muti_set = 3;            // 已废弃，用下面的support_muti_set_cnt = 6
  repeated string value_conf_list = 4;     // 游戏属性 系统配置的备选项
  repeated string value_userset_list = 5;  // 游戏属性 用户实际设置项

  uint32 support_muti_set_cnt = 6;        // 选项用户可以选择的数量1,2,3...
  uint32 partition_id = 7;                // 选项分区（1=第一部分 2=第二部分）
  repeated GameOptSecondProp prop_value_list = 8;      ////二级属性，例如王者荣耀的英雄选项
  bool is_must = 9;       //是否是必填项
  uint32 text_box_style = 10;      //0斜杠 1带方框
  bool display_in_channel = 11 ;    //是否在个人房麦下列表显示
}

// 获取配置标签列表
message GetTagConfigListReq{
  uint32 tag_type = 1;      // ga::UserTagType 指定设置的标签类型 如果为0 那么就是全部类型
}

message GetTagConfigListResp{
  repeated UserTagBase tag_list = 1;
  repeated uint32 game_id_list = 2;   // 注册时就需要 完善二级属性的游戏 id
}

// GetUserTag接口的数据是用UserGameTagExt这个解析UserTagBase.TagInfo
message UserGameTagExt
{
  uint32 game_id   = 1;                    // 关联的游戏ID
  string back_img_url = 2;
  repeated UserGameTagOpt opt_list = 3;    // 选项列表
  string thumb_img_url = 4;                // 缩略图 缩略图为圆形图 目前用于好友匹配中展示
  string game_nickname = 5;                // 游戏的昵称 由用户自己填写
  GameScreenShot game_screenshot = 6;      //游戏上分图,用户上传
  repeated GameScreenShot game_screenshot_list = 7 ; //20211125，支持多张游戏截图
}

message UserGameTagOpt
{
  string opt_name = 1;
  uint32 opt_id = 2;
  bool is_support_muti_set = 3;            // 是否支持多选 (该字段废弃，填support_muti_set_cnt就可以了)
  repeated string value_conf_list = 4;     // 游戏属性 系统配置的备选项
  repeated string value_userset_list = 5;  // 游戏属性 用户实际设置项

  uint32 support_muti_set_cnt = 6;        // 选项用户可以选择的数量 1就是单选 2就是可以选2个
  uint32 partition_id = 7;                // 选项分区（1=第一部分 2=第二部分）
  repeated GameOptSecondProp prop_value_list = 8;  //二级属性，例如王者荣耀的英雄选项
}

message MicUser {
  uint32 uid = 1;
  // 用户类型 see account.proto enum USER_TYPE
  uint32 type = 2;
}

message GetMicUserExtInfoRequest {
  MicUser user = 1;
}

message GetMicUserExtInfoResponse {
  bytes mic_user_info = 1;
}

message BatchGetMicUserExtInfoRequest {
  repeated MicUser users = 1;
}

message BatchGetMicUserExtInfoResponse {
  map<uint32, bytes> mic_user_infos = 1;
}
