syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-admin-handler";

package channel_admin_handler;

service ChannelAdminHandler {
    rpc GuildChannelAdminCheckIsInWhiteList (GuildChannelAdminCheckIsInWhiteListReq) returns (GuildChannelAdminCheckIsInWhiteListResp) {
    }
    //设置管理员
    //删除管理员
}

message GuildChannelAdminCheckIsInWhiteListReq {
    uint32 guild_id = 1;
    uint32 channel_id = 2;
}

message GuildChannelAdminCheckIsInWhiteListResp {
    bool guild_white_list = 1;
    bool channel_white_list = 2;
}
