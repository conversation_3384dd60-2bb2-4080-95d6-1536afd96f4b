syntax = "proto3";

package knight_group_mission;
option go_package = "golang.52tt.com/protocol/services/knight-group-mission";

import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

// buf:lint:ignore SERVICE_PASCAL_CASE
service knightGroupMission {
  rpc GetKnightMission (GetKnightMissionReq) returns (GetKnightMissionResp) {}
  rpc AddKnightMission (AddKnightMissionReq) returns (AddKnightMissionResp) {}

  //  对账相关
  //  获取时间段内的订单数和金额
  rpc GetKnightMissionOrderCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  //  获取时间范围内的订单列表
  rpc GetKnightMissionOrderList(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  //  补单
  rpc FixKnightMissionOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}
}

//任务
message GetKnightMissionReq {
  uint32 uid = 1;
  uint32 channel_id = 2;
}

message GetKnightMissionResp{
  repeated KnightMissionMember member_list = 1;
  repeated KnightMissionText mission_text = 2 ; // 任务文本
  string time_string_now = 3;  // 当前周期的时间
  string mission_desc = 4; // 任务描述
}

message KnightMission {
  string time_string = 1 ; // 时间周期
  uint32 total_score = 2 ; // 当期积分
  bool is_finished = 3;  // 是否完成
  bool is_this_week = 4;  // 是否本周
}


message KnightMissionMember {
  uint32 uid = 1;
  repeated KnightMission mission_list = 2;
}

message KnightMissionText {
  string text = 1 ;
  string key_text = 2; // 需要加粗的文本
}


message AddKnightMissionReq{
  string order_id = 1;
  uint32 anchor_uid = 2;
  uint32 knight_uid = 3;
  uint32 channel_id = 4;
  uint32 channel_type = 5;
  uint32 price = 6;
  uint32 create_time = 7;
  uint32 begin_time = 8;
  uint32 expire_time = 9;
  uint32 guild_id = 10;
}
message AddKnightMissionResp{

}