syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/super-player-privilege";
package super_player_privilege;

message GetUserSpecialConcernReq{
  uint32 uid = 1;
}

message GetUserSpecialConcernResp{
  repeated uint32 special_concern_list = 1;
  bool show_expire_window = 2;
}

message GetUserBeSpecialConcernReq{
  uint32 uid = 1;
}

message GetUserBeSpecialConcernResp{
  repeated uint32 be_special_concern_list = 1;
}

message AddUserSpecialConcernReq{
  uint32 uid = 1;
  uint32 target_uid = 2;
}

message AddUserSpecialConcernResp{
}

message DelUserSpecialConcernReq{
  uint32 uid = 1;
  uint32 target_uid = 2;
}


/*获取IM搭讪特权剩余次数*/
message GetIMPrivilegeCountReq{
  uint32 uid = 1;
}
message GetIMPrivilegeCountResp{
  uint32 uid = 1;
  uint32 cnt = 2;
}

/*使用一次IM搭讪特权*/
message UseIMPrivilegeReq{
  uint32 uid = 1;
  uint32 target_uid = 2;
}

message UseIMPrivilegeResp{
}

enum SuperPlayerPrivilegeType {
  IMPri = 0;
}
/*查询是否使用了IM搭讪特权*/
message GetPrivilegeTargetReq{
  uint32 uid = 1;
  uint32 target_uid = 2;
  uint32 privilege_type = 3; /*SuperPlayerPrivilegeType*/
}

message GetPrivilegeTargetResp{
  bool used = 1; /*使用了特权*/
}

message DelUserSpecialConcernResp{
}


// 在线隐身状态
enum OnlineSwitch
{
  ENUM_ONLINE_SWITCH_UNSPECIFIED = 0;
  ENUM_ONLINE_SWITCH_ONLINE = 1; // 在线
  ENUM_ONLINE_SWITCH_STEALTH = 2; // 隐身
}

// 挚友墙可见范围
enum FellowVisible
{
  ENUM_FELLOW_VISIBLE_UNSPECIFIED = 0;
  ENUM_FELLOW_VISIBLE_ALL = 1; // 允许所有人查看
  ENUM_FELLOW_VISIBLE_PLAYMATES_AND_FANS = 2; // 允许所有玩伴和粉丝查看
  ENUM_FELLOW_VISIBLE_MY_FELLOW = 3; // 仅允许我的挚友查看
  ENUM_FELLOW_VISIBLE_ME = 4; // 仅允许我自己查看
}

// 获取用户挚友墙可见范围
message BatchGetFellowVisibleProfilesReq {
  repeated uint32 uid_list = 1;
}
message BatchGetFellowVisibleProfilesResp {
  map<uint32, FellowVisible> uid_fellow_visible_map = 1;
}

// 在线状态变更事件 kafka
message SVIPPrivilegeOnlineChangeEvent
{
  enum ChangeType
  {
    ENUM_CHANGE_TYPE_UNSPECIFIED = 0;
    ENUM_CHANGE_TYPE_MANUAL = 1; // 手动操作
    ENUM_CHANGE_TYPE_MORROW = 2; // 次日自动结束隐身
    ENUM_CHANGE_TYPE_SVIP_EXPIRED = 3; // SVIP到期结束隐身
    ENUM_CHANGE_TYPE_SVIP_GOD_KING = 4; // SVIP升级神王结束隐身
    ENUM_CHANGE_TYPE_SVIP_SYS = 5; // 其他内部处理
    ENUM_CHANGE_TYPE_OFFLINE_TIMEOUT = 6; // 离线超时结束隐身
  }
  uint32 uid = 1;
  OnlineSwitch online_switch = 2; // 在线状态
  ChangeType change_type = 3; // 变更类型
  int64 change_time = 4; // 变更时间
}

// 悄悄看全局开关
enum SneakilyReadSwitch
{
  ENUM_SNEAKILY_READ_SWITCH_UNSPECIFIED = 0;
  ENUM_SNEAKILY_READ_SWITCH_ON = 1; // 开启
  ENUM_SNEAKILY_READ_SWITCH_OFF = 2; // 关闭
}

// SVIP权益设置
message SVIPPrivilegeProfile
{
  OnlineSwitch online_switch = 1; // 在线状态
  FellowVisible fellow_visible = 3; // 挚友墙可见范围
  uint32 week_remain_stealth_times = 2; // 本周剩余隐身次数
  uint32 week_stealth_times = 4; // 本周已消耗隐身次数
  bool is_today_stealth = 5; // 今日是否使用隐身
  SneakilyReadSwitch sneakily_read_switch = 6; // 悄悄看全局开关
}

// 更新SVIP权益设置
message SetUserSVIPPrivilegeProfileReq
{
  uint32 uid = 1;
  SVIPPrivilegeProfile privilege_profile = 2;
  bool ignore_freq_limit = 3; // 忽略频率限制
  bool ignore_week_times_limit = 4; // 忽略周次数限制
}
message SetUserSVIPPrivilegeProfileResp
{
  SVIPPrivilegeProfile privilege_profile = 2;
}

// 获取SVIP权益设置
message GetUserSVIPPrivilegeProfileReq
{
  uint32 uid = 1;
}
message GetUserSVIPPrivilegeProfileResp
{
  SVIPPrivilegeProfile privilege_profile = 1;
}

// 提前使用SVIP隐身权益天数
message UseSVIPStealthAheadReq
{
  uint32 uid = 1;
}
message UseSVIPStealthAheadResp
{

}

// 获取用户隐身状态
message GetUserOnlineSwitchReq
{
  uint32 uid = 1;
}
message GetUserOnlineSwitchResp
{
  OnlineSwitch online_switch = 1;
}

// 批量获取用户隐身状态
message BatchGetUserOnlineSwitchReq
{
  repeated uint32 uid_list = 1;
}
message BatchGetUserOnlineSwitchResp
{
  map<uint32, OnlineSwitch> uid_online_switch_map = 1;
}

// 触发SVIP隐身到期推送
message TriggerSVIPStealthExpiringPushReq
{
  uint32 uid = 1;
  uint64 mock_time = 2; // 模拟时间
}
message TriggerSVIPStealthExpiringPushResp
{

}

// 重置隐身状态与本周次数
message ResetUserStealthReq
{
  uint32 uid = 1;
}
message ResetUserStealthResp
{

}

message ReportSneakilyReadReq {
  uint32 uid = 1;
}
message ReportSneakilyReadResp {
}

message ValidUserHasSneakilyReadReq {
  uint32 uid = 1;
}
message ValidUserHasSneakilyReadResp {
  bool has_sneakily_read = 1;
}

message ValidUserHasFellowVisibleReq {
  uint32 uid = 1;
}
message ValidUserHasFellowVisibleResp {
  bool has_set_fellow_visible = 1;
  bool is_set_fellow_visible_now = 2;
}

service SuperPlayerPrivilege
{
  rpc GetUserSpecialConcern(GetUserSpecialConcernReq) returns (GetUserSpecialConcernResp){
  }
  rpc GetUserBeSpecialConcern(GetUserBeSpecialConcernReq) returns (GetUserBeSpecialConcernResp){
  }
  rpc AddUserSpecialConcern(AddUserSpecialConcernReq) returns (AddUserSpecialConcernResp){
  }
  rpc DelUserSpecialConcern(DelUserSpecialConcernReq) returns (DelUserSpecialConcernResp){
  }

  // 获取IM搭讪特权剩余次数
  rpc GetIMPrivilegeCount(GetIMPrivilegeCountReq) returns (GetIMPrivilegeCountResp){}
  // 使用一次IM搭讪特权
  rpc UseIMPrivilege(UseIMPrivilegeReq) returns (UseIMPrivilegeResp){}
  // 查询是否使用了IM搭讪特权
  rpc GetPrivilegeTarget(GetPrivilegeTargetReq) returns (GetPrivilegeTargetResp){}

  // 获取用户挚友墙可见范围
  rpc BatchGetUserFellowVisibleProfile(BatchGetFellowVisibleProfilesReq) returns (BatchGetFellowVisibleProfilesResp){}

  // 更新SVIP权益设置
  rpc SetUserSVIPPrivilegeProfile(SetUserSVIPPrivilegeProfileReq) returns (SetUserSVIPPrivilegeProfileResp){}
  // 获取SVIP权益设置
  rpc GetUserSVIPPrivilegeProfile(GetUserSVIPPrivilegeProfileReq) returns (GetUserSVIPPrivilegeProfileResp){}
  // 提前使用SVIP隐身权益天数
  rpc UseSVIPStealthAhead(UseSVIPStealthAheadReq) returns (UseSVIPStealthAheadResp){}
  // 获取用户隐身状态
  rpc GetUserOnlineSwitch(GetUserOnlineSwitchReq) returns (GetUserOnlineSwitchResp){}
  // 批量获取用户隐身状态
  rpc BatchGetUserOnlineSwitch(BatchGetUserOnlineSwitchReq) returns (BatchGetUserOnlineSwitchResp){}

  // 触发SVIP隐身到期推送
  rpc TriggerSVIPStealthExpiringPush(TriggerSVIPStealthExpiringPushReq) returns (TriggerSVIPStealthExpiringPushResp){}
  // 重置隐身状态与本周次数
  rpc ResetUserStealth(ResetUserStealthReq) returns (ResetUserStealthResp){}

  // 上报局部悄悄看触发事件
  rpc ReportSneakilyRead(ReportSneakilyReadReq) returns (ReportSneakilyReadResp) {}
  // 判断用户是否开启过悄悄看
  rpc ValidUserHasSneakilyRead(ValidUserHasSneakilyReadReq) returns (ValidUserHasSneakilyReadResp) {}
  // 判断用户是否设置过挚友墙访问权限
  rpc ValidUserHasFellowVisible(ValidUserHasFellowVisibleReq) returns (ValidUserHasFellowVisibleResp) {}
}