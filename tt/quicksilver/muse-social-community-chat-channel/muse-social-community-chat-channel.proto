syntax = "proto3";

import "channel/channel_.proto";

package muse_social_community_chat_channel;
option go_package = "golang.52tt.com/protocol/services/muse-social-community-chat-channel";

// buf:lint:ignore SERVICE_PASCAL_CASE
service muse_social_community_chat_channel {
  rpc GetSocialCommunityChatChannelHistoryMsg(GetSocialCommunityChatChannelHistoryMsgReq)returns(GetSocialCommunityChatChannelHistoryMsgResp); /* 社群聊天室历史消息 */

}

/*********************************************公屏消息拉取*********************************************/
/* 社群聊天室历史消息 */
message GetSocialCommunityChatChannelHistoryMsgReq {
  uint32 channel_id = 1;
  uint32 count = 2;
}
message GetSocialCommunityChatChannelHistoryMsgResp {
  uint32 channel_id = 1;
  uint32 begin_seq = 2;
  uint32 end_seq = 3;
  repeated ga.channel.ChannelMsg channel_msg_list = 4;
  bool is_end = 5; /* 是否到底 */
}
