syntax="proto3";

option go_package = "golang.52tt.com/protocol/services/channel-business-ext";
package channel_business_ext;

message GetChannelImBusinessExtInfoReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 channel_type = 3;
}

message GetChannelImBusinessExtInfoResp {
    bytes ext_info = 1;
}


service ChannelImBusinessExt {
    rpc GetChannelImBusinessExtInfo(GetChannelImBusinessExtInfoReq) returns(GetChannelImBusinessExtInfoResp) {}
}