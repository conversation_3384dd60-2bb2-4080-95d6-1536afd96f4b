syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-recommend-relationship";
package channel_recommend_relationship;

service ChannelRecommendRelationshipService {
    // 获取房间推荐关注链区域
    rpc GetChannelRecommendRelationship (GetChannelRecommendRelationshipReq) returns (GetChannelRecommendRelationshipRsp) {}

    //根据房间ID获取用户关系链信息
    rpc BatchGetRecommendRelationshipByChannelId(BatchGetRecommendRelationshipByChannelIdReq) returns (BatchGetRecommendRelationshipByChannelIdRsp) {}

    // 获取UGC首页推荐的PGC房间
    rpc GetUGCHomePageRecommendChannel (GetUGCHomePageRecommendChannelReq) returns (GetUGCHomePageRecommendChannelRsp) {}

    // === 运营后台接口

    // 获取首页推荐池标签列表(包含虚拟标签)
    rpc GetRecommendPoolChannelTags(GetRecommendPoolChannelTagsReq) returns (GetRecommendPoolChannelTagsResp) {}

    // 新增首页推荐池房间
    rpc CreateRecommendPoolChannel(CreateRecommendPoolChannelReq) returns (EmptyResp) {}
    // 删除首页推荐池房间
    rpc DeleteRecommendPoolChannel(DeleteRecommendPoolChannelReq) returns (EmptyResp) {}
    // 获取首页推荐池房间列表
    rpc GetRecommendPoolChannelList(GetRecommendPoolChannelListReq) returns (GetRecommendPoolChannelListResp) {}

    // 新增首页推荐池权重
    rpc CreateRecommendPoolTagWeight(CreateRecommendPoolTagWeightReq) returns (EmptyResp) {}
    // 修改首页推荐池权重
    rpc ModifyRecommendPoolTagWeight(CreateRecommendPoolTagWeightReq) returns (EmptyResp) {}
    // 删除首页推荐池权重
    rpc DeleteRecommendPoolTagWeight(DeleteRecommendPoolTagWeightReq) returns (EmptyResp) {}
    // 获取首页推荐池权重列表
    rpc GetRecommendPoolTagWeightList(GetRecommendPoolTagWeightListReq) returns (GetRecommendPoolTagWeightListResp) {}

    //触发定时任务
    rpc TriggerTimer(TriggerTimerReq) returns (TriggerTimerResp) {}

    //批量获取房间映射的标题
    rpc BatGetChannelTitle(BatGetChannelTitleReq) returns(BatGetChannelTitleResp){}

    // 批量获取房间映射的标题
    rpc BatchGetChannelGlorySubscript(BatchGetChannelGlorySubscriptReq) returns (BatchGetChannelGlorySubscriptResp) {}
}

// 首页推荐池房间推荐位置
enum RecommendChannelLocation {
    RECOMMEND_CHANNEL_LOCATION_UNSPECIFIED = 0;
    RECOMMEND_CHANNEL_LOCATION_HOME = 1; // 首页
    RECOMMEND_CHANNEL_LOCATION_TAB_WANG_ZHE = 2; // 王者Tab
    RECOMMEND_CHANNEL_LOCATION_TAB_TOP_OVERLAY = 3; // 顶部浮窗
    RECOMMEND_CHANNEL_LOCATION_TAB_CH_LIST = 4; // 娱乐tab-列表页
}

// 获取房间推荐关注链区域
message GetChannelRecommendRelationshipReq {
    uint32 uid = 1;
}

message GetChannelRecommendRelationshipRsp {
    map<uint32, uint32> cids_types_map = 1;    // cid -> channel-recommend-svr.proto RecommendRelationshipType
    repeated uint32 guild_cids = 2;                   // 我的公会房间
}


// 根据房间ID获取用户关系链信息
message BatchGetRecommendRelationshipByChannelIdReq {
    uint32 uid = 1;
    repeated uint32 channel_id_list = 2;
}
message BatchGetRecommendRelationshipByChannelIdRsp {
    map<uint32, uint32> relationships_map = 1;    // cid -> channel-recommend-svr.proto RecommendRelationshipType
}

enum RecommendReason {
    RECOMMEND_REASON_UNSPECIFIED = 0;
    RECOMMEND_REASON_FRIEND_MIC = 1; // 关系链-玩伴在麦
    RECOMMEND_REASON_REC_POOL   = 2; // 推荐池（兜底池)
}
message RecommendChannelInfo {
    uint32 channel_id = 1; // 房间ID
    uint32 recommend_reason = 2;   // 推荐来源 enum RecommendReason
    uint32 tag_id = 3; // 房间品类ID
    string channel_title = 4;  // 房间标题
}
// 获取首页推荐的PGC房间
message GetUGCHomePageRecommendChannelReq {
    uint32 uid = 1;       // 用户ID
    uint32 num = 2;       //需要数量
    uint32 location = 3;  // 推荐位置 enum RecommendChannelLocation
}
message GetUGCHomePageRecommendChannelRsp {
    repeated RecommendChannelInfo channel_list = 1; // 房间列表
}



// 首页推荐池房间标签
message RecommendPoolChannelTag {
    enum ChannelTagType {
        RECOMMEND_POOL_CHANNEL_TAG_TYPE_DEFAULT = 0; // 默认PGC房间品类
        RECOMMEND_POOL_CHANNEL_TAG_TYPE_VIRTUAL = 1; // 虚拟标签
    }
    uint32 tag_id = 1; // 标签ID
    string tag_name = 2; // 标签名称
    string weighted_tag_text = 3; // 加权标签文案
    uint32 weight = 4; // 权重 1~1000
    ChannelTagType tag_type = 5; // 房间标签类型
}

// 获取首页推荐池标签列表(包含虚拟标签)
message GetRecommendPoolChannelTagsReq {

}
message GetRecommendPoolChannelTagsResp {
    repeated RecommendPoolChannelTag list = 1;
}

// 新增首页推荐池房间
message CreateRecommendPoolChannelReq {
    message Channel {
        uint32 channel_id = 1; // 房间ID
        uint32 tag_id = 2; // 房间标签ID
    }
    repeated Channel channels = 1; // 房间列表
    uint32 location = 2; // 位置 RecommendChannelLocation
}
message EmptyResp {}

// 删除首页推荐池房间
message DeleteRecommendPoolChannelReq {
    repeated uint32 id = 1;
}

// 推荐池房间信息
message RecommendPoolChannel {
    uint32 id = 1;
    uint32 location = 2; // 位置
    uint32 channel_id = 3; // 房间ID
    string channel_view_id = 4; // 房间view_id
    string channel_name = 5; // 房间名称
    uint32 tag_id = 6; // 房间标签
    string tag_name = 7; // 标签名称
    uint32 active_status = 8; // 生效状态
    string weighted_tag_text = 9; // 加权标签文案
}

// 获取首页推荐池房间列表
message GetRecommendPoolChannelListReq {
    uint32 channel_id = 1; // 房间ID
    uint32 tag_id = 2; // 房间标签
    uint32 offset = 3;
    uint32 limit = 4;
    string channel_view_id = 5; // 房间view_id
    repeated uint32 locations = 6; // 位置
}
message GetRecommendPoolChannelListResp {
    repeated RecommendPoolChannel list = 1;
    uint32 total = 2; // 总数
}

// 新增/编辑首页推荐池权重
message CreateRecommendPoolTagWeightReq {
    uint32 tag_id = 1; // 房间标签
    uint32 weight = 2; // 权重 1~1000
    uint32 location = 3; // 位置
}

// 删除首页推荐池权重
message DeleteRecommendPoolTagWeightReq {
    repeated uint32 id = 1; // 首页权重 唯一标识
}

// 推荐池权重信息
message RecommendPoolTagWeight {
    uint32 id = 1; // 首页权重 唯一标识
    uint32 tag_id = 2; // 房间标签
    uint32 weight = 3; // 权重 1~1000
    string tag_nane = 4; // 标签名称
    uint32 location = 5; // 位置
}

// 获取首页推荐池权重列表
message GetRecommendPoolTagWeightListReq {
    uint32 offset = 1;
    uint32 limit = 2;
    repeated uint32 locations = 3; // 位置
}

message GetRecommendPoolTagWeightListResp {
    repeated RecommendPoolTagWeight list = 1;
    uint32 total = 2; // 总数
}


//触发定时任务
message TriggerTimerReq {
   // 定时任务类型
   enum TimerType
   {
      Timer_Type_Invalid = 0;  // 无效
      Timer_Type_AssignChannelTitle = 1;  // 分配房间标题
   }

   TimerType timer_type = 1; // 定时任务类型
   uint32 assign_ts = 2;  //房间标题映射时间
}
message TriggerTimerResp {
}

//批量获取房间映射的标题
message BatGetChannelTitleReq {
   uint32 ts = 1;
   repeated uint32 cid_list = 2;
}
message BatGetChannelTitleResp {
  map<uint32, string> map_id_title = 1;   
}

// 房间荣誉角标
message ChannelGlorySubscript {
    uint32 channel_id = 1;
    string list_bg_url = 2;  //推荐列表背景图
    string list_text   = 3;  //推荐列表文案
    string room_head_url = 4; //房间内头像标识
    string room_nickname_url = 5; //房间内昵称底色框
    string room_pc_head_url = 6; //房间内PC头像
}
// 批量获取房间荣誉角标
message BatchGetChannelGlorySubscriptReq {
    repeated uint32 cid_list = 1;
}
message BatchGetChannelGlorySubscriptResp {
    map<uint32, ChannelGlorySubscript> map_cid_subscript = 1;
}
