syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/present-illustration";
package present_illustration;

import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";


service PresentIllustration {
  // 获取图鉴配置列表
  rpc GetIllustrationConfigList(GetIllustrationConfigListReq) returns (GetIllustrationConfigListResp) {}
  // 获取图鉴配置详情
  rpc GetIllustrationConfig(GetIllustrationConfigReq) returns (GetIllustrationConfigResp) {}
  // 创建图鉴配置
  rpc CreateIllustrationConfig(CreateIllustrationConfigReq) returns (CreateIllustrationConfigResp) {}
  // 删除图鉴配置
  rpc DeleteIllustrationConfig(DeleteIllustrationConfigReq) returns (DeleteIllustrationConfigResp) {}
  // 检查排序重复
  rpc CheckIllustrationConfigSort(CheckIllustrationConfigSortReq) returns (CheckIllustrationConfigSortResp) {}

  //获取礼物图鉴摘要
  rpc GetIllustrationSummary(GetIllustrationSummaryReq) returns(GetIllustrationSummaryRsp);
  //获取用户所有图鉴列表
  rpc GetIllustrationListAll(GetIllustrationListAllReq) returns(GetIllustrationListAllRsp);
  //获取图鉴详情信息
  rpc GetIllustrationDetail (GetIllustrationDetailReq) returns (GetIllustrationDetailRsp);
  //开关
  rpc GetIllustrationSwitch (GetIllustrationSwitchReq) returns (GetIllustrationSwitchRsp);
  //定时获取红点的接口
  rpc GetUserSampleRedpoint (GetUserSampleRedpointReq) returns (GetUserSampleRedpointRsp);

  rpc FixPresentIllustrationEvent (FixPresentIllustrationEventReq) returns (FixPresentIllustrationEventRsp);
  rpc CleanUserIllustration (CleanUserIllustrationReq) returns (CleanUserIllustrationRsp);

  //礼物->图鉴对账
  rpc TimeRangeCount (ReconcileV2.TimeRangeReq ) returns (ReconcileV2.CountResp ) {}
  rpc TimeRangeOrderIds (ReconcileV2.TimeRangeReq ) returns (ReconcileV2.OrderIdsResp ) {}
  //补单图鉴
  rpc FixPresentIllustrationOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}

}

// 图鉴状态
enum IllustrationStatus {
  ILLUSTRATION_STATUS_UNSPECIFIED = 0;
  ILLUSTRATION_STATUS_IN_PROGRESS = 1; // 生效中
  ILLUSTRATION_STATUS_NOT_STARTED = 2; // 未开始
  ILLUSTRATION_STATUS_ENDED = 3; // 已结束
}

// 活动状态
enum IllustrationSearchType {
  ILLUSTRATION_SEARCH_TYPE_UNSPECIFIED = 0;
  ILLUSTRATION_SEARCH_TYPE_ID = 1; // id
  ILLUSTRATION_SEARCH_TYPE_NAME = 2; // 图鉴名称
  ILLUSTRATION_SEARCH_TYPE_REMARK = 3; // 运营备注
}

// 马甲包跳转配置
message PackageJumpConfig {
  uint32 app = 1; // 马甲包
  uint32 platform = 2; // 平台
  string link = 3; // 跳转链接
}

// 图鉴配置
message IllustrationConfig {
  // 返场时间
  message EncoreTime {
    uint64 start_time = 1; // 返场开始时间
    uint64 end_time = 2; // 返场结束时间
  }
  uint32 id = 1;
  string name = 2; // 图鉴名称
  string icon = 3; // 图鉴封面
  uint64 total_price = 4; // 总金额（分）
  string total_price_cny = 5; // 总金额（元）
  uint64 start_time = 6; // 开始时间（返场时为返场时间）
  uint64 end_time = 7; // 结束时间（返场时为返场时间）
  string remark = 8; // 运营备注
  repeated IllustrationPresentConfig present_list = 9;
  uint32 sort = 10; // 图鉴排序
  uint64 encore_start_time = 11; // 返场开始时间（编辑入参）
  uint64 encore_end_time = 12; // 返场结束时间（编辑入参）
  string operator = 13; // 操作人
  repeated EncoreTime history_encore_time_list = 14; // 历史返场时间列表
  string source_note = 15; // 来源说明
  repeated PackageJumpConfig source_jump_config = 16; // 马甲包跳转配置
  uint64 origin_start_time = 17; // 初始开始时间
  uint64 origin_end_time = 18; // 初始结束时间
  bool is_encore = 19; // 是否返场
  uint64 create_time = 20; // 创建时间
  IllustrationStatus status = 21; // 图鉴状态
}

// 图鉴礼物配置
message IllustrationPresentConfig {
  uint32 gift_id = 1;
  string name = 2;
  string icon = 3;
  uint32 price = 4; // 价格（分）
  uint32 price_cny = 5; // 价格（元）
  uint32 sort = 6; // 排序
}

message GetIllustrationConfigListReq {
  enum SortType {
    SORT_TYPE_UNSPECIFIED = 0;
    SORT_TYPE_TOTAL_PRICE = 1; // 总金额
    SORT_TYPE_START_TIME = 2; // 开始时间
    SORT_TYPE_END_TIME = 3; // 结束时间
    SORT_TYPE_ID = 4; // ID
  }
  enum SortOrder {
    SORT_ORDER_UNSPECIFIED = 0;
    SORT_ORDER_ASC = 1; // 升序
    SORT_ORDER_DESC = 2; // 降序
  }
  uint32 offset = 1;
  uint32 limit = 2;
  string id = 3; // 搜索ID
  string name = 4; // 搜索图标名称
  string remark = 5; // 搜索运营备注
  IllustrationStatus status = 6; // 图鉴状态
  SortType sort = 7; // 排序类型
  SortOrder order = 8; // 排序方式
}
message GetIllustrationConfigListResp {
  repeated IllustrationConfig list = 1;
  uint32 total = 2;
}

message GetIllustrationConfigReq {
  uint32 id = 1;
}
message GetIllustrationConfigResp {
  IllustrationConfig config = 1;
}

message CreateIllustrationConfigReq {
  IllustrationConfig config = 1;
}
message CreateIllustrationConfigResp {
}

message DeleteIllustrationConfigReq {
  uint32 id = 1;
}
message DeleteIllustrationConfigResp {
}

message CheckIllustrationConfigSortReq {
  uint32 id = 1; // 图鉴ID
  uint32 sort = 2; // 排序
}
message CheckIllustrationConfigSortResp {
  bool is_repeat = 1; // 是否重复
  IllustrationConfig config = 2; // 重复图鉴信息
}

//图鉴摘要信息
message SummaryTopInfo {
  uint32 level = 1;   //图鉴等级
  uint32 count = 2;   //已收集数量
}
//获取礼物图鉴摘要
message GetIllustrationSummaryReq {
  uint32 uid = 1;
}
message GetIllustrationSummaryRsp {
  uint32 collected_total = 1; // 已收集总数
  uint32 not_collect_total = 2; // 未收集总数
  repeated SummaryTopInfo top_list = 3; // TOP2等级摘要
  bool has_new = 4; // 上新红点
  bool red_dot = 5; // 是有有集齐或升级红点
}

enum IllustrationInfoRedType{
  Unknow = 0;         //未有红点
  Collected = 1;      //集齐红点
  Upgrade = 2;      //升级红点
}
//图鉴信息
message IllustrationInfo {
  uint32 id = 1;   //图鉴ID
  uint32 level = 2;   //图鉴等级
  uint32 status = 3;   //收集状态 present_illustration_logic.proto IllustrationCollectStatus
  uint32 progress = 4; //收集礼物进度
  uint64 value = 5;    //礼物总价值
  bool   has_new = 6;  //是否是上新图鉴
  IllustrationInfoRedType   red_dot = 7;  //红点类型
  uint32 total_gift_id = 8; //礼物ID总数
}

//获取用户所有图鉴信息
message GetIllustrationListAllReq {
  uint32 uid = 1;                 //操作者uid
  uint32 target_uid = 2;          //目标用户uid
  bool   clear_red = 3;          //是否清红点
}
message GetIllustrationListAllRsp {
  repeated IllustrationInfo list = 1;
}

//图鉴礼物信息
message IllustrationGiftInfo {
  uint32 gift_id = 1;   //礼物ID
  uint32 received_num = 2;   //收到礼物数量
  uint64 received_price = 3;   //收到礼物价值
  uint32 price_type = 4;       //礼物价值类型
}

//获取图鉴详情信息
message GetIllustrationDetailReq {
  uint32 illustration_id = 1; // 图鉴ID
  uint32 target_uid = 2; // 查看目标UID
}
message GetIllustrationDetailRsp {
  IllustrationInfo base_info = 1;
  repeated  IllustrationGiftInfo gift_list = 2;   //图鉴详情信息
}

//获取用户开关
message GetIllustrationSwitchReq {
  uint32 uid = 1;
}
message GetIllustrationSwitchRsp {
  bool   switch = 1;
}


message FixPresentIllustrationEventReq {
  uint32 uid = 1;
  uint32 cid = 2;
  uint32 send_time = 3;
  uint32 gift_id = 4;
  uint32 gift_num = 5;
  uint32 price_type = 6;
  uint32 price = 7;
  string order_id = 8;
}
message FixPresentIllustrationEventRsp {
}
message CleanUserIllustrationReq {
  uint32 uid = 1;
}
message CleanUserIllustrationRsp {

}

message GetUserSampleRedpointReq {
  uint32 uid = 1;
}
message GetUserSampleRedpointRsp {
  bool redpoint = 2;
}