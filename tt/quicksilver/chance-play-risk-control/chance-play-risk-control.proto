syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/chance-play-risk-control";
package chance_play_risk_control;

service ChancePlayRiskControl {
  // 上报奖品价值
  rpc ReportAward(ReportAwardReq) returns (ReportAwardResp) {}
  // 检查当前状态
  rpc CheckStatus(CheckStatusReq) returns (CheckStatusResp) {}
  // 改变当前状态
  rpc ChangeStatus(ChangeStatusReq) returns (ChangeStatusResp) {}
}

enum AppId {
  Unknown = 0;
  SmashEgg = 1;     // 转转
  MagicSpirit = 2;  // 幸运礼物（魔法精灵）
}

// 上报
message ReportAwardReq {
  AppId app_id = 1; // 业务来源id see AppId
  string order_id = 2;
  uint32 uid = 3;
  int64 price = 4;  // 奖品价值
  int64 cost = 5;   // 消费值
}

message ReportAwardResp {
  int64 curr_hour_profit = 1;   // 当前小时利润
  string token = 2;             // 签发娱乐玩法token
}

enum Status {
  Common = 0;
  Confuse = 1;  // 熔断
}

// 检查当前状态
message CheckStatusReq {
  AppId app_id = 1; // 业务来源id see AppId
}

message CheckStatusResp {
  int64 curr_hour_profit = 1;   // 当前小时利润
  Status status = 2;            // 状态 see Status
}

// 改变当前状态
message ChangeStatusReq {
  AppId app_id = 1;             // 业务来源id see AppId
  Status target_status = 2;     // 状态 see Status
}

message ChangeStatusResp {}