syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-heartbeat-go";

package channel_heartbeat_go;

service ChannelHeartbeatGo {
	rpc UpdateUserHeartbeat (UpdateUserHeartbeatReq) returns (UpdateUserHeartbeatResp);
	rpc RemoveUserHeartbeat (RemoveUserHeartbeatReq) returns (RemoveUserHeartbeatResp);
	rpc CheckUserHeartRecordExist (CheckUserHeartRecordExistReq) returns (CheckUserHeartRecordExistResp);
}

message UserHeartbeatInfo {
	uint32 uid = 1;
	uint32 channel_id = 2;
}

message UpdateUserHeartbeatReq {
	UserHeartbeatInfo info = 1;
}

message UpdateUserHeartbeatResp {
}

message RemoveUserHeartbeatReq {
	UserHeartbeatInfo info = 1;
}

message RemoveUserHeartbeatResp {
}

message CheckUserHeartRecordExistReq {
	repeated uint32 uid_list = 1;
}
message CheckUserHeartRecordExistResp {
	repeated  uint32 exist_uid_list = 1;
}