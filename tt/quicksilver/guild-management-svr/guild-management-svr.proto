syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/guild-management-svr";
package guild_management_svr;

service GuildManagementSvr {
  rpc GetGuildAgentList(GetGuildAgentListReq) returns (GetGuildAgentListResp) {
  }
  rpc AddGuildAgent(AddGuildAgentReq) returns (AddGuildAgentResp) {
  }
  rpc DelGuildAgent(DelGuildAgentReq) returns (DelGuildAgentResp) {
  }
  rpc UpdateAgentDataPermission(UpdateAgentDataPermissionReq) returns (UpdateAgentDataPermissionResp) {
  }
  rpc GetAgentGuild(GetAgentGuildReq) returns (GetAgentGuildResp) {
  }
  rpc GetAgentAnchorList(GetAgentAnchorListReq) returns (GetAgentAnchorListResp) {
  }
  rpc GetAgentAnchor(GetAgentAnchorReq) returns (GetAgentAnchorResp) {
  }
  rpc GetAnchorAgent(GetAnchorAgentReq) returns (GetAnchorAgentResp) {
  }
  rpc AddAgentAnchor(AddAgentAnchorReq) returns (AddAgentAnchorResp) {
  }
  rpc DelAgentAnchor(DelAgentAnchorReq) returns (DelAgentAnchorResp) {
  }
  rpc CheckIsNeedVerify(CheckIsNeedVerifyReq) returns (CheckIsNeedVerifyResp) {
  }
  rpc SetVerifyFlag(SetVerifyFlagReq) returns (SetVerifyFlagResp) {
  }
  // 电竞从业者日信息
  rpc GetEsportPractitionerDaily(GetEsportPractitionerDailyReq) returns (GetEsportPractitionerDailyResp) {}
  // 电竞从业者月信息
  rpc GetEsportPractitionerMonthly(GetEsportPractitionerMonthlyReq) returns (GetEsportPractitionerMonthlyResp) {}
  // 电竞游戏月统计信息
  rpc GetEsportGameMonthlyStat(GetEsportGameMonthlyStatReq) returns (GetEsportGameMonthlyStatResp) {}
  // 电竞公会月统计信息
  rpc GetEsportGuildMonthlyStat(GetEsportGuildMonthlyStatReq) returns (GetEsportGuildMonthlyStatResp) {}
  // 电竞公会日统计信息
  rpc GetEsportGuildDailyStat(GetEsportGuildDailyStatReq) returns (GetEsportGuildDailyStatResp) {}

  rpc GetWhiteWhaleAccessToken(GetWhiteWhaleAccessTokenReq) returns (GetWhiteWhaleAccessTokenResp) {}
  rpc CheckWhiteWhaleAccessToken(CheckWhiteWhaleAccessTokenReq) returns (CheckWhiteWhaleAccessTokenResp){}


  // 发送公会代理人邀请
  rpc SendGuildAgentInvite(SendGuildAgentInviteReq) returns (SendGuildAgentInviteResp) {}

  // 获取公会代理人邀请列表
  rpc GetGuildAgentInviteList(GetGuildAgentInviteListReq) returns (GetGuildAgentInviteListResp) {}

  // 撤回邀请
  rpc CancelGuildAgentInvite(CancelGuildAgentInviteReq) returns (CancelGuildAgentInviteResp) {}

  // 代理人角色变更
  rpc ModifyGuildAgent(ModifyGuildAgentReq) returns (ModifyGuildAgentResp) {}

  // 处理公会代理人邀请
  rpc ProcGuildAgentInvite(ProcGuildAgentInviteReq) returns (ProcGuildAgentInviteResp) {}

  // 增加违规数据导出记录
  rpc AddVioExportRecord(AddVioExportRecordReq) returns (AddVioExportRecordResp) {}

  // 获取管理员列表
  rpc GetGuildAdminList(GetGuildAdminListReq) returns (GetGuildAdminListResp) {}

  // 获取多人互房间动任职信息列表
  rpc GetMultiChannelEmploymentInfoList (GetMultiChannelEmploymentInfoListReq) returns (GetMultiChannelEmploymentInfoListResp) {}
  // 获取多人互房间动任职信息
  rpc GetMultiChannelEmploymentInfo (GetMultiChannelEmploymentInfoReq) returns (GetMultiChannelEmploymentInfoResp) {}
  // 设置房间任职成员
  rpc SetMultiChannelEmploymentInfo (SetMultiChannelEmploymentInfoReq) returns (SetMultiChannelEmploymentInfoResp) {}
  // 设置房间厅管
  rpc SetMultiChannelAdminInfo (SetMultiChannelAdminInfoReq) returns (SetMultiChannelAdminInfoResp) {}
  // 批量设置任职成员
  rpc BatchSetMultiChannelEmploymentInfo (BatchSetMultiChannelEmploymentInfoReq) returns (BatchSetMultiChannelEmploymentInfoResp) {}
  // 获取厅管房间
  rpc GetChannelListByAdminUid (GetChannelListByAdminUidReq) returns (GetChannelListByAdminUidResp) {}

  // 公会主播人脸核验展示相关
  // 获取公会主播人脸核验日明细列表
  rpc BatchGetDailyFaceCheckInfo(BatchGetDailyFaceCheckInfoReq) returns (BatchGetDailyFaceCheckInfoResp) {}
  // 获取公会主播人脸核验周明细列表
  rpc BatchGetWeeklyFaceCheckInfo(BatchGetWeeklyFaceCheckInfoReq) returns (BatchGetWeeklyFaceCheckInfoResp) {}
  // 获取公会主播人脸核验公会每周汇总
  rpc BatchGetWeeklySumFaceCheckInfo(BatchGetWeeklySumFaceCheckInfoReq) returns (BatchGetWeeklySumFaceCheckInfoResp) {}
  // 获取公会主播人脸核验公会每日汇总列表
  rpc BatchGetDailySumFaceCheckInfo(BatchGetDailySumFaceCheckInfoReq) returns (BatchGetDailySumFaceCheckInfoResp) {}
  // 根据类型导出公会主播人脸核验明细数据
  rpc ExportFaceCheckInfo(ExportFaceCheckInfoReq) returns (ExportFaceCheckInfoResp) {}

  // 预创建上传文件
  rpc PreCreateUploadFile(PreCreateUploadFileReq) returns (PreCreateUploadFileResp) {}
  // 完成上传文件
  rpc CompleteUploadFile(CompleteUploadFileReq) returns (CompleteUploadFileResp) {}
  // 获取上传文件Url
  rpc BatchGetUploadFileUrl(BatchGetUploadFileUrlReq) returns (BatchGetUploadFileUrlResp) {}
  //获取公会平台资讯banner
  rpc GetBannerInfo(GetBannerInfoReq) returns (GetBannerInfoResp){}


  //角色权限
  // 预设角色权限
  rpc SetRolePermission(SetRolePermissionReq) returns (SetRolePermissionResp) {}

  // 获取预设角色权限
  rpc GetRolePermission(GetRolePermissionReq) returns (GetRolePermissionResp) {}

  // 给uid添加角色
  rpc AddRoleToUid(AddRoleToUidReq) returns (AddRoleToUidResp) {}

  // 批量删除某个角色的uids
  rpc BatchDelRoleToUid(BatchDelRoleToUidReq) returns (BatchDelRoleToUidResp) {}

  // 根据业务类型获得角色列表
  rpc GetRoleUidList(GetRoleUidListReq) returns (GetRoleUidListResp) {}

  // 设置某个uid的权限
  rpc SetUidPermission(SetUidPermissionReq) returns (SetUidPermissionResp) {}

  // 获取某个uid的权限
  rpc GetUidPermission(GetUidPermissionReq) returns (GetUidPermissionResp) {}

  // 设置经纪人和管理员的关系
  rpc AddSuperior(AddSuperiorReq) returns (AddSuperiorResp) {}

  // 删除经纪人和管理员的关系
  rpc DelSuperior(DelSuperiorReq) returns (DelSuperiorResp) {}

  // 获取管理员下面的经纪人
  rpc GetSuperiorAgent(GetSuperiorAgentReq) returns (GetSuperiorAgentResp) {}

  // 设置语音直播和电竞的经纪人管理的uid
  rpc AddAnchorManage(AddAnchorManageReq) returns (AddAnchorManageResp) {}

  // 删除语音直播和电竞的经纪人管理的uid
  rpc DelAnchorManage(DelAnchorManageReq) returns (DelAnchorManageResp) {}

  // 获取语音直播和电竞的经纪人管理的uid
  rpc GetAnchorManage(GetAnchorManageReq) returns (GetAnchorManageResp)  {}

  // 设置多人互动的管理员管理的channel_id
  rpc AddChannelManage(AddChannelManageReq) returns (AddChannelManageResp) {}

  // 删除多人互动的管理员管理的channel_id
  rpc DelChannelManage(DelChannelManageReq) returns (DelChannelManageResp) {}

  // 获取多人互动的管理员管理的channel_id
  rpc GetChannelManage(GetChannelManageReq) returns (GetChannelManageResp) {}

  // 设置托管
  rpc AddManageTrust(AddManageTrustReq) returns (AddManageTrustResp) {}

  // 删除托管
  rpc DelManageTrust(DelManageTrustReq) returns (DelManageTrustResp) {}

  // 获取是否托管
  rpc GetManageTrust(GetManageTrustReq) returns (GetManageTrustResp) {}

  // 旧版权限迁移新版
  rpc MovePermission(Empty) returns (Empty) {}

  //触发任务
  rpc TriggerTask(TriggerTaskReq) returns (TriggerTaskResp) {}
}

//******  角色权限 ******//
message Empty {
}

message GetSuperiorAgentReq {
  uint32 guild_id = 1; // 公会id
  BusinessType b_type = 2;
  uint32 superior_uid = 3;  // 0给所有有管理员的经纪人
}

message AgentData {
  uint32 uid = 1;
  int64 create_time = 2;
  string alias = 3; //别名
  uint32 superior_uid = 4;
}

message GetSuperiorAgentResp {
  repeated AgentData list = 1;
}


message GetRoleUidListReq {
  uint32 guild_id = 1; // 公会id
  BusinessType b_type = 2;
  repeated uint32 uid_list = 3;  //空数组就给全部，否则筛选
}

message GetRoleUidListResp {
  repeated GuildAgent uid_list = 1; //管理员和经纪人列表
}

message GetChannelManageReq {
  uint32 guild_id = 1; // 公会id
  uint32 manager_uid = 2; //传0，就是查全部
}

message ChannelManageData {
  uint32 channel_id = 1;
  int64 create_time = 2;
  uint32 manager_uid = 3;
}

message GetChannelManageResp {
  repeated ChannelManageData list = 1;
}


message GetManageTrustReq {
  uint32 guild_id = 1; // 公会id
  BusinessType b_type = 2;
}

message TrustData {
  uint32 uid = 1;
  int64 create_time = 2;  //0是没有托管，非0就是有托管
}

message GetManageTrustResp {
  repeated TrustData list = 1;
}

message DelManageTrustReq {
  uint32 guild_id = 1; // 公会id
  BusinessType b_type = 2;
  uint32 uid = 3;
}

message DelManageTrustResp {
}

message AddManageTrustReq {
  uint32 guild_id = 1; // 公会id
  BusinessType b_type = 2;
  uint32 uid = 3;
}

message AddManageTrustResp {
}


message DelChannelManageReq {
  uint32 guild_id = 1; // 公会id
  repeated uint32 channel_id_list = 2;
  uint32 manager_uid = 3;
}

message DelChannelManageResp {
}


message AddChannelManageReq {
  uint32 guild_id = 1; // 公会id
  repeated uint32 channel_id_list = 2;
  uint32 manager_uid = 3;
}

message AddChannelManageResp {
}

message GetAnchorManageReq {
  uint32 guild_id = 1; // 公会id
  repeated BusinessType b_type_list = 2;
  repeated uint32 agent_uid_list = 3; //非空指定查询
  uint32 offset = 4;
  uint32 limit = 5;
  repeated uint32 uid_list = 6; //非空指定查询
}

message AnchorMangeData {
  uint32 uid = 1;
  int64 create_time = 2;
  uint32 agent_uid = 3;
  BusinessType b_type = 4;
}

message GetAnchorManageResp {
  repeated AnchorMangeData list = 1;
  uint32 total = 2;
}

message DelAnchorManageReq {
  uint32 guild_id = 1; // 公会id
  BusinessType b_type = 2;
  repeated uint32 uid_list = 3;
  uint32 agent_uid = 4;
}

message DelAnchorManageResp {
}

message AddAnchorManageReq {
  uint32 guild_id = 1; // 公会id
  BusinessType b_type = 2;
  repeated uint32 uid_list = 3;
  uint32 agent_uid = 4;
}

message AddAnchorManageResp {
}

message DelSuperiorReq {
  uint32 guild_id = 1; // 公会id
  BusinessType b_type = 2;
  repeated uint32 uid_list = 3;
  uint32 superior_uid = 4;
}

message DelSuperiorResp {
}

message AddSuperiorReq {
  uint32 guild_id = 1; // 公会id
  BusinessType b_type = 2;
  repeated uint32 uid_list = 3;
  uint32 superior_uid = 4;
}

message AddSuperiorResp {
}

message SetRolePermissionReq {
  uint32 guild_id = 1; // 公会id
  BusinessType b_type = 2;
  AgentType r_type = 3;
  GuildManagePermission permission = 4; // 权限
}

message SetRolePermissionResp {
}

message GetRolePermissionReq {
  uint32 guild_id = 1; // 公会id
  BusinessType b_type = 2;
  AgentType r_type = 3;
}

message GetRolePermissionResp {
  GuildManagePermission permission = 1; // 权限
}

message AddRoleToUidReq {
  uint32 guild_id = 1; // 公会id
  uint32 uid = 2;
  BusinessType b_type = 3;
  AgentType r_type = 4;
  string alias = 5;
}

message AddRoleToUidResp {
}

message BatchDelRoleToUidReq {
  uint32 guild_id = 1; // 公会id
  BusinessType b_type = 2;
  repeated uint32 uid_list = 3;
}

message BatchDelRoleToUidResp {
}

message SetUidPermissionReq {
  uint32 guild_id = 1; // 公会id
  uint32 uid = 2;
  BusinessType b_type = 3;
  GuildManagePermission permission = 4; // 权限
}

message SetUidPermissionResp {
}

message UidPermissionData {
  BusinessType b_type = 1;
  AgentType r_type = 2;
  GuildManagePermission permission = 3; // 权限
  string alias = 4;
}

message GetUidPermissionReq {
  uint32 guild_id = 1; // 公会id
  uint32 uid = 2;
}

message GetUidPermissionResp {
  repeated UidPermissionData permission_list = 1;
}

//******  菜单权限 ******//

// 公会经营后台语音直播经营分析菜单栏 移位定义 1,2,4,8...
enum GuildLiveDataMenuType {
  GuildLiveDataInvalid = 0;
  GuildLiveStatsTotalData = 1;   //经营情况总览
  GuildLiveTaskData = 2;   // 公会任务
  GuildAnchorData = 4;   // 主播数据
  AnchorPractitionerAnalysis = 8;  // 从业者分析
  GuildBusinessDiagnosis = 16;   // 经营诊断
  AnchorFaceCheckDetail = 32;   // 主播人脸识别失败明细
}

// 公会经营后台经纪人菜单栏 移位定义 1,2,4,8...
enum GuildAgentMenuType {
  GuildAgentInvalid = 0;
  GuildAgentManage = 1;   // 经纪人管理
}

// 公会经营后台收益管理菜单栏 移位定义 1,2,4,8...
enum GuildIncomeMenuType {
  GuildIncomeInvalid = 0;
  GuildIncomeManage = 1;   // 收益管理
}

// 公会经营后台多人互动经营管理菜单栏 移位定义 1,2,4,8...
enum GuildMultiPlayDataType {
  GuildMultiPlayDataInvalid = 0;
  MultiPlayOperationData = 1; // 经营总览
  MultiPlayDetail = 2; // 数据明细
  MultiPlayPractitionerAnalysis = 4; // 从业者分析
  MultiPlayHallTask = 8; // 承接大厅
  MultiPlayScheduleData = 16; // 接档管理
  MultiAnchorFaceCheckDetail = 32;   // 主播人脸识别失败明细
  MultiPlayWeddingReserved = 64;   // 婚礼预约
  MultiPlayBusinessDiag = 128;  // 经营分析
}

// 流量卡管理菜单栏 移位定义 1,2,4,8...
enum FlowCardMenuType {
  FlowCardInValid = 0;
  AnchorFlowCard = 1;   // 主播流量卡
}

// 签约管理菜单栏 移位定义 1,2,4,8...
enum AnchorContractMenuType {
  AnchorContractInValid = 0;
  AnchorContractApplySign = 1; // 签约申请
  AnchorContractCancel = 2;    // 解约申请
  AnchorContractList = 4;      // 签约成员列表
  AnchorContractVio = 8;   // 违规记录
}

// 消息管理 移位定义 1,2,4,8...
enum GuildMsgMenuType {
  GuildMsgInValid = 0;
  FuWuHaoMsg = 1;  // 平台公告
}

// 公会管理 移位定义 1,2,4,8...
enum GuildManageMenuType {
  GuildManageInValid = 0;
  PersonnelManage = 1;  // 人员设置
  LiveManage = 2; // 开播管理
}

//首页管理 移位定义 1,2,4,8...
enum HomePageMenuType {
  HomePageMenuTypeInValid = 0;  //无效
  HomePageMenuTypeMulti = 1;  // 多人互动
}

//******  菜单权限 ******//

//****** 数据权限 ******//

// 语音直播经营分析预览数据类型  移位定义 1,2,4,8...
enum GuildLiveStatsDataType {
  LiveStatsDataInvalid = 0;
  AnchorIncome  = 1;  // 主播收入
  ChannelFee = 2;    // 直播间收入
  ChannelPkgFeeRatio = 4;  // 直播间背包流水占比
  PureNewAnchorFee = 8;   // 纯新达人流水
}

// 公会直播任务模块数据类型 移位定义 1,2,4,8...
enum GuildLiveTaskDataType {
  LiveTaskDataInvalid = 0;
  MonthChannelFee = 1;   // 月直播流水
  MonthValidAnchorCnt = 2;  // 月有效主播数
  MonthAddValidAnchorCnt = 4; // 月新增有效主播数
  MonthPotentialAnchorCnt = 8;  // 月潜力主播数
}

// 主播数据数据类型 移位定义 1,2,4,8...
enum AnchorDataType {
  AnchorDataInvalid = 0;
  ChannelFeeData = 1;   // 直播间流水
  AnchorIncomeData = 2; // 主播流水
  SignTsData = 4;  // 签约时间
  SignExpireTsData = 8;  // 签约到期时间
  ChannelPkgFeeRatioData = 16;  // 直播间背包流水占比
  AnchorPkgIncomeRatioData = 32; // 主播收礼背包占比
}

// 语音直播从业者数据类型 移位定义 1,2,4,8...
enum AnchorPracDataType {
  AnchorPracDataInvalid = 0;
  PracChannelFee = 1;   // 直播间流水
  PracAnchorIncome = 2; // 主播流水
}

// 多人互动经营管理数据明细数据类型 移位定义 1,2,4,8...
enum MultiPlayDetailDataType {
  MultiPlayDetailDataInvalid = 0;
  MultiPlayDetailSignTs = 1;   // 签约时间
  MultiPlayDetailSignExpireTs = 2; // 签约到期时间
  MultiPlayDetailGuildFee = 4;  // 公会流水（T豆）
  MultiPlayDetailChGiftFee = 8;  // 房间收礼金额（元）
  MultiPlayDetailChWolfFee = 16;  // 房间狼人杀金额（元）
  MultiPlayDetailChannelFee = 32;  // 房间总流水金额（元）
}

// 多人互动经营管理从业者分析数据类型 移位定义 1,2,4,8...
enum MultiPlayPracAnalysisDataType {
  MultiPlayPracAnalysisDataInvalid = 0;
  MultiPlayPracAnalysisGiftFee = 1;   // 收礼金额（元）
  MultiPlayPracAnalysisSignTs = 2; // 签约时间
  MultiPlayPracAnalysisChannelFee = 4;  // 房间流水（元）
  MultiPlayPracAnalysisActiveFee = 8;  // 活跃从业者贡献流水（元）
  MultiPlayPracAnalysisQualityFee = 16;  // 优质从业者贡献流水（元）
  MultiPlayPracAnalysisQualityFeeRat = 32;  // 优质从业者贡献流水占比
  MultiPlayPracAnalysisPracGiftFee= 64;  // 从业者收礼流水（元）
  MultiPlayPracAnalysisPkgFee= 128;  // 包裹礼物流水（元）
  MultiPlayPracAnalysisTbeanGiftFee= 256;  // T豆礼物流水（元）
  MultiPlayPracAnalysisGuildFee = 512; // 公会月流水（元）
  MultiPlayPracAnalysisChannelPkgFee = 1024; // 房间背包流水占比
  MultiPlayPracAnalysisGuildPkgFee = 2048; // 公会背包流水占比
  MultiPlayPracAnalysisPkgFeeRat = 4096; // 背包流水占比
}

//多人互动经营管理经营总览数据类型 移位定义 1,2,4,8....
enum MultiPlayChannelOperationDataType {
  MultiPlayChannelOperationDataInvalid = 0;
  MultiPlayChannelOperationDataThisMonthFee = 1;  //房间当月流水
  MultiPlayChannelOperationDataLastMonthSamePeriodFee = 2;  //上月同期流水
  MultiPlayChannelOperationDataChainRatio = 4;  //月环比
  MultiPlayChannelOperationDataLastMonthFee = 8;  //房间上月整月流水 
}

//多人互动经营管理接档管理数据类型 移位定义 1,2,4,8....
enum MultiPlayScheduleDataType {
  MultiPlayScheduleDataInvalid = 0;
  MultiPlayScheduleDataReceiveGiftTotal = 1;  //接档期间收礼
  MultiPlayScheduleDataReceiveHundredGiftTotal = 2;  //收到百元礼物总值
}

// EsportMenuType 电竞经营管理菜单栏 移位定义 1,2,4,8...
enum EsportMenuType {
  EsportMenuTypeValid = 0;
  EsportMenuTypeAudit = 1;// 修改技能审核
  EsportMenuTypePerformanceAnalysis = 2;// 电竞业绩分析
}


//****** 数据权限 ******//

//****** 功能权限 ******//
// 签约申请功能权限 移位定义 1,2,4,8...
enum ContractApplySignFuncType {
  ContractApplySignInvalid = 0;
  ContractApplySignQuery = 1;  // 查询
  ContractApplySignApproval = 2;  // 审批
}

// 解约申请功能权限 移位定义 1,2,4,8...
enum ContractCancelFuncType {
  ContractCancelInvalid = 0;
  ContractCancelQuery = 1;  // 查询
  ContractCancelApproval = 2;  // 审批
}

// 签约成员列表功能权限 移位定义 1,2,4,8...
enum ContractListFuncType {
  ContractListInvalid = 0;
  ContractListQuery = 1;  // 查询
  ContractListCancelApproval = 2;  // 解约审批
  ContractListRenewApproval = 4;  // 续约审批
  ContractListExport = 8;  // 导出
}

// 违规记录功能权限 移位定义 1,2,4,8...
enum ContractVioFuncType {
  ContractVioInvalid = 0;
  ContractVioQuery = 1;  // 查询
  ContractVioExport = 2;  // 导出
}

// 语音直播经营情况总览功能权限 移位定义 1,2,4,8...
enum LiveTotalDataFuncType {
  LiveTotalDataInvalid = 0;
  LiveTotalDataQuery = 1;  // 查询
  LiveTotalDataExport = 2;  // 导出
}

// 语音直播公会任务功能权限 移位定义 1,2,4,8...
enum LiveTaskDataFuncType {
  LiveTaskDataFuncInvalid = 0;
  LiveTaskDataQuery = 1;  // 查询
}

// 语音直播主播数据功能权限 移位定义 1,2,4,8...
enum AnchorDataFuncType {
  AnchorDataFuncInvalid = 0;
  AnchorDataQuery = 1;  // 查询
  AnchorDataExport = 2;  // 导出
  AnchorDataQueryAllAnchor = 4;  //查询所有达人
  AnchorDataQueryManageAnchor = 8;  //查询管理的达人
}

// 语音直播从业者分析功能权限 移位定义 1,2,4,8...
enum AnchorPracAnalysisFuncType {
  AnchorPracAnalysisInvalid = 0;
  AnchorPracAnalysisQuery = 1;  // 查询
  AnchorPracAnalysisExport = 2;  // 导出
}

// 主播流量卡功能权限 移位定义 1,2,4,8...
enum FlowCardFuncType {
  FlowCardInvalid = 0;
  FlowCardQuery = 1;  // 查询
  FlowCardUse = 2;  // 使用流量卡
  FlowCardGrant = 4;  // 发放流量卡
}

// 多人互动经营管理数据明细功能权限 移位定义 1,2,4,8...
enum MultiPlayDetailFuncType {
  MultiPlayDetailInvalid = 0;
  MultiPlayDetailQuery = 1;  // 查询
  MultiPlayDetailExport = 2;  // 导出
  MultiPlayDetailTabAnchorDetail = 4;  // 签约成员个人明细数据tab
  MultiPlayDetailTabChannelStat = 8;  // 成员各房间接档数据tab
}

// 多人互动经营从业者分析功能权限 移位定义 1,2,4,8...
enum MultiPlayPracAnalysisFuncType {
  MultiPlayPracAnalysisInvalid = 0;
  MultiPlayPracAnalysisQuery = 1;  // 查询
  MultiPlayPracAnalysisExport = 2;  // 导出
  MultiPlayPracAnalysisTabPractitioner = 4;  //个人表现tab
  MultiPlayPracAnalysisTabPgc = 8;  //房间表现tab
  MultiPlayPracAnalysisTabGuildStat = 16;  //公会整体tab
}

// 多人互动经营经营总览功能权限 移位定义 1,2,4,8...
enum MultiPlayOperationFuncType {
  MultiPlayOperationInvalid = 0;
  MultiPlayOperationQuery = 1;  // 查询
}

// 消息管理 功能权限 移位定义 1,2,4,8...
enum GuildMsgFuncType {
  GuildMsgInvalid = 0;
  GuildMsgQuery = 1;  // 查询
}

// 开播管理 功能权限 移位定义 1,2,4,8...
enum LiveManageFuncType {
  LiveManageInvalid = 0;
  LiveManageQuery = 1;  // 查看
  LiveManageExport = 2;  // 导出
}

// 电竞经营管理 技能功能权限 移位定义 1,2,4,8...
enum EsportManageSkillFuncType {
  EsportManageSkillInvalid = 0;
  EsportManageSkillQuery = 1;  //数据查看
  EsportManageSkillEdit = 2;  // 审核权限
}

// 电竞经营管理 业绩分析功能权限 移位定义 1,2,4,8...
enum EsportManagePerformanceFuncType {
  EsportManagePerformanceInvalid = 0;
  EsportManagePerformanceQuery = 1;  //数据查看
  EsportManagePerformanceExport = 2;  // 导出
}

// 接档管理 功能权限 移位定义 1,2,4,8...
enum MultiScheduleDataFuncType {
  MultiScheduleDataInvalid = 0;
  MultiScheduleDataQuery = 1;  // 查询
  MultiScheduleDataExport = 2;  // 导出
  MultiScheduleDataSetAdmin = 4;  // 设置厅管
  MultiScheduleDataTabScheduleData = 8; //接档数据tab
  MultiScheduleDataTabEmployment = 16;  //房间任职信息tab
}

//经营诊断 功能权限 移位定义 1,2,4,8...
enum BusinessDiagnosisFuncType {
  BusinessDiagnosisInvalid = 0;
  BusinessDiagnosisQuery = 1;  // 查看
  BusinessDiagnosisExport = 2;  // 导出
  BusinessDiagnosisAbility = 4;  // 能力分析
  BusinessDiagnosisMgr = 8;   // 经营分析
  BusinessDiagnosisRevenue = 16;   // 营收
  BusinessDiagnosisRecruit = 32;  // 拉新
}

// 语音直播从业者本人验证 功能权限 移位定义 1,2,4,8...
enum LiveAnchorFaceCheckDetailFuncType {
  LiveAnchorFaceCheckDetailInvalid = 0;
  LiveAnchorFaceCheckDetailQuery = 1;  // 查看
  LiveAnchorFaceCheckDetailExport = 2;  // 导出
  LiveAnchorFaceCheckDetailQueryAllAnchor = 4;  // 查询所有达人
  LiveAnchorFaceCheckDetailQueryManageAnchor = 8;   //查询管理的达人 
}

// 多人互动经营从业者本人验证 功能权限 移位定义 1,2,4,8...
enum MultiAnchorFaceCheckDetailFuncType {
  MultiAnchorFaceCheckDetailInvalid = 0;
  MultiAnchorFaceCheckDetailQuery = 1;  // 查看
  MultiAnchorFaceCheckDetailExport = 2;  // 导出
  MultiAnchorFaceCheckDetailTabUserFaceCheck = 4;  // 个人认证记录tab
  MultiAnchorFaceCheckDetailTabGuildFaceCheck = 8;  // 公会认证情况tab
}

//首页管理多人互动 功能权限 移位定义 1,2,4,8...
enum HomePageMultiFuncType {
  HomePageMultiFuncInValid = 0; //无效
  HomePageMultiFuncGuildMultiKeyData = 1;  //公会多人互动重点数据 
  HomePageMultiFuncBanner = 2; // 公会平台资讯
  HomePageMultiFuncMultiTopChannel = 4;  // 公会top公开厅
}

// 多人互动经营分析 功能权限 移位定义 1,2,4,8...
enum MultiBusinessDiagFuncType {
  MultiBusinessDiagFuncInValid = 0;  //无效
  MultiBusinessDiagFuncRadarChart = 1;  // 能力星图
  MultiBusinessDiagFuncRevenue = 2; // 营收tab
  MultiBusinessDiagFuncRecruit = 4; // 招新tab
  MultiBusinessDiagFuncHatch = 8; // 孵化tab
  MultiBusinessDiagFuncSafety = 16; // 安全tab
  MultiBusinessDiagFuncStability = 32; // 稳定tab
  MultiBusinessDiagFuncRiskResistance = 64; // 抗风险tab
}

// 婚礼预约 功能权限 移位定义 1,2,4,8...
enum MultiPlayWeddingReservedFuncType {
  MultiPlayWeddingReservedInvalid = 0;
  MultiPlayWeddingReservedQuery = 1;  // 查看
  MultiPlayWeddingReservedExport = 2;  // 导出
}

//人员设置 功能权限 移位定义 1,2,4,8...
enum PersonnelManageFuncType {
  PersonnelManageFuncInValid = 0;  //无效
  PersonnelManageFuncAgentAdd = 1; //新增角色tab
  PersonnelManageFuncPerPreset = 2;  //管理权限预设tab
  PersonnelManageFuncAgentInviting = 4;  //邀请中tab
  PersonnelManageFuncPerEdit = 8;  //编辑权限
  PersonnelManageFuncAgentModify = 16;  //角色变更
  PersonnelManageFuncAnchorManage = 32;  //管理达人/管理大神
  PersonnelManageFuncAgentDel = 64;  //删除角色
  PersonnelManageFuncBrokerManage = 128;  //管理经纪人
  PersonnelManageFuncTabMulti = 256;  // 多人互动tab
  PersonnelManageFuncTabLive = 512;  // 听听tab
  PersonnelManageFuncTabESport = 1024;  // 电竞tab
  PersonnelManageFuncChannelManage = 2048;  //管理房间
  PersonnelManageFuncESportManage = 4096;  //管理大神
}



//****** 功能权限 ******//



// 经营后台菜单权限
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message MenuPermission {
  uint32  guildLiveDataMenuPer = 1; // 公会经营后台语音直播经营分析菜单栏 see GuildLiveDataMenuType
  uint32  guildAgentMenuPer = 2;   // 公会经营后台经纪人菜单栏 see GuildAgentMenuType
  uint32  flowCardMenuPer = 3;       // 流量卡管理菜单栏 see FlowCardMenuType
  uint32  contract = 4;         // 签约管理菜单栏 see AnchorContractMenuType
  uint32  multiPlay = 5;      // 公会经营后台多人互动经营管理菜单栏 see GuildMultiPlayDataType
  uint32  msg = 6;  //   消息管理 see GuildMsgMenuType
  uint32  guildManage = 7;  //公会管理 see GuildManageMenuType
  uint32  esportMenuPer = 8;  // 电竞经营管理 see EsportMenuType
  uint32  homePageMenuPer = 9;  // 首页管理 see HomePageMenuType
}

// 语音直播数据权限配置
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message LiveDataPermission {
  uint32 guildLiveStatsDataPer = 1;  // 公会经营分析预览数据权限 see GuildLiveStatsDataType
  uint32 guildTaskDataPer = 2;   // 公会任务模块数据权限 see GuildTaskDataType
  uint32 anchorDataPer = 3;      // 主播数据权限  see AnchorDataType
  uint32 anchorPracDataPer = 4;   // 主播从业者数据 see AnchorPracDataType
}

// 功能权限配置
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message FunctionPermission {
  uint32 applySign = 1;  // 签约申请功能权限 see ContractApplySignFuncType
  uint32 cancelSign = 2;  // 解约申请功能权限 see ContractCancelFuncType
  uint32 contractList = 3;  // 签约成员列表功能权限 see ContractListFuncType
  uint32 contractVio = 4; // 违规记录功能权限 see ContractVioFuncType
  uint32 liveTotalData = 5;  // 语音直播经营情况总览功能权限 see LiveTotalDataFuncType
  uint32 liveTaskData = 6;  // 语音直播公会任务功能权限 see LiveTaskDataFuncType
  uint32 anchorData = 7;  // 语音直播主播数据功能权限 see AnchorDataFuncType
  uint32 anchorPracAnalysis = 8; // 语音直播从业者分析功能权限  see AnchorPracAnalysisFuncType
  uint32 flowCard = 9; // 主播流量卡功能权限 see FlowCardFuncType
  uint32 multiPlayDetail = 10;  // 多人互动经营管理数据明细功能权限 see MultiPlayDetailFuncType
  uint32 multiPlayPracAnalysis = 11;  // 多人互动经营从业者分析功能权限  see MultiPlayPracAnalysisFuncType
  uint32 msg = 12;  // 消息管理 功能权限 see GuildMsgFuncType
  uint32 multiPlayOper = 13;  // 多人互动经营经营总览 功能权限 see MultiPlayOperationFuncType
  uint32 multiPlayHallTask = 14; // 承接大厅 see MultiPlayHallTaskFuncType
  uint32 liveManage = 15;  // 开播管理 see LiveManageFuncType
  uint32 esportManageSkillFuncPer = 16;  // 电竞经营管理（技能审核） see EsportManageSkillFuncType
  uint32 esportManagePerformanceFuncPer = 17;  // 电竞经营管理（业绩分析） see EsportManagePerformanceFuncType
  uint32 multiScheduleDataFuncPer = 18;  // 多人互动经营管理接档管理功能权限 see MultiScheduleDataFuncType
  uint32 businessDiag = 19;   //经营诊断 功能权限         see BusinessDiagnosisFuncType
  uint32 liveAnchorFaceCheckDetail = 20;   //语音从业者本人验证 功能权限        see LiveAnchorFaceCheckDetailFuncType
  uint32 multiAnchorFaceCheckDetail = 21;   //多人互动从业者本人验证 功能权限        see MultiAnchorFaceCheckDetailFuncType
  uint32 multiWeddingReservedFuncPer = 22;   //婚礼预约 功能权限       see MultiPlayWeddingReservedFuncType
  uint32 homePageMultiFuncPer = 23;  // 首页管理多人互动 功能权限 see HomePageMultiFuncType 
  uint32 multiBusinessDiagFunc = 24; // 多人互动经营分析 功能权限 see MultiBusinessDiagFuncType
  uint32 personnelManage = 25;   // 人员设置 功能权限 see PersonnelManageFuncType
}

// 数据权限配置
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DataPermission {
  uint32 liveStats = 1;  // 公会经营分析预览数据权限 see GuildLiveStatsDataType
  uint32 taskData = 2;   // 公会任务模块数据权限 see GuildTaskDataType
  uint32 anchorData = 3;      // 主播数据权限  see AnchorDataType
  uint32 anchorPrac = 4;   // 主播从业者数据 see AnchorPracDataType
  uint32 multiPlayDetail = 5; // 多人互动经营管理数据明细数据 see MultiPlayDetailDataType
  uint32 MultiPlayPracAnalysis = 6; // 多人互动经营管理从业者分析数据 see MultiPlayPracAnalysisDataType
  uint32 MultiPlayChannelOperation = 7;   // 多人互动经营管理经营总览数据  see MultiPlayChannelOperationDataType
  uint32 MultiPlaySchedule = 8;   //多人互动经营管理接档管理数据       see MultiPlayScheduleDataType
}

// 公会经营后台权限
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GuildManagePermission {
  MenuPermission guildManageMenuPer = 1;  // 菜单权限
  LiveDataPermission liveDataPermission = 2;  // 语音直播经营分析数据权限 新的使用DataPermission，准备弃用这个字段
  FunctionPermission  functionPer = 3;  // 功能权限
  DataPermission  dataPer = 4;   // 数据权限
}

// 采用移位定义， 0,1,2,4,8...
// 公会代理人类型
enum AgentType {
  InValidAgent = 0; // 无效
  AgentBroker = 1;  // 经纪人
  AgentAdmin = 2;  // 管理员
}

//业务类型
enum BusinessType {
  BusinessType_InValid = 0;  //无效
  BusinessType_MultiPlay = 1;  // 多人互动
  BusinessType_RadioLive = 2;  // 语音直播
  BusinessType_ESports = 3;  // 电竞
}

// 公会代理人
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GuildAgent {
  uint32 uid = 1;
  GuildManagePermission permission = 2; // 权限
  uint32 add_ts = 3;  // 经纪人添加时间
  uint32 guild_Id = 4;
  uint32 agent_type = 6;  // 角色类型 see AgentType 拥有经纪人和管理员身份值为3
  uint32 admin_add_ts = 7;  // 管理员添加时间
  string alias = 8;
  repeated UidPermissionData role_list = 9;   // 代理人多个业务的角色列表
}

// 获取公会代理人列表
message GetGuildAgentListReq{
  uint32 guild_id = 1;   // 公会id
  repeated uint32 uid_list =2;  // 指定uid查询
  uint32 agent_type = 3;  // 指定type查询 see AgentType
}
message GetGuildAgentListResp{
  repeated GuildAgent agent_list = 1;
}

// 新增公会代理人
message AddGuildAgentReq {
  uint32 guild_id = 1;   // 公会id
  uint32 agent_uid = 2;  // 代理人uid
  GuildManagePermission permission = 3;  // 权限
  uint32 agent_type = 4; // see AgentType
}
message AddGuildAgentResp {
}

// 删除公会代理人
message DelGuildAgentReq {
  uint32 guild_id = 1;   // 公会id
  uint32 agent_uid = 2;  // 代理人uid
  uint32 agent_type = 3; // see AgentType
}
message DelGuildAgentResp {
}

// 更新代理人的数据权限
message UpdateAgentDataPermissionReq {
  uint32 guild_id = 1;   // 公会id
  uint32 agent_uid = 2;  // 代理人uid
  GuildManagePermission permission = 3;  // 权限
}
message UpdateAgentDataPermissionResp {
}

// 获取代理人的公会信息
message GetAgentGuildReq {
  uint32 agent_uid = 1;  // 代理人uid
}
message GetAgentGuildResp {
  GuildAgent info = 1;
}


// 主播信息
message AgentAnchorInfo {
  uint32 anchor_uid = 1;
  uint32 agent_uid = 2;
}

// 获取经纪人的主播信息列表
message GetAgentAnchorListReq {
  uint32 agent_uid = 1;
  uint32 page = 2;
  uint32 page_size = 3;
}
message GetAgentAnchorListResp {
  repeated AgentAnchorInfo info_list = 1;
  uint32 next_page = 2;
  uint32 total_cnt = 3;
}

// 获取经纪人的指定主播信息 
message GetAgentAnchorReq {
  uint32 agent_uid = 1;
  uint32 anchor_uid = 2;
}
message GetAgentAnchorResp {
  AgentAnchorInfo info = 1;
}

// 获取主播的经纪人信息
message GetAnchorAgentReq {
  uint32 anchor_uid = 1;
}
message GetAnchorAgentResp {
  AgentAnchorInfo info = 1;
}

message AnchorErrorMsg {
  uint32 uid = 1;
  string err_msg = 2;
}

// 增加经纪人管理主播
message AddAgentAnchorReq {
  uint32 agent_uid = 1;
  repeated uint32 uid_list = 2;
  uint32 guild_id = 3;
}
message AddAgentAnchorResp {
  repeated AnchorErrorMsg err_list = 1;
}

// 删除经纪人主播
message DelAgentAnchorReq {
  uint32 agent_uid = 1;
  repeated uint32 uid_list = 2;
  uint32 guild_id = 3;
}
message DelAgentAnchorResp {
}


enum VerifyScene {
  VerifySceneInvalid = 0;  // 无效
  VerifySceneAddAgent = 1;  // 增加公会代理人
  VerifySceneDelAgent = 2;  // 删除公会代理人
}

// 校验是否需要拉起验证
message CheckIsNeedVerifyReq {
  uint32 scene  = 1 ;   // 校验场景 see VerifyScene
  uint32 uid = 2;
}
message CheckIsNeedVerifyResp {
  bool  is_need = 1;  // 是否需要拉取验证
}

// 设置验证成功标识
message SetVerifyFlagReq {
  uint32 scene = 1;   // 校验场景 see VerifyScene
  uint32 uid = 2;
}
message SetVerifyFlagResp {
}

// EsportPractitionerMonthly 电竞从业者月信息
message EsportPractitionerMonthly{
  uint32 date_time = 1;  // 时间
  uint32 guild_id = 2; // 公会id
  uint32 uid = 3; // 从业者uid
  uint32 order_amt = 4;  // 订单总金额(T豆)
  uint32 order_cnt = 5;  // 订单总数
  uint32 order_day_cnt = 6;  // 接单天数
  uint32 active_day_cnt = 7; // 活跃天数
  uint32 served_user_cnt = 8; // 服务人数
  uint32 new_pay_user_cnt = 9; // 新客人数
  uint32 repay_user_cnt = 10; // 复购人数
}

// EsportPractitionerDaily 电竞从业者日信息
message EsportPractitionerDaily{
  uint32 date_time = 1;  // 时间
  uint32 guild_id = 2; // 公会id
  uint32 uid = 3; // 从业者uid
  uint32 order_amt = 4;  // 订单总金额(T豆)
  uint32 order_cnt = 5;  // 订单总数
  uint32 served_user_cnt = 6; // 服务人数
  uint32 new_pay_user_cnt = 7; // 新客人数
  uint32 new_repay_user_cnt = 8; // 复购人数
}

// EsportGameMonthlyStat 电竞游戏月统计信息
message EsportGameMonthlyStat {
  uint32 date_time = 1;
  uint32 guild_id = 2;
  uint32 game_id = 3;
  uint32 order_amt = 4; // 订单总金额(T豆)
  uint32 order_cnt = 5; // 订单总数
  uint32 coach_cnt = 6; // 指导人数
  uint32 take_orders_coach_cnt = 7; // 接单指导人数
  uint32 served_user_cnt = 8; // 服务人数
}

message EsportGuildMonthlyStat {
  uint32 date_time = 1;
  uint32 guild_id = 2;
  uint32 order_amt = 3;  // 订单总金额(T豆)
  uint32 order_cnt = 4;  // 订单总数
  uint32 served_user_cnt = 5; // 服务人数
  uint32 coach_cnt = 6; // 指导人数
  uint32 new_coach_cnt = 7; // 新增指导人数
}

message EsportGuildDailyStat {
  uint32 date_time = 1;
  uint32 guild_id = 2;
  uint32 income_amt = 3; // 当日收益(金钻)
  uint32 order_amt = 4; // 当日收益(T豆)
  uint32 lst_d_income_amt = 5; // 前一日收益(金钻)
  uint32 lst_d_order_amt = 6; // 前一日收益(T豆)
  double income_amt_dod = 7; // 当日收益环比百分比(金钻)
  double order_amt_dod = 8; // 当日收益环比百分比(T豆)
  uint32 lst_m_income_amt = 9; // 上月同一日收益(金钻)
  uint32 lst_m_order_amt = 10; // 上月同一日收益(T豆)
}

// 获取电竞从业者日信息列表 (取左右闭区间)
message GetEsportPractitionerDailyReq {
  uint32 begin_date_time = 1;
  uint32 end_date_time = 2;
  repeated uint32 uid_list = 3;
  uint32 page = 4;
  uint32 page_size = 5;
  repeated uint32 guild_list = 6;
}

message GetEsportPractitionerDailyResp {
  repeated EsportPractitionerDaily info_list = 1;
  uint32 total = 2;
}

// 获取电竞从业者月信息列表 (取左右闭区间)
message GetEsportPractitionerMonthlyReq {
  uint32 begin_date_time = 1;
  uint32 end_date_time = 2;
  repeated uint32 uid_list = 3;
  uint32 page = 4;
  uint32 page_size = 5;
  repeated uint32 guild_list = 6;
}

message GetEsportPractitionerMonthlyResp {
  repeated EsportPractitionerMonthly info_list = 1;
  uint32 total = 2;
}

// 获取电竞游戏月统计信息 (取左右闭区间)
message GetEsportGameMonthlyStatReq {
  uint32 begin_date_time = 1;
  uint32 end_date_time = 2;
  repeated uint32 guild_list = 3;
  repeated uint32 game_id = 4; // 游戏名称列表
  uint32 page = 5;
  uint32 page_size = 6;
}

message GetEsportGameMonthlyStatResp {
  repeated EsportGameMonthlyStat info_list = 1;
  uint32 total = 2;
}

// 获取电竞公会月统计信息 (取左右闭区间)
message GetEsportGuildMonthlyStatReq {
  uint32 begin_date_time = 1;
  uint32 end_date_time = 2;
  repeated uint32 guild_list = 3;
  uint32 page = 4;
  uint32 page_size = 5;
}

message GetEsportGuildMonthlyStatResp {
  repeated EsportGuildMonthlyStat info_list = 1;
  uint32 total = 2;
}

// 获取电竞公会日统计信息
message GetEsportGuildDailyStatReq {
  uint32 date_time = 1;
  uint32 guild_id = 2;
}

message GetEsportGuildDailyStatResp {
  EsportGuildDailyStat info = 1;
}

//获取生成白鲸后台的access_token
message GetWhiteWhaleAccessTokenReq {
  uint32 uid = 1;
  uint32 guild_id = 2;
}
message GetWhiteWhaleAccessTokenResp {
  string access_token = 1;
  uint32 expire_ts = 2; // 过期时间戳
}

//校验白鲸后台的access_token
message CheckWhiteWhaleAccessTokenReq {
  string access_token = 1;
  uint32 uid = 2;
  uint32 guild_id = 3;
}

message CheckWhiteWhaleAccessTokenResp {
  bool is_valid = 1;
}

//公会代理人邀请状态
enum InviteStatusType {
  INVITE_STATUS_TYPE_UNSPECIFIED = 0;
  INVITE_STATUS_TYPE_PROCESSING = 1;     // 处理中
  INVITE_STATUS_TYPE_ACCEPT = 2;     // 接受
  INVITE_STATUS_TYPE_REJECT = 3;     // 拒绝
  INVITE_STATUS_TYPE_TIME_OUT = 4;     // 超时未处理
  INVITE_STATUS_TYPE_CANCEL_SIGN = 5;     // 解约
  INVITE_STATUS_TYPE_CANCEL = 6;     // 撤回
}

// 发送公会代理人邀请
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SendGuildAgentInviteReq {
  uint32 guildId = 1;   // 公会id
  uint32 invite_uid = 2;  //邀请uid
  uint32 invited_uid = 3;  //被邀请uid
  uint32 agent_type = 4; // see AgentType
  BusinessType b_type = 5;
  string alias = 6; //别名
}
message SendGuildAgentInviteResp {
}

// 撤回邀请
message CancelGuildAgentInviteReq {
  uint32 id = 1;  // 邀请id
}
message CancelGuildAgentInviteResp {
}

// 代理人角色变更
message ModifyGuildAgentReq {
  uint32 guild_id = 1;   // 公会id
  uint32 agent_uid = 2;  // 代理人uid
  uint32 agent_type = 3; // see AgentType
  uint32 business_type = 4;  // 业务类型 see BusinessType
  string agent_name = 5;  // 角色名称
}
message ModifyGuildAgentResp {
}


// 公会代理人邀请
message GuildAgentInvite {
  uint32 id = 1; // 邀请id
  uint32 uid = 2;
  uint32 ts = 3;  // 邀请时间
  uint32 agent_type = 4;  // 角色类型 see AgentType 拥有经纪人和管理员身份值为3
  uint32 invite_status = 5;  // 邀请状态 see InviteStatusType
  BusinessType b_type = 6;
  string alias = 7; //别名
}

// 获取公会代理人邀请列表
message GetGuildAgentInviteListReq {
  uint32 guild_id = 1; // 公会id
  repeated uint32 uid_list = 2;
  uint32 invite_status = 3;
  uint32 page = 4; // 从1开始
  uint32 page_size = 5;
  BusinessType b_type = 6;
}
message GetGuildAgentInviteListResp {
  repeated GuildAgentInvite invite_list = 1;
  uint32 next_page = 2;
  uint32 total_cnt = 3;
}

// 处理公会代理人邀请
message ProcGuildAgentInviteReq {
  uint32 uid = 1;
  uint32 id = 2;  // 邀请id
  bool is_accept = 3;  // 是否接受
}
message ProcGuildAgentInviteResp {
}


// 增加违规数据导出记录
message AddVioExportRecordReq {
  uint32 guild_id = 1;
  uint32 uid = 2;
  uint64 begin_ts = 3;
  uint64 end_ts = 4;
}
message AddVioExportRecordResp {
}

// 获取多人互房间动任职信息列表
message GetMultiChannelEmploymentInfoListReq {
  repeated uint32 guild_id_list = 1;
  repeated uint32 channel_id_list = 2;
  repeated uint32 uid_list = 3;
  uint32 offset = 4;
  uint32 limit = 5;
}
message MultiChannelEmploymentInfo {
  uint32 channel_id = 1;
  repeated uint32 admin_list = 4; // 厅管信息
  repeated uint32 employee_list = 6; // 任职成员列表
}
message GetMultiChannelEmploymentInfoListResp {
  repeated MultiChannelEmploymentInfo employment_info_list = 1;
  uint32 total = 2;
}

// 获取多人互房间动任职信息
message GetMultiChannelEmploymentInfoReq {
  uint32 channel_id = 1;
}
message GetMultiChannelEmploymentInfoResp {
  MultiChannelEmploymentInfo employment_info = 1;
}

// 设置房间任职成员
message SetMultiChannelEmploymentInfoReq {
  uint32 guild_id = 1;
  uint32 channel_id = 2;
  repeated uint32 employee_uid_list = 3; // 任职成员UID
  uint32 operator_uid = 4; // 操作者UID
}
message SetMultiChannelEmploymentInfoResp {

}

// 批量设置任职成员
message BatchSetMultiChannelEmploymentInfoReq {
  message Item {
    uint32 guild_id = 1;
    uint32 channel_id = 2;
    uint32 employee_uid = 3;
  }
  repeated Item employment_list = 1;
  uint32 operator_uid = 4; // 操作者UID
}
message BatchSetMultiChannelEmploymentInfoResp {

}

// 设置房间厅管
message SetMultiChannelAdminInfoReq {
  uint32 guild_id = 1;
  uint32 channel_admin_uid = 2; // 厅管UID
  repeated uint32 channel_id_list = 3; // 房间ID
  uint32 operator_uid = 4; // 操作者UID
}
message SetMultiChannelAdminInfoResp {

}

enum MultiChannelEmploymentType {
  MultiChannelEmploymentTypeInvalid = 0;
  MultiChannelEmploymentTypeAdmin = 1; // 厅管
  MultiChannelEmploymentTypeEmployee = 2; // 任职成员
}

enum MultiChannelEmploymentOpt {
  MultiChannelEmploymentOptInvalid = 0;
  MultiChannelEmploymentOptAdd = 1; // 添加
  MultiChannelEmploymentOptDel = 2; // 删除
}

// 批量设置厅管
message GetGuildAdminListReq {
  repeated uint32 guild_id = 1;
}
message GetGuildAdminListResp {
  repeated uint32 admin_list = 1;
}

message GetChannelListByAdminUidReq {
  uint32 admin_uid = 1;
}

message GetChannelListByAdminUidResp {
  repeated uint32 channel_list = 1;
}

message BatchGetDailyFaceCheckInfoReq {
  uint32 guild_id = 1; // 公会id
  uint32 identity = 2; // 查询身份类型 0:多人互动身份 1:语音直播身份 2:电竞陪玩
  uint32 begin_ts = 3; // 查询开始时间
  uint32 end_ts = 4; // 查询结束时间
  uint32 page = 5; // 页码
  uint32 page_size = 6; // 每页数量
  repeated uint32 uid_list = 7; // 主播uid列表（选填）
  repeated string ttid_list = 8; // 主播ttid列表（选填）
}

message BatchGetDailyFaceCheckInfoResp {
  repeated DailyFaceCheckInfo info_list = 1;
  uint32 total = 2;
}

// DailyFaceCheckInfo 主播人脸核验日明细数据
message DailyFaceCheckInfo {
  uint32 uid = 1; // 主播uid
  string date = 2; // 日期
  string account = 3; // 主播账号
  string nickname = 4; // 主播昵称
  string ttid = 5; // 主播ttid
  int64 sign_start_ts = 6; // 签约开始时间
  int64 sign_end_ts = 7; // 签约结束时间
  uint32 agent_uid = 8; // 经纪人uid
  string agent_nickname = 9; // 经纪人昵称
  string rule_type = 10; // 从业者标记
  uint32 channel_id = 11; // 房间id
  uint32 channel_display_id = 12; // 房间号
  uint32 guild_id = 13; // 公会id
  uint32 guild_short_id = 14; // 公会短id
  string room_gift_amt = 15; // 用户在房间收礼金额/元
  string guild_gift_amt = 16; // 公会收礼金额,属于同个公会的所有房间的收礼金额之和/元
  string less_commission_amt = 17; // 预计扣除佣金礼物金额/元
  string identity_type = 18; // 用户签约身份,听听/多人
}

// WeeklyFaceCheckInfo 主播人脸核验周明细数据
message WeeklyFaceCheckInfo {
  uint32 uid = 1; // 主播uid
  string date = 2; // 周日期（周第一天）
  string account = 3; // 主播账号
  string nickname = 4; // 主播昵称
  string ttid = 5; // 主播ttid
  int64 sign_start_ts = 6; // 签约开始时间
  int64 sign_end_ts = 7; // 签约结束时间
  uint32 agent_uid = 8; // 经纪人uid
  string agent_nickname = 9; // 经纪人昵称
  string rule_type = 10; // 从业者标记
  uint32 channel_id = 11; // 房间id
  uint32 channel_display_id = 12; // 经营房间ID
  uint32 guild_id = 13; // 公会id
  uint32 guild_short_id = 14; // 公会短id
  string room_gift_amt = 15; // 用户在房间收礼金额/元
  string guild_gift_amt = 16; // 公会收礼金额,属于同个公会的所有房间的收礼金额之和/元
  string less_commission_amt = 17; // 预计扣除佣金礼物金额/元
  string identity_type = 18; // 用户签约身份,听听/多人
}

// 获取公会主播人脸核验周明细列表
message BatchGetWeeklyFaceCheckInfoReq {
  uint32 guild_id = 1; // 公会id
  uint32 identity = 2; // 查询身份类型 see AnchorIdentityType
  uint32 begin_ts = 3; // 查询开始时间
  uint32 end_ts = 4; // 查询结束时间
  uint32 page = 5; // 页码
  uint32 page_size = 6; // 每页数量
  repeated string ttid_list = 7; // 主播ttid列表（选填）
  repeated uint32 uid_list = 8; // 主播uid列表
}

message BatchGetWeeklyFaceCheckInfoResp {
  repeated WeeklyFaceCheckInfo info_list = 1;
  uint32 total = 2;
}

// GuildWeeklySumFaceCheckInfo 主播人脸核验公会每周汇总明细数据
message GuildWeeklySumFaceCheckInfo {
  string date = 1; // 日期 yyyy/mm/dd至yyyy/mm/dd
  string rule_type = 2; // 从业者标记
  string action_user_cnt = 3; // 下发验证人数
  string face_user_cnt = 4; // 实际参与人数
  string not_himself_user_cnt = 5; // 综合非本人数
  string face_ratio = 6; // 验证参与率
  string himself_ratio = 7; // 本人率
  string face_himself_ratio = 8; // 参与验证本人率
  string not_himself_guild_gift_amt = 9; // 综合非本人公会流水
  string less_commission_amt = 10; // 预计扣除佣金（非最终扣款金额）
  string identity_type = 11; // 用户签约身份,听听/多人
}

// 获取公会主播人脸核验公会每周汇总
message BatchGetWeeklySumFaceCheckInfoReq {
  uint32 guild_id = 1; // 公会id
  uint32 identity = 2; // 查询身份类型 see AnchorIdentityType
  uint32 begin_ts = 3; // 查询开始时间
  uint32 end_ts = 4; // 查询结束时间
  uint32 page = 5; // 页码
  uint32 page_size = 6; // 每页数量
}

message BatchGetWeeklySumFaceCheckInfoResp {
  repeated GuildWeeklySumFaceCheckInfo info_list = 1;
  uint32 total = 2;
}

// GuildDailySumFaceCheckInfo 主播人脸核验公会每日汇总数据
message GuildDailySumFaceCheckInfo {
  string date = 1; // 日期
  string rule_type = 2; // 从业者标记
  string action_user_cnt = 3; // 下发验证人数
  string face_user_cnt = 4; // 实际参与人数
  string not_himself_user_cnt = 5; // 非本人数
  string face_ratio = 6; // 验证参与率
  string himself_ratio = 7; // 本人率
  string face_himself_ratio = 8; // 参与验证本人率
  string not_himself_guild_gift_amt = 9; // 综合非本人公会流水
  string less_commission_amt = 10; // 预计扣除佣金（非最终扣款金额）
  string identity_type = 11; // 用户签约身份,听听/多人
}

// 获取公会主播人脸核验公会每日汇总列表
message BatchGetDailySumFaceCheckInfoReq {
  uint32 guild_id = 1; // 公会id
  uint32 identity = 2; // 查询身份类型 see AnchorIdentityType
  uint32 begin_ts = 3; // 查询开始时间
  uint32 end_ts = 4; // 查询结束时间
  uint32 page = 5; // 页码
  uint32 page_size = 6; // 每页数量
}

message BatchGetDailySumFaceCheckInfoResp {
  repeated GuildDailySumFaceCheckInfo info_list = 1;
  uint32 total = 2;
}

// 导出主播人脸核验类型
enum FaceCheckExportType {
  FaceCheckTypeInvalid = 0;
  FaceCheckTypeDailyDetail = 1; // 日明细数据
  FaceCheckExportTypeWeeklyDetail = 2; // 周明细数据
  FaceCheckExportTypeWeeklySum = 3; // 周汇总数据
  FaceCheckExportTypeDailySum = 4; // 日汇总数据
}

// 根据类型导出公会主播人脸核验明细数据
message ExportFaceCheckInfoReq {
  uint32 uid = 1; // 操作人uid
  uint32 guild_id = 2; // 公会id
  uint32 begin_ts = 3; // 查询开始时间
  uint32 end_ts = 4; // 查询结束时间
  repeated uint32 uid_list = 5; // 主播uid列表（选填）
  FaceCheckExportType export_type = 6; // 导出类型
  uint32 identity = 7; // 查询身份类型
  repeated string ttid_list = 8; // 主播ttid列表（选填）
}
// 导出主播人脸核验数据响应
message ExportFaceCheckInfoResp {
  string download_url = 1; // 下载链接
}

// 预创建上传文件
message PreCreateUploadFileReq {
  uint32 uid = 1; // uid
  string file_name = 2; // 文件名
  uint64 file_size = 3; // 文件大小
  string file_type = 4; // 文件类型
  string file_md5 = 5; // 文件md5
  string file_ext = 6; // 文件扩展名
  string scene = 7; // 场景
  string scope = 8; // 桶
}
message PreCreateUploadFileResp {
  string file_id = 2; // 文件id
}

// 完成上传文件
message CompleteUploadFileReq {
  string file_id = 1; // 文件id
}
message CompleteUploadFileResp {
}

// 获取上传文件URL
message BatchGetUploadFileUrlReq {
  repeated string file_key_list = 1; // 文件id列表
  uint32 expiration_sec = 2; // 过期时间 (默认10分钟)
}
message BatchGetUploadFileUrlResp {
  map<string, string> file_url_map = 1; // 文件id对应的下载链接
}

// 平台资讯banner
message BannerInfo {
  string img_url = 1; 
  string jump_url = 2;  //跳转url
}

//获取公会平台资讯banner
message GetBannerInfoReq{
}
message GetBannerInfoResp{
  repeated BannerInfo banner_list = 1;  //平台资讯banner
}


//触发任务
message TriggerTaskReq {
   //任务类型
   enum TaskType
   {
      Task_Type_Invalid = 0;  // 无效
      Task_Type_MoveMultiEmploye = 1;  // 迁移厅管数据
   }

   TaskType task_type = 1; //任务类型
}
message TriggerTaskResp {
}

