syntax = "proto3";

package comm_push_user_cache;

option go_package = "golang.52tt.com/protocol/services/comm-push-user-cache";

service CommPushUserCache {
  //根据 uid 批量拉取用户基本信息的接口
  rpc GetUserByUids(GetUserInfoByUidsReq) returns (GetUserInfoByUidsResp) {}

  //根据 uid 批量拉取用户在线状态的接口
  rpc GetUserOnlineStateByUids(GetUserOnlineStateByUidsReq) returns (GetUserOnlineStateByUidsResp){}

  //根据 uid 批量拉取用户游戏卡列表的接口
  rpc GetUserTagsByUids(GetUserTagsByUidsReq) returns (GetUserTagsByUidsResp){}

  //注意：此接口在服务第一次上线时执行一次，后续不执行调用
  //提前将服务上线时登录时间30天内的用户信息缓存
  rpc SetBatchUserInfoPre(SetBatchUserInfoPreReq) returns (SetBatchUserInfoPreResp){}

}

//拉取用户基本信息批量uid
message GetUserInfoByUidsReq {
  repeated uint32 uids = 1;
}

//返回用户完整信息repeated uint32
message UserResp {
  uint32 uid = 1;
  string phone = 2;  //如果库中手机号是xxx_disable, 则该字段返回空
  string username = 3;
  string alias = 4;
  string nickname = 5;
  int32 sex = 6;
  string signature = 7; //不支持！！！

  string password = 9; //不支持！！！
  bool verify = 10; //不支持！！！
  uint32 current_guild_id = 11;
  uint32 last_quit_guild_type = 12;
  uint32 registered_at = 13;
  bytes kick_client_device_id = 14;
  uint32 question = 15;
  uint32 last_quit_guild_id = 16;
  uint32 last_quit_guild_time = 17;
  uint32 who_invite_uid = 18;
  string invite_code = 19; //不支持！！！
  uint32 status = 20;  //已废弃
  uint32 user_type = 21;
  string fromid = 22;  //不支持！！！
  uint32 source = 23;  // USER_SOURCE
  uint32 last_login_at = 24;
  bool password_set = 25;
  uint32 prefix_valid = 26;       //是否使用公会马甲
  string original_nickname = 27;  //无马甲昵称 (不支持！！！)
  string guild_prefix = 28;       // 公会马甲 (//不支持！！！)
  bool is_unregister = 29;        //是否已注销
  bool is_clear_local_account = 30;    // 退出后清除账号和密码信息
  bool is_noble = 31; //是否贵族
}

//返回用户基本信息
message GetUserInfoByUidsResp {
  map<uint32,UserResp> user_info_map = 1;
}

//拉取用户在线状态批量uid
message GetUserOnlineStateByUidsReq {
  repeated uint32 uids = 1;
}

message OnlineUpdate
{
  uint32 uid 	= 1;
  bool is_online = 2;
  uint32 os_type = 3; //see protodef.h EOSType 1-android,2-IOS
  bool is_logout = 4; //for ios logout, ios logout and offline is diffence.
  uint32 ts 	= 5;
}

//用户在线状态
message GetUserOnlineStateByUidsResp {
  map<uint32,OnlineUpdate> online_update_map = 1;
}

//拉取用户游戏卡列表批量uid
message GetUserTagsByUidsReq {
  repeated uint32 uids = 1;
}

// 用户标签
message UserTagBase{
  uint32 tag_type = 1; // ga::UserTagType 1年龄段标签 2找什么人标签 4游戏标签 5个性标签 6生日日期标签
  string tag_name = 2;
  uint32 tag_id   = 3;
  bytes  tag_info = 4; // 扩展信息 结构体Ext字段，根据tag_type不同有不同类型 比如 tag_type=2时为 UserFindWhoTagExt / tag_type=4时为 UserGameTagExt
  bool is_del = 5;
}

message UserTagBaseList {
  repeated UserTagBase user_tag_list= 1;
}

//用户游戏卡列表
message GetUserTagsByUidsResp {
  map<uint32,UserTagBaseList> user_tag_list_map = 1;
  map<uint32,uint32> tag_tab_map = 2;
}

message SetBatchUserInfoPreReq {

}

message SetBatchUserInfoPreResp {

}
