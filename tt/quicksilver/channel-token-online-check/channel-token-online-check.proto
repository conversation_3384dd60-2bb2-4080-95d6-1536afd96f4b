syntax = "proto3";

package channel_token_online_check;
option go_package = "golang.52tt.com/protocol/services/channel-token-online-check";

service ChannelTokenOnlineCheck {
    rpc GetUserChannelOnlineInfo(GetUserChannelOnlineReq) returns( GetUserChannelOnlineResp ){
	}
}

message GetUserChannelOnlineReq{
	uint32 uid  =  1;
	uint32 cid = 2;
	uint32 mic_id = 3;			      //micid非0时，会去查是不是在该麦上
}
message GetUserChannelOnlineResp{
	uint32 cid = 1 ;
	uint32 mic_id = 2;
}
