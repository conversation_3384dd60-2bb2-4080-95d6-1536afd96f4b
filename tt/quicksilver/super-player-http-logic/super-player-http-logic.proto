syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/super-player-http-logic";
package super_player_http_logic;

//补单
message ReplacementOrderReq{
  string old_order_id = 1; //原旧订单
  int32  create_time  = 2; //原旧单下单时间
}

message ReplacementOrderResp {
  string order_id = 1;
}

//开通会员
message OpenSuperPlayerReq { // 该接口需要加后台白名单才能使用，白名单帐号不进行订单核销
  uint32 super_player_uid = 1;//购买的用户UID
  int64 months = 2; //购买多少个月（30天）
  int64 value = 3; //增加的会员值
  int64 server_time = 4; //同一个单，不能变
  string order_id = 5; //订单号
  string package_id = 6;//套餐ID
  int64 mins = 7; //开通时长，跟months和package_id三选一
}
message OpenSuperPlayerResp{
}

//套餐
message Package {
  string id = 1; //套餐ID
  string product_id = 2; //苹果商店商品ID
  string name = 3; //套餐名
  string desc = 4; //描述
  string label = 5; //标签
  float  original_price = 6; //原价RMB
  float  price = 7;//现价RMB
  int32  months = 8; //几个月
  int32  value  = 9; //第一个周期增加的成长值；如果是包X的可能第一个周期和后续的周期值不一样，需要从配置中获取
  bool   auto = 10; //是否自动续费
  repeated string pay_channel_list = 11; // 支持的支付渠道列表
  float  discount_price = 12;//优惠价格RMB
  float  daily_price = 13; // 每日单价，该单价按每月30天计算，一位小数，向上取整
  bool is_show_coupon = 14; // 是否展示优惠券
}

message UpgradePackage {
  string id = 1; // 套餐ID
  string product_id = 2; // 苹果商店商品ID
  string name = 3; // 套餐名
  string desc = 4; // 描述
  float  price = 5; // 价格RMB
  int32  days = 6; // 多少天
  int32  value  = 7; // 追加的成长值
  repeated string pay_channel_list = 8; // 支持的支付渠道列表
  float  discount_price = 9;// 优惠价格RMB
  float  daily_price = 10; // 每日单价
  string label = 11; //标签
  bool is_show_coupon = 12; // 是否展示优惠券
}

//获取套餐配置
message GetPackageListReq {
  string uid = 1;
  uint32 super_player_status = 2; // see SuperPlayerStatus （该字段废弃）
  string device_id = 3; //设备号
}

message GetPackageListResp {
  repeated Package package_list = 1;//套餐配置列表
}

//获取SVIP套餐配置
message GetSvipPackageListReq {
  string uid = 1;
}
message GetSvipPackageListResp {
  repeated UpgradePackage upgrade_package_list = 1;//升级套餐配置列表
  repeated Package package_list = 2;//套餐配置列表
}

//下单接口
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message PlaceOrderReq {
  uint32 super_player_uid = 1;//购买的用户UID
  string package_id = 2;//下单的套餐ID
  string pay_channel = 3;//下单的渠道

  string order_type = 4; //订单类型 默认:BUY 类型枚举：BUY(购买) PERIOD(自动续费) UPGRADE(升级，注意升级仅SVIP套餐可用)
  string os_type = 5; //系统类型 (i IOS系统 a android系统)
  string version = 6; //客户端版本号
  string remark = 7; //备注
  string bundle_id = 8; //IOS的produceID(appstore支付必传)
  string code = 9 ; //端外支付code
  string ttid = 10; //端外支付TTID
  string token = 11; //端外支付校验

  uint32 client_version = 12;
  string device_id = 13; //设备号
  string request_id = 14;
  uint32 client_type = 15;
  string FaceAuthToken = 16;
  string face_auth_provider_code = 17;
  string face_auth_provider_result_data = 18;
  string face_auth_result_token = 19;

  SuperPlayerType super_player_type = 20; //会员类型，改字段默认不传时为普通超级会员
  repeated string original_transaction_ids = 21; // 苹果帐号使用，新版本用于AB帐号问题消除，不传走旧流程
}

message PlaceOrderResp{
  string order_id = 1;
  string token = 2;
  string cli_order_no = 3; //第三方订单号
  string cli_order_title = 4;
  string tsk = 5; //加密字符串
  string channel_map = 6; //唤起支付渠道的参数, 用于安卓、IOS、前端使用
  string request_id = 7; //
  uint32 auth_scene = 8;
  string face_auth_context_json = 9;
  bool is_first_open = 10;
}

message CancelOrderReq {
  uint32 super_player_uid = 1;//购买的用户UID
  string order_id = 2;
}

message CancelOrderResp{}


//自动扣款下单接口
message PlaceAutoPayOrderReq {
  uint32 super_player_uid = 1;//购买的用户UID
  string produce_id = 2; //APPSTORE 商品ID
  string contract_id = 3; //签约ID
  bool has_discount = 4; //是否有折扣
}

message PlaceAutoPayOrderResp{
}


//支付回调
message PayCallBackReq {
  string order_no = 1; //支付平台订单
  string pay_channel = 2;
  string total_fee = 3;
  string order_status = 4;
  string cli_order_no = 5; //会员服务订单
  string other_order_no = 6; //支付渠道订单
  string other_status = 7 ;//第三方订单状态
  string notify_time = 8; //回调时间
  string uid = 9;//需要填收货人UID
  string buyer_id = 10; //绑定AppleID签约的UID
  string begin_time = 11; //当前支付会员开始时间，格式 2021-08-16 17:22:30（背景时区）
  string end_time   = 12; //当前支付会员结束时间，格式 2021-08-16 17:22:30（背景时区）
}

message PayCallBackResp {
}


//签约回调
message NotifyContractReq {
  string buyer_id = 1; //绑定AppleID签约的UID
  string type = 2;
  string client_id = 3;
  string business_id = 4;
  string plan_id = 5;
  string id = 6; //会员系统订单
  string contract_id = 7; //
  string contract_channel = 8;//
  string order_no = 9;
  string product_id = 10 ; //APPStore 商品ID
  string next_pay_time = 11; //下次扣款时间，只允许APPSTROE使用
  string real_buyer_id = 12; //操作扣款的用户UID
}

message NotifyContractResp{
}

message GetSuperPlayerInfoReq {
  uint32 super_player_uid = 1; //购买的用户UID
  uint32 market_id = 2; // 应用ID
}


message SuperPlayerContract {
  string pay_channel = 1; //支付渠道
  string status = 2;
  string package_id = 3;
  string extra_tag = 4; //discount优惠
  uint32 next_pay_timestamp = 5; //下次扣款时间
  uint32 market_id = 6;
  string contract_id = 7; //签约ID
  uint32 sign_time   = 8; //签约时间
  float price        = 9 ;//扣款金额
  SuperPlayerType super_player_type = 10; //会员类型
}

message GetSuperPlayerInfoResp{
  uint32 super_player_uid = 1; //uid
  int64 super_player_value = 2; //会员值
  int64 super_player_level = 3; //会员等级

  int64 begin_timestamp = 4; //当前会员周期开始时间戳，没开通过是0
  int64 expire_timestamp = 5; //当前会员周期结束时间戳，没开通过是0；也是一个版本号，push和get拿到不同结果时，取版本号大的。
  int64 expire_notify_hour = 6; //差多少个小时过期，算即将过期 废弃

  string account = 7; //
  string nickname = 8;

  repeated SuperPlayerContract super_player_contract_list = 9;

  bool bind_phone = 10;

  uint32 year_member_expire_ts = 11; //年费会员过期时间
  bool is_show_manager_contract_btn = 12; //是否显示签约管理入口
  bool has_expired = 13; // 当前帐号是否已过期，若该字段为true，显示剩余时间为0，做自动续费显示兼容
  int64 svip_begin_timestamp = 14; //svip开始时间戳
  int64 svip_expire_timestamp = 15; //svip结束时间戳
  int64 svip_year_member_expire_timestamp = 16; //svip年费会员结束时间戳
  SuperPlayerStatus status = 17; //会员状态
  SuperPlayerStatus svip_status = 18; // svip状态
  bool is_svip_first = 19; //是否优先展示svip
  uint32 super_player_left_days = 20; // 会员剩余天数
  uint32 svip_left_days = 21; // svip剩余天数
}


message CancelContractReq
{
  uint32 uid = 1;
  string contract_id = 2;
}

message CancelContractResp{}

//增加会员值记录
message SuperPlayerValueRecord {
  uint32 super_player_uid = 1;
  int64 time_stamp = 2;//增加时间
  string reason = 3; //理由
  int64  incr_value = 4; //增加数量
}

//设置红点时间
message SetLastSuperPlayerValueChangeTimeReq {
  uint32 super_player_uid = 1;
  int64 prev_timestamp = 2; //上次打开页面最新的红点时间。
}

message SetLastSuperPlayerValueChangeTimeResp {
}

//充值记录
message PayRecord {
  int64 time_stamp = 1; //充值时间
  string desc = 2; //充值描述
  float price = 3;
}

message GetSuperPlayerPayRecordReq {
  uint32 super_player_uid = 1;
  int64 off = 2;
  int64 count = 3;
}

message GetSuperPlayerPayRecordResp {
  repeated PayRecord pay_record_list = 1;
}

message GetSuperPlayerValueRecordReq {
  uint32 super_player_uid = 1;//超级玩家UID
  int64 off = 2;
  int64 count = 3;
}
message GetSuperPlayerValueRecordResp {
  repeated SuperPlayerValueRecord record_list = 1; //成长值明细
  int64 today_incr_value = 2; //今天增加总值
  int64 prev_timestamp = 3; //上次打开页面最新的红点时间。
}

// 会员任务，获取用户所有会员任务
message GetSuperPlayerMissionReq{
  uint32 super_player_uid = 1;
}
message GetSuperPlayerMissionResp{
  repeated MissionDetail mission = 1;
}

message AddSuperPlayerValueReq {
  uint32 super_player_uid = 1;
  int64 super_player_value = 2;
  string order_id = 3;
  string reason = 4;
}
message AddSuperPlayerValueResp {
}

//订单状态
enum EnumOrderStatus {
  ORDER_INIT = 0; //下单
  ORDER_PAY_SUCCESS = 1; //支付成功
  ORDER_PAY_FAILED = 2; //支付失败
}

message GetOrderStatusReq{
  string order_id = 1;
}
message GetOrderStatusResp{
  EnumOrderStatus status = 1;
}

message MissionDetail{
  uint32 id = 1;     // 任务id
  string name = 2;  // 任务名称
  uint32 mission_type = 3;   // 任务类型 MissionType
  uint32 award_value = 4;  // 任务奖励成长值
  uint32 required_count = 5;   // 任务需求计数
  uint32 finish_count = 6;    // 任务已完成计数
  uint32 mission_status = 7;  // 任务目前状态 MissionStatus
  string mission_icon = 8; // 任务图标
}

enum MissionType{
  Unknown = 0;
  Daily = 1;
  TimeLimit = 2;
  Activity = 3;
}

enum MissionStatus{
  Incomplete = 0;   // 未完成
  Completed = 1;    // 已完成，尚未发奖
  Finished = 2;     // 已完成，发奖完毕
}

//装扮类型
enum DressType {
  DRESS_TYPE_UNSPECIFIC = 0;
  DRESS_TYPE_ROOM_SUIT = 1;  //房间套装
  DRESS_TYPE_SPECIAL_CONCERN = 2;  //特别关心
  DRESS_TYPE_CHAT_BACKGROUND = 3;  //聊天背景
  DRESS_TYPE_CHAT_BUBBLE = 4;  //聊天气泡
}

//装扮资源配置
message DressResInfo {
  string list_url = 1;  //封面图
  string preview_url = 2;  // 预览图，png格式，新版兜底展示
  string audio_url = 3;  // 音频资源
  string video_url = 4;  // 预览动图资源
  string top_url = 5;  // 预览图top图
  string web_static = 6; //web 静态控件图
  string com_desc = 7;  // 组件文案
  string preview_mp4 = 8; // 预览图 mp4压缩版
}

//会员装扮配置
message DressInfo {
  uint32 id = 1;    // 装扮id
  uint32 level = 2;  // 装扮最小使用等级
  bool   is_use = 3; //是否正在使用
  string name = 4;   // 装扮名称
  string desc = 5;   // 装扮描述
  DressResInfo res_info = 6;  // 资源配置
  bool is_experience = 7; // 是否为可体验装扮
  int64 experience_end_time = 8; // 剩余可装扮时间（当为体验装扮时返回，其余时间返回0）
}

// 获取用户会员装扮
message GetUserDressListReq {
  string uid = 1;
  DressType type = 2;
  string to_account = 3; //IM 1v1背景 需要传to_account
}

message GetUserDressListResp {
  repeated DressInfo info_list = 1;
}

// 设置使用装扮
message SetDressInUseReq {
  string uid = 1;
  DressType dress_type = 2; //装扮类型 see DressType
  uint32 dress_id = 3;       //装扮ID 取消使用为0
}

message SetDressInUseResp {
}


// 设置IM聊天背景特殊装扮
message SetChatSpecialDressInUseReq {
  string uid = 1;               //本人UID
  uint32 dress_id = 2;         //装扮ID 取消使用为0
  string to_account = 3;       //需要设置的对方账号
  string replace_account = 4;  //超过50人  需要替换的对方账号
}

message SetChatSpecialDressInUseResp {
}

// 获取用户会员装扮历史记录
message GetUserDressHistoryReq {
  string uid = 1;
  DressType type = 2;
  uint32 offset = 3;  //分页偏移 开始是0
  uint32 limit = 4;   //单页大小 建议单次20
}

// 装扮历史记录
message UserDressHistory {
  uint32 id = 1;
  uint32 create_time = 2;
  uint32 removed = 3;  // 0 正常 1已经下架
  string name = 4;
  string web_list = 5;
  uint32 level = 6;
}

// 获取用户会员装扮历史记录
message GetUserDressHistoryResp {
  string uid = 1;
  DressType type = 2;
  uint32 offset = 3;
  uint32 limit = 4;
  repeated UserDressHistory history_list = 5;  //装扮历史记录   history_list < limit 结束
}

// 会员身份提示弹窗
message SuperPlayerIdentityPopupReq {
  enum ReqType {
    REQ_QUERY = 0;   // 查询弹窗信息
    REQ_FINISHED = 1;  // 设置已完成弹窗提示
    REQ_VALUE_CHANGE_TIME = 2; //设置成长值变化更新时间
    REQ_WEB_CANCEL_CONTRACT = 3; //会员页取消订阅提示
  }
  uint32 req_type = 1;    // see  ReqType
  string uid = 2;
}
message SuperPlayerIdentityPopupResp {
  bool is_need_push = 1;    // 是否需要弹窗
  uint32 before_lv = 2;    // 升级前等级
  uint32 after_lv = 3;     // 升级后等级
  string pop_up_msg = 4; //弹框信息
}

enum PlatFormType
{
  ENUM_PLATFORM_ALL = 0;  // 所有平台
  ENUM_PLATFORM_ANDROID = 1;   // android
  ENUM_PLATFORM_IOS = 2;  // ios
}

enum SuperPlayerStatus
{
  ENUM_STATUS_NO_OPEN = 0;  // 未开通
  ENUM_STATUS_OPENING = 1;  // 生效中
  ENUM_STATUS_SOON_EXPIRE = 2;  // 即将过期
  ENUM_STATUS_EXPIRED = 3;  // 已过期
  ENUM_STATUS_SIGN = 4; //连续订阅签约中
  ENUM_STATUS_PENDING = 5; // 待生效（该状态仅普通会员存在）
}

enum SuperPlayerType
{
  ENUM_TYPE_NORMAL = 0;  // 普通会员
  ENUM_TYPE_SVIP = 1;  // SVIP会员
}

message BannerAdvConf {
  string banner_img = 1; // 图标
  string jump_url = 2;   // 跳转链接
}

// 获取会员banner广告位
message GetBannerAdvConfListReq {
  string uid = 1;
  uint32 platform_type = 2;  // see PlatFormType
  uint32 super_player_status = 3; // see SuperPlayerStatus 废弃
}
message GetBannerAdvConfListResp {
  repeated BannerAdvConf adv_list = 1;
}

// 会员订单端外退款后回滚
message RevokeOrderReq {
  string order_id = 1; // 订单ID
  string notify_time = 2; // 通知时间
  string uid = 3; // 用户UID
}
message RevokeOrderResp {
}

//**微信端外支付相关**//

//获取TT用户信息
message GetTTUserInfoReq{
  string ttid = 1;
  string token = 2;
}

message GetTTUserInfoResp {
  string ttid = 1;
  uint32 uid = 2;
  uint32 age = 3;
  string nickname = 4;
  string alias = 5;
}

//获取套餐列表
message GetSuperPlayerPackageListReq{
  string pay_channel = 1;
  string ttid = 2;
  string token = 3;
}

message GetSuperPlayerPackageListResp{
  repeated Package package_list = 1;//套餐配置列表
}

//网页授权接口
message SuperPlayerPayAuthReq {
  uint32 uid = 1;
}

message SuperPlayerPayAuthResp{
}

//会员优惠券弹窗场景
enum CouponPopUpScene {
  ENUM_RENEWAL_POPUP_SCENE_UNSPECIFIED = 0;
  ENUM_RENEWAL_POPUP_SCENE_PERSONAL_PAGE = 1; // 个人主页
  ENUM_RENEWAL_POPUP_SCENE_ENTRY_BANNER = 2; // 拉起在线状态设置
  ENUM_RENEWAL_POPUP_SCENE_SUPER_PLAY_PAGE = 3; // 会员主页
  ENUM_RENEWAL_POPUP_SCENE_SUPER_PLAY_PAGE_LEAVE = 4; //离开会员主页
  ENUM_RENEWAL_POPUP_SCENE_WHO_MIND_ME = 7; // 谁看过我
}

// 检查是否展示优惠券弹窗
message CheckCouponPopUpReq {
  CouponPopUpScene scene = 1; // 场景
  uint32 os_type = 2;   // 终端类型 1 Android 2 IOS
}
message CheckCouponPopUpResp {
  bool is_red_packge_show = 1; //是否展示红包弹窗
  bool is_retain_show = 2; //是否展示挽留弹窗
  string resource_url = 3; //资源地址
  string resource_md5 = 4; //资源md5
}

// 获取优惠券挽留弹窗配置
message GetCouponRetainPopUpConfReq {
  CouponPopUpScene scene = 1; // 场景
  uint32 os_type = 2;   // 终端类型 1 Android 2 IOS
}
message GetCouponRetainPopUpConfResp {
  string reminder = 1; // 挽留弹窗文案
  string sale_id = 2; // 会员套餐id
  uint32 package_type = 3; // 会员套餐类型
  uint32 show_time = 4; // 弹窗展示时间
  CouponPopUpScene scene = 5; // 场景
}

message CouponPopUpAndPayReq {
  CouponPopUpScene scene = 1; // 场景
  bool is_pay = 2; // 是否支付  0 不支付 1 支付
}

message CouponPopUpAndPayResp {
}