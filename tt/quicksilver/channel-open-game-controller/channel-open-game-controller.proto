syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-open-game-controller";
package channel_open_game_controller;

service ChannelOpenGameController {
    // ===== channel-open-game =====
    // 游戏用户信息限制信息同步
    rpc SetChannelGame (SetChannelGameReq) returns (SetChannelGameResp) {
    }

    // ===== channel-open-game-controller-logic =====
    // 游戏模式设置
    rpc SetChannelGameModeInfo (SetChannelGameModeInfoReq) returns (SetChannelGameModeInfoResp) {
    }

    rpc GetOpenid (GetOpenidReq) returns (GetOpenidResp) {
    }

    rpc JoinChannelGame (JoinChannelGameReq) returns (JoinChannelGameResp) {
    }

    rpc QuitChannelGame (QuitChannelGameReq) returns (QuitChannelGameResp) {
    }

    rpc ReadyChannelGame (ReadyChannelGameReq) returns (ReadyChannelGameResp) {
    }

    rpc UnReadyChannelGame (UnReadyChannelGameReq) returns (UnReadyChannelGameResp) {
    }

    rpc GetChannelGameStatusInfo (GetChannelGameStatusInfoReq) returns (GetChannelGameStatusInfoResp) {
    }

    rpc BatchGetChannelGameStatusInfo (BatchGetChannelGameStatusInfoReq) returns (BatchGetChannelGameStatusInfoResp) {
    }
    // 设置房间内用户加载游戏状态

    rpc SetLoading (SetLoadingReq) returns (SetLoadingResp) {
    }

    // 有用户退出房间
    rpc ExitChannelGame (ExitChannelGameReq) returns (ExitChannelGameResp) {
    }

    // 开始游戏
    rpc StartChannelGame (StartChannelGameReq) returns (StartChannelGameResp) {
    }

    // 用户被T
    rpc KickOutChannelGame (KickOutChannelGameReq) returns (KickOutChannelGameResp) {
    }

    //获取游戏开始时的主持人
    rpc GetChannelGameHost (GetChannelGameHostReq) returns (GetChannelGameHostResp) {
    }

    rpc CheckInterruptOpt (CheckInterruptOptReq) returns (CheckInterruptOptResp) {
    }
}

// 游戏模式信息
message ChannelGameModeInfo {
    string mode_key = 1; //模式key
    string game_param = 2; //游戏参数
    repeated uint32 player_limit_list = 3; //限制人数
}

// 设置房间游戏
message SetChannelGameReq {
    uint32 game_id = 1;
    string game_version = 2;
    string game_name = 3; // datacenter需要获取返回
    uint32 game_member_cnt_limit = 4;
    uint32 cp_id = 5;
    uint32 channel_id = 7;
    string game_mode_info = 8; // 游戏模式信息
    string mode = 9; // 游戏模式
    int64 load_seq = 10; // 房间变更序号，用于同步房间状态和游戏操作
}

message SetChannelGameResp {
}

// 修改游戏附加信息
message SetChannelGameModeInfoReq {
    uint32 channel_id = 1;
    uint32 uid = 2;
    int64 load_seq = 3;
    ChannelGameModeInfo game_mode = 4; // 游戏模式
    bool skip_permission_check = 5; //跳过检查load_seq和权限，用于小游戏服务端直接调过来的请求
}
message SetChannelGameModeInfoResp {
}

// 游戏加载完，登录获取openid
message GetOpenidReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    int64 load_seq = 3;
}
message GetOpenidResp {
    string open_id = 1;
    string code = 2;
}

// 加入游戏
message JoinChannelGameReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    int64 load_seq = 3;
}
message JoinChannelGameResp {
}

// 取消加入
message QuitChannelGameReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    int64 load_seq = 3;
}
message QuitChannelGameResp {
}

// 准备游戏
message ReadyChannelGameReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    int64 load_seq = 3;
}
message ReadyChannelGameResp {
}

// 取消准备
message UnReadyChannelGameReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    int64 load_seq = 3;
}
message UnReadyChannelGameResp {
}

// 游戏用户信息
message ChannelGamePlayerInfo {
    uint32 uid = 1;
    uint32 status = 2; // 0：join，1：ready
    string username = 3;
    string nickname = 4;
    uint32 sex = 5;
    string openid = 6;
    uint32 seq = 7;
    bool is_loading_completed = 8;
    bytes user_profile = 9; // 游戏开始时，玩家的神秘人数据结构-> app.ga_base.UserProfile
}
// 获取游戏状态数据
message GetChannelGameStatusInfoReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    int64 load_seq = 3;
}

message GetChannelGameStatusInfoResp {
    uint32 game_master = 1;
    uint32 game_status = 2;
    ChannelGameModeInfo game_mode = 3;
    repeated ChannelGamePlayerInfo game_players = 4; // 玩家列表
    string desc = 5;
    repeated ChannelGamePlayerInfo other_players = 6; // 其他玩家列表
    string set_id = 7;
}

message ChannelGameStatusInfo {
    uint32 game_master = 1;
    uint32 game_status = 2; // 0:对局准备中，1:对局进行中
    ChannelGameModeInfo game_mode = 3;
    repeated ChannelGamePlayerInfo game_players = 4; // 玩家列表
    string desc = 5;
    repeated ChannelGamePlayerInfo other_players = 6; // 其他玩家列表
    uint64 start_time = 7; // 对局开始unix时间戳
    uint32 game_id = 8;
}

message BatchGetChannelGameStatusInfoReq {
    repeated uint32 channel_id_list = 1;
}

message BatchGetChannelGameStatusInfoResp {
    map<uint32, ChannelGameStatusInfo> channel_game_status_info_map = 1; // {channel_id, ChannelGameStatusInfo}
}

// 退出游戏
message ExitChannelGameReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    int64 load_seq = 3;
    uint32 reason = 4;        //0:主动退出  1:被踢
}
message ExitChannelGameResp {
}

// 开始游戏
message StartChannelGameReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    int64 load_seq = 3;
}
message StartChannelGameResp {
}

// 设置房间内用户加载游戏状态
message SetLoadingReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    int64 load_seq = 3;
    bool is_loading_completed = 4;
}
message SetLoadingResp {
}

// 用户被T
message KickOutChannelGameReq {
    uint32 channel_id = 1;
    uint32 uid = 2;
    int64 load_seq = 3;

    string reason = 4;
}
message KickOutChannelGameResp {
    bool exist = 1;
}

message GetChannelGameHostReq {
    uint32 channel_id = 1;
}

message GetChannelGameHostResp {
    uint32 channel_id = 1;
    uint32 uid = 2;
}

// InterruptOpt
// 游戏对局开始以后，用户操作踢出房间或者切换游戏时检查，向小游戏服务器查询操作是否允许
// 需求：【小游戏游戏生态治理】https://q9jvw0u5f5.feishu.cn/wiki/FcLDwPJrwiezRkkw5GyclhsSnGf
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CheckInterruptOptReq {
    uint32 uid = 1;
    uint32 roomId = 2;
    uint32 schemeId = 3;
    enum OptType {
        Unknown = 0;
        KickUser = 1; //踢用户出房间
        SwitchGame = 2; //切换游戏
    }
    OptType optType = 4; //用户操作类型
    uint32 gameId = 5; //游戏id
}

message CheckInterruptOptResp {
    int32 code = 1;
    string err_msg = 2;
}