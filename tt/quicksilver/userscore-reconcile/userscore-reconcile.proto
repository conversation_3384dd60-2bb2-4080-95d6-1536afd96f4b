syntax = "proto3";

package userscore_reconcile;
option go_package = "golang.52tt.com/protocol/services/userscore-reconcile";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

import "tt/quicksilver/extension/options/options.proto";

service UserScoreReconcile {
  option (service.options.old_package_name) = "UserScoreReconcile.UserScoreReconcile";

  //获取时间范围内的积分数量和金额
  rpc GetUserScoreCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

  //获取时间范围内的订单列表
  rpc GetUserScoreOrderList(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  //补单
  rpc ReplaceOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}
}
