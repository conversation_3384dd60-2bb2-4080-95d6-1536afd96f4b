syntax = "proto3";
package comm_push_op_schedule;

import "tt/quicksilver/push_notification/v3/push-ops.proto";
option go_package = "golang.52tt.com/protocol/services/comm-push-op-schedule";

service CommPushOpSchedule {
    //运营推送创建任务
    rpc OpOfflineSendPush(push_notification.v3.SendPushReq) returns (push_notification.v3.SendPushResp) {}

    //运营推送指定推送目标
    rpc OpOfflineSendTargetList(push_notification.v3.BatchTargetUidListReq) returns (push_notification.v3.BatchTargetUidListResp) {}

    //运营推送聚合接口（由运营后台产生taskId和指定每批的推送内容）
    rpc OpOfflineAggreSendPush(OpOfflineAggreSendPushReq) returns (OpOfflineAggreSendPushResp) {}

    rpc GetOpPushTaskFromSetListLen(GetOpPushTaskFromSetListLenReq) returns (GetOpPushTaskFromSetListLenResp) {}

    //运营付费推送接口（由运营后台产生taskId和指定每批的推送内容）
    rpc PaidOpOfflineSendPush(PaidOpOfflineSendPushReq) returns (PaidOpOfflineSendPushResp) {}

    rpc GetPaidOpOfflineSendPushCnt(GetPaidOpOfflineSendPushCntReq) returns(GetPaidOpOfflineSendPushCntResp){}
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message OpOfflineAggreSendPushReq {
    string taskId = 1;  //由运营后台生成并保证唯一
    push_notification.v3.SendPushReq.BaseInfo baseInfo = 2; //跟之前老的调用填充方式一致
    repeated string uidList = 3; //最多5k个
}

message OpOfflineAggreSendPushResp {
    uint32 result = 1; //0为正常，其余为异常
}

message GetOpPushTaskFromSetListLenReq {

}

message GetOpPushTaskFromSetListLenResp {
    uint32 length = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message PaidOpOfflineSendPushReq {
    string taskId = 1;  //由运营后台生成并保证唯一
    // 付费推送接口由 BaseInfo 里面的 extra 字段提供
                                                                                                                    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    //op_paid_api/vv_paid_api/xm_paid_api/hw_paid_api/ho_paid_api 以此类推 填充 push-paid-api.proto 中对应 message 的 protobuf 序列化内容
    push_notification.v3.SendPushReq.BaseInfo baseInfo = 2; //跟之前老的调用填充方式一致
    repeated string uidList = 3; //最多5k个
}

message PaidOpOfflineSendPushResp {
    uint32 result = 1; //0为正常，其余为异常
}

message GetPaidOpOfflineSendPushCntReq {

}

message PushCntInfo {
    string platform = 1;  // 比如 oppo
    uint32 push_cnt = 2;  // 推送量
    map<string, uint32> task_id_push_cnt_map = 3;   // taskId 对应的推送量
}

message GetPaidOpOfflineSendPushCntResp {
    repeated PushCntInfo push_cnt_list = 1;
}