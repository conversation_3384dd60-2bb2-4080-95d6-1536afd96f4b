syntax="proto3";

option go_package = "golang.52tt.com/protocol/services/switch-scheme-checker";
package switch_scheme_checker;

message SchemeInfo {
    uint32 scheme_id = 1;
    string scheme_name = 2;
    uint32 scheme_svr_detail_type = 3; //废弃,用下面的scheme_detail_type
    uint32 scheme_detail_type = 4;    //玩法详细类型，用于区分不同玩法类型，例如是你行你唱还是pia戏，定义在channel-scheme_.proto的枚举类型SchemeDetailType
}

message CheckReq {
    uint32 op_uid = 1;
    uint32 cid = 2;
    SchemeInfo from_scheme = 3;
    SchemeInfo to_scheme = 4;
}
message CheckResp {
    int32 code = 1;  //0为成功，非0则把该值作为err code返回给客户端
    string msg = 2;   //如果不成功，则该值不为空，以该值作为err msg,如果该值为空，则用code的err msg
}

//从某玩法切换走或切换到某玩法的前置检查,用于切换玩法条件的判断
//可配对应的玩法是否需要调这个check，配了之后对应的玩法服务需要实现该接口
service SwitchSchemeChecker {
    rpc Check(CheckReq) returns(CheckResp) {}
}