syntax = "proto3";
package muse_http_go_logic;
option go_package = "golang.52tt.com/protocol/services/muse-http-go-logic";

//  生成 pb文件
//  protoc --go_out=plugins=grpc:./gen-go muse-channel-hot-game.proto
//    protoc  --go_out=./gen-go --go_opt=paths=source_relative --go-grpc_out=./gen-go --go-grpc_opt=paths=source_relative  muse-channel-hot-game.proto

message GetMuseChannelHotGameDetailRequest{
  string hot_game_id = 1;
}

message GetMuseChannelHotGameDetailResponse{
  HotGameDetail hot_game_detail = 1;
  uint32 user_channel_id = 2;
  repeated HotGameUser users = 3;
  HotGameUser user = 4;
}

message HotGameDetail {
  string id = 1;
  string title = 2;
  string desc = 3;
  string image = 4;
  uint32 member_count = 5;
  string tag = 6;
  repeated uint32 tab_ids = 7;
  NewHotGameTabResource new_hot_game_tab_resource = 8;
}

message NewHotGameTabVideoUrl {
  string cover_image_url=2;
  string video_url=1;

}

message NewHotGameTabImageUrl {
  string cover_image_url=1;
  string long_image_url=2;

}

message NewHotGameTabResource{
  oneof resource{
    NewHotGameTabImageUrl new_hot_game_tab_image_url=1;
    NewHotGameTabVideoUrl new_hot_game_tab_video_url=2;
  }
}




message HotGameUser {
  uint32 uid = 1;
  uint32 channel_id = 2;
  string account = 3;//头像
  string nick_name = 4;//用户昵称
  uint32 value = 5;//热度值
  uint32 status = 6;//0-无状态，1-在房，2-挑战中
  uint32 sex = 7;
  MusicChannelPersonalCert personal_cert = 8;//个人认证标签
  // 用户自己查看自己信息时独有
  int64 rank = 9; // 排名
  uint32 value_diff = 10; // 差距热度值
}

message MusicChannelPersonalCert{
  string icon = 1;
  string text = 2;
  repeated string color = 3;
  string text_shadow_color = 4;
  string introduce = 5; /* 介绍 */
  string jump_url = 6; /* 跳转链接 */
  string cert_type_name = 7; /* 类型名称 */
  repeated ExtraContent  extra_content=8;
}

message ExtraContent  {
  oneof extra_content{
    PersonalCertificationMuseSocialCommunity social_community=1;
  }
}

/*认证标对应的社群相关信息*/
message PersonalCertificationMuseSocialCommunity{
   string social_community_id=1;
   string name=2;
   string logo=3;
   string intro=4;
   string category_type_simple_desc=5;
  uint32 brand_professionalism = 6; //1-专业，2-非专业
}

message GetMuseUserInChannelIdRequest{
}

message GetMuseUserInChannelIdResponse{
  uint32 channel_id = 1;
}

message MuseSwitchChannelTabRequest{
  uint32 channel_id = 1;
  uint32 tab_id = 2;
}

message MuseSwitchChannelTabResponse{

}


// 获取所有的挑战并排行
message GetAllHotGameForRankRequest {
}

message GetAllHotGameForRankResponse {
  repeated HotGameDetail hot_game_detail_list = 1;
}

// 房间tab
message GetChannelListByHotGameIdRequest {
  string hot_game_id = 1;
}

message GetChannelListByHotGameIdResponse {
  map<uint32, HobbyChannelItem>  channel_view_map = 1;
}

message HobbyChannelItem {
  uint32 channel_id = 1;
  string channel_name = 2;

  string channel_owner_account = 3;
  int32 channel_owner_sex = 4;

  uint32 channel_member_count = 5;

  string publish_label = 6;    // 房间标签
  string publish_region = 7;   // 分类专区

  uint32 view_type = 8; // 客户端基于这个展示ui, see hobby-channel-view_.proto

  ViewData view_data = 9; // 此处修改为 ViewData

  uint32 rcmd_label = 10; // 推荐行为标签, see topic-channel_.proto

  string geo_info = 11; // 地理位置信息

  string icon = 12;//左上角图标

  string publish_desc = 13; //后台直接拼接分类简称与分区
  string song_title = 14; //当前歌曲
  bool high_quality = 15;//是否是优质房

  uint32 tab_id = 16;//玩法id
  uint64 region_id = 17;//blockId与elemId的拼接或者你行你唱的tagId,客户端埋点需要
  repeated uint32 fellow_cids = 18;
  string footprint = 19; //推荐trace id

  string review_account = 20;
  string review_desc = 21;
  int32 review_sex = 22;

  string bg = 23;//背景图片
  string dark_bg = 24;//深色背景图片
  uint32 channel_type = 26; // 房间类型
}

message ViewData{
  HobbyChannelViewRap rap = 1;
  HobbyChannelViewLeisure leisure = 2; // 挂房听歌/看书/摸鱼
  HobbyChannelViewKtv ktv = 3; // 一起K歌
  HobbyChannelViewSingASong sing_a_song = 4; // 你行你唱
  HobbyChannelViewPia pia = 5; // pia
  HobbyChannelViewInteresting interesting = 6; // 兴趣
  HobbyChannelBand band = 7; // 麦克乐队
  HobbyChannelViewSocial social = 8; // 社团
  HobbyChannelViewRadioLive radio_live = 9; // 语音直播房
  HobbyChannelViewChat chat = 10; //边唱边聊，默认模式
}

//一起K歌
message HobbyChannelViewKtv {
  uint32 singer_count = 1; // 数量大于1代表合唱

  repeated string order_song_titles = 2; // 已点歌曲名，第一首为当前播放
  uint32 order_song_count = 3; // 已点歌曲数量

  repeated string channel_member_accounts = 4; // 房间成员/麦上成员

  string glory_name = 5; // 称号名称
  string glory_img = 6; // 头标
  string glory_bg_img = 7; // 背景颜色
  uint32 glory_rank = 8; // 排行
  GloryLevel glory_level = 9;
  string glory_loc_code = 10;//位置code
  string glory_singer_id = 11;//歌手id

  string dan_img = 12;
  repeated HobbyChannelViewSong songs = 13;
  string topic = 14; // 话题
  string topic_icon = 15; // 话题
  int32 topic_type = 16;//0-话题，1-热聊话题


  enum GloryLevel{
    City = 0;//市级称号
    Province = 1;//省级称号
    Country = 2;//国服称号
  }
}

//你行你唱
message HobbyChannelViewSingASong {
  string status_desc = 1; // 接歌准备中/接歌进行中+第X/Y首（X为当前歌曲的次序，Y为总歌曲数）
  uint32 mic_empty_count = 2; //空麦位数
  string private_region = 3; // 专区信息
  repeated string order_song_titles = 4; // 已点歌曲名，第一首为当前播放
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 tagId = 5;//专区Id

  repeated HobbyChannelViewSong songs = 6;

}

//挂房听歌/看书/摸鱼
message HobbyChannelViewLeisure {
  repeated string order_song_titles = 1; // 正在播放的歌曲+未来2首歌曲（不足三首时都展示，0首时显示“正在挑选歌单”）

  string leisure_icon = 2; // 分类图标
  string leisure_desc = 3; // 分类描述

  repeated HobbyChannelViewSong songs = 4;
}

message HobbyChannelViewRap {
  repeated string order_song_titles = 1;  // 已点歌曲名，第一首为当前播放
  repeated string channel_member_accounts = 2; // 房间成员/麦上成员
  PersonalCert personal_cert = 3;

  message PersonalCert{
    string icon = 1;
    string text = 2;
    repeated string color = 3;
    string text_shadow_color = 4;
  }

  repeated HobbyChannelViewSong songs = 4;
  string topic = 5; // 话题
  string topic_icon = 6; // 话题
  int32 topic_type = 7;//0-话题，1-热聊话题

}

//边唱边聊，默认模式
message HobbyChannelViewChat {
  repeated string order_song_titles = 1; // 已点歌曲名，第一首为当前播放
  repeated HobbyChannelViewSong songs = 2;
  repeated string channel_member_accounts = 3; // 房间成员/麦上成员
  string topic = 4; // 话题
  string topic_icon = 5; // 话题
  int32 topic_type = 6;//0-话题，1-热聊话题

}

message HobbyChannelViewSocialRankHonorSignInfo{
  string icon = 1; /* icon */
  repeated string style_color_list = 2; /* 样式 底色 */
  string text = 3; /* 文案 */
}

//社团
message HobbyChannelViewSocial {
  string member_label_bg = 1;
  string member_label_color = 2;

  string member_label_dark_bg = 3;
  string member_label_dark_color = 4;

  string member_label_text = 5;
  repeated string channel_member_accounts = 6; // 房间成员头像
  HobbyChannelViewSocialRankHonorSignInfo rank_sign_info = 7; // 榜单（周榜）的荣誉标识
}

message HobbyChannelViewSong{
  string id = 1;
  string title = 2;
  repeated singer singers = 3;
  // buf:lint:ignore MESSAGE_PASCAL_CASE
  message singer{
    string id = 1;
    string name = 2;
  }
}

//麦克乐队
message HobbyChannelBand {
  string order_song_title = 1;
  string mic_band_role_desc = 2;//想找-主唱...
  string status = 3; // 状态演奏中
  repeated string channel_member_accounts = 4; // 房间成员/麦上成员

  HobbyChannelViewSong song = 5;//推荐用
  map<uint32, RoleInfo> mic_id_to_role = 6;

  message RoleInfo {
    BandRole role = 1;//角色枚举
    string role_desc = 2;//角色描述
    bool has_user = 3;//是否有用户
  }

  enum BandRole{
    BandRole_UNDEFINED = 0;
    BandRole_MAIN_SINGER = 1; // 主唱
    BandRole_GUITAR = 2; // 吉他
    BandRole_KEYBOARD = 3; // 键盘手
    BandRole_BASSIST = 4; // 贝斯手
    BandRole_DRUMMER = 5; // 鼓手
  }
}

//兴趣
message HobbyChannelViewInteresting {
  string song_title = 1; // 已点歌曲名
  repeated string channel_member_accounts = 2; // 房间成员/麦上成员
  string status = 3;
  string topic = 4; // 话题
  string topic_icon = 5; // 话题
  int32 topic_type = 6;//0-话题，1-热聊话题

}

//pia戏
message HobbyChannelViewPia {
  repeated string label = 1; //剧本标签
  string name = 2; // 剧本名称
  repeated string channel_member_accounts = 4; // 房间成员/麦上成员
  string status = 5;
  string topic = 6; // 话题
  string topic_icon = 7; // 话题
  int32 topic_type = 8;//0-话题，1-热聊话题
}

// 语音直播房
message HobbyChannelViewRadioLive {
  repeated string channel_member_accounts = 1; // 房间成员/麦上成员
  string status = 2; // 目前统一为直播中
  string topic = 3; // 话题
}

// 实验组挑战榜单页
message GetMuseChannelHotGameDetailV2Request{
  string hot_game_id = 1;
  int32 offset=2;
  int32 limit=3;
}

message GetMuseChannelHotGameDetailV2Response{
  HotGameDetail hot_game_detail = 1;
  uint32 user_channel_id = 2;
  repeated HotGameUser day_rank = 3;
  repeated HotGameUser total_rank = 4;
  HotGameUser day_user = 5;
  HotGameUser total_user = 6;
}