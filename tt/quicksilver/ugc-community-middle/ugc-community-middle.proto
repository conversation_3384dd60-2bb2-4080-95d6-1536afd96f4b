syntax = "proto3";

package ugc_community_middle;

option go_package = "golang.52tt.com/protocol/services/ugc-community-middle";

import "tt/quicksilver/ugc-community/ugc-community.proto";

service UgcCommunityMiddle {
    // 发布帖子
    rpc PublishPost(PublishPostRequest) returns(PublishPostResponse);
    // 生成发布帖子参数
    rpc GeneratePublishPostParam(GeneratePublishPostParamRequest) returns(GeneratePublishPostParamResponse);

    rpc GetNewsFeeds(GetNewsFeedsReq) returns(GetNewsFeedsResp);

    rpc GetPost(GetPostReq) returns(GetPostResp);

    rpc CommentSend(CommentSendRequest) returns (CommentSendResponse);
    rpc CommentFetch(CommentFetchRequest) returns (CommentFetchResponse);
    rpc CommentDelete(CommentDeleteRequest) returns (CommentDeleteResponse);
}

message PublishPostRequest {
    message Post {
        // 帖子ID
        string id = 1;
        // 帖子类型
        ugc_community.PostType type = 2;
        // 帖子状态
        ugc_community.PostState state = 3;
        // 发布来源
        ugc_community.PostOrigin origin = 4;
        
        // 内容
        string content = 5;
        // 附件列表
        repeated ugc_community.Attachment attachments = 6;
        
        // 业务数据
        ugc_community.PostBizData biz_data = 8;

        // 关联的话题id
        repeated string topic_id_list = 9;
    }
    
    Post post = 2;
    // 发帖引导任务token
    string task_token = 3;
}

message PublishPostResponse {
    // 帖子id
    string id = 1;
}

message GeneratePublishPostParamRequest {
    repeated ugc_community.Attachment attachments = 2;
}

message GeneratePublishPostParamResponse {
    // 提前生成的帖子ID，调用发布接口带上
    string id = 1;
    // obs app
    string obs_app = 2;
    // obs 桶
    string obs_scope = 3;
    // 返回的附件参数
    repeated ugc_community.Attachment attachments = 4;
    string obs_token = 5;
}


// ugc中的用户信息
message UserUgcCommunity {
    uint32 uid = 1; // id
    string account = 2; // 账号
    string alias = 3; // 数字账号
    string nickname = 4; // 昵称
    uint32 gender = 5; // 性别
}

enum PostSource {
    POST_SOURCE_AI_DISTRICT_UNSPECIFIED = 0;
    POST_SOURCE_AI_DISTRICT_RECOMMENDATION = 1;      // ai社区推荐流
    POST_SOURCE_AI_DISTRICT_PERSON = 2;          // ai社区个人流
}

// 帖子详情
message TopicInfo {
    // 话题id
    string id = 1;
    // 话题类型
    ugc_community.TopicType type = 2;
    // 话题名称
    string name = 3;
}

// 查看帖子详情
message PostInfo {
    message AigcCommunityBizPost {
        uint32 role_id = 1;
        uint32 role_type = 2;
        string role_name = 3;
        string role_avatar = 4;
        string role_character = 5;

        repeated ugc_community.AigcCommunityPost.ChatRecord chat_records = 6;

        string role_image = 7;
    }

    string post_id = 1;
    UserUgcCommunity post_owner = 2;
    uint32 post_type = 3;  //ugc_community.PostType
    string content = 4;
    repeated ugc_community.Attachment attachments = 5;
    int64 post_time = 6; // 发帖时间, unix second
    uint32 comment_count = 7;
    uint32 attitude_count = 8;

    uint32 origin = 9; // 发帖来源  PostOrigin

    uint32 biz_type = 10; // 业务类型 PostBizType

    map<string, string> biz_data = 11; // 业务数据 角色id放里面

    CommentItem comment_item = 12; //热评
    bool is_attitude = 13; //是否点赞
    ugc_community.PostState state = 14;  //匿名

    // 关联话题
    repeated TopicInfo topic_list = 15;

    bytes biz_bytes = 16;
    bool had_followed = 17; // 是否关注发帖人
}

message BaseRequest {
    string device_id = 1;
    uint32 market_id = 2;
    uint32 client_type = 3;
    uint32 client_version = 4;
}

message GetPostReq {
    BaseRequest base_req = 1;
    string post_id = 2;
    uint32 post_source = 3;  //PostSource
}

message GetPostResp {
    PostInfo post_info = 1;
}

message Feed {
    // 帖子
    PostInfo post = 1;

    string feed_id = 2;
}

message NewFeedsLoadMore {
    string last_feed_id = 1;
    uint32 last_page = 2;
    uint32 last_count = 3;
}

enum FeedMode {
    FEED_MODE_UNSPECIFIED = 0;
    FEED_MODE_NEXT_PAGE= 1; // 请求下一页
    FEED_MODE_REFRESH = 2; // 刷新
}

message GetNewsFeedsReq {
    BaseRequest base_req = 1;

    NewFeedsLoadMore load_more = 2; // 首次拉取不传, 加载更多时原封不动地填入上一次GetNewsFeedsResp中的load_more字段
    uint32 get_mode = 3; //1代表下一页，2代表刷新
    repeated string browse_post_ids = 4; //请求列表未曝光帖子列表
    uint32 post_source = 5; //PostSource

    // 主题id
    string subject_id = 6;
    // 角色id
    uint32 role_id = 7;
}


message GetNewsFeedsResp {
    repeated Feed feeds = 1; // feed列表, 数量可能会超过请求指定的count
    NewFeedsLoadMore load_more = 2; // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了
    string footprint = 3; // 推荐trace id
}

message CommentSendRequest{
    // 公参
    BaseRequest base_req = 1;
    // 帖子id
    string post_id = 2;
    // 首评论id，如果是回复帖子，这个字段为空
    string root_parent_id = 3;
    // 回复评论id，如果是第一条回复帖子或帖子的，这个字段为空
    string parent_id = 4;
    // 回复内容
    string content = 5;
    // 评论实体类型
    ugc_community.CommentEntityType type = 6;
    // 角色id，type=COMMENT_ENTITY_TYPE_ROLE时用到
    uint32 role_id = 7;
    // 评论来源
    ugc_community.CommentOrigin origin = 8;
}

message CommentSendResponse{
}

message CommentFetchRequest{
    BaseRequest base_req = 1;
    // 帖子id
    string post_id = 2;
    // 上一页最后的首次评id，分页使用
    string last_comment_id = 3;
}

message CommentUser {
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    uint32 sex = 4;
}

message CommentRole {
    uint32 role_id = 1; // 角色id
    string role_avatar = 2; // 角色头像
    string nickname = 3; // 角色昵称
    uint32 sex = 4; // 角色性别
}

message CommentEntity {
    ugc_community.CommentEntityType type = 1;
    CommentUser user = 2;
    CommentRole role = 3;
}

message CommentItem {
    // 评论id
    string id = 1;
    // Deprecated  评论人信息
    CommentUser send_user = 2;
    // 评论内容
    string content = 3;
    // 点赞数
    uint32 likes = 4;
    // 评论时间，ms
    int64 create_at = 5;
    // 二级评论信息
    message SecondCommentInfo {
        // 评论id
        string id = 1;
        // Deprecated  评论人信息
        CommentUser send_user = 2;
        // 评论内容
        string content = 3;
        // 评论时间，ms
        int64 create_at = 4;
        // Deprecated  回复评论的用户，评论的评论的回复用户
        CommentUser reply_user = 5;
        // 发布评论对象信息
        CommentEntity send_entity = 6;
        // 回复评论的对象，评论的评论的回复对象
        CommentEntity reply_entity = 7;
    }
    // 子评论，可能子评论很多，当前不做三级页面和分页处理，简单实现全部一次返回
    repeated SecondCommentInfo sub_comments = 6;
    bool is_attitude = 7; //是否点赞
    // 回复数
    uint32 reply_count = 8;
    // 发布评论对象信息
    CommentEntity send_entity = 9;
}

message CommentFetchResponse{
    repeated CommentItem comments = 1;
    bool is_load_finish = 2;
}

message CommentDeleteRequest{
    BaseRequest base_req = 1;
    string comment_id = 2;
}

message CommentDeleteResponse{
}
