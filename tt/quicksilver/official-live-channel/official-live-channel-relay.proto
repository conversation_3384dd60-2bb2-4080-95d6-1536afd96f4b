syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/official-live-channel";

package official_live_channel;


service OfficialLiveChannelRelay {
    // ----------------- 状态 ----------------------
    // 获取转播信息
    rpc GetRelay (GetRelayReq) returns (GetRelayResp) {
    }

    // 获取被转播信息
    rpc GetRelayBy (GetRelayByReq) returns (GetRelayByResp) {
    }

    rpc GetAllRelayBy (GetAllRelayByReq) returns (GetAllRelayByResp) {
    }

    // 获取转播详细信息
    rpc GetRelayDetail (GetRelayDetailReq) returns (GetRelayDetailResp) {
    }

    // 获取排班信息
    rpc GetRelayScheduleDetail (GetRelayScheduleDetailReq) returns (GetRelayScheduleDetailResp) {
    }

    // 禁用被排班信息
    rpc CancelRelay (CancelRelayReq) returns (CancelRelayResp) {
    }

    // ----------------- 排班 ----------------------
    // 设置排班信息
    rpc SetRelaySchedule (SetRelayScheduleReq) returns (SetRelayScheduleResp) {
    }

    // 获取排班信息
    rpc GetRelaySchedule (GetRelayScheduleReq) returns (GetRelayScheduleResp) {
    }

    // 获取排班列表
    rpc GetRelayScheduleList (GetRelayScheduleListReq) returns (GetRelayScheduleListResp) {
    }

    // 获取被排班信息
    rpc GetRelayBySchedule (GetRelayByScheduleReq) returns (GetRelayByScheduleResp) {
    }

    // 获取被排班信息
    rpc BatchGetRelayByScheduleEx (BatchGetRelayByScheduleExReq) returns (BatchGetRelayByScheduleExResp) {
    }

    // ----------------- 其他 ----------------------
    // 直播上报音频信息
    rpc ReportRelayAudio (ReportRelayAudioReq) returns (ReportRelayAudioResp) {
    }

    //获取官频直播间的描述信息
    rpc GetOfficialChannelDescribe (GetOfficialChannelDescribeReq) returns (GetOfficialChannelDescribeResp) {
    }

    //获取官频直播间的跟随信息
    rpc GetOfficialChannelFollowInfo (GetOfficialChannelFollowInfoReq) returns (GetOfficialChannelFollowInfoResp) {
    }

    //获取官频直播间的跟随信息
    rpc BatchGetOfficialChannelFollowInfo (BatchGetOfficialChannelFollowInfoReq) returns (BatchGetOfficialChannelFollowInfoResp) {
    }
}

message GetRelayReq {
    uint32 official_channel_id = 1; // 官频id
}
message GetRelayResp {
    uint32 channel_id = 1; // 排班直播间id
    uint32 anchor_id = 2; // 排班主播id
    RelayStatus relay_status = 3;
    int64 update_time = 4; // 更新时间，用于判断转播变更的时序
    int64 start_time = 5; // 当前节目开始时间戳
    int64 end_time = 6; // 当前节目结束时间戳
}

message GetRelayByReq {
    uint32 channel_id = 1; // 排班直播间id
}
message GetRelayByResp {
    uint32 official_channel_id = 1; // 官频id
    int64 start_time = 2; // 开始时间戳
    int64 end_time = 3; // 结束时间戳
    RelayStatus relay_status = 4;
    int64 update_time = 5; // 更新时间，用于判断转播变更的时序

    uint32 increase_follower_count = 6;
}

message GetAllRelayByReq {
}
message GetAllRelayByResp {
    map<uint32, GetRelayByResp> relay_by_list = 1;
}

message GetRelayDetailReq {
    uint32 official_channel_id = 1; // 官频id
}
message GetRelayDetailResp {
    // 当前排班信息
    RelaySection current = 1; // 当前节目，此处返回的结构体不包含显示不需要的信息
    RelaySection next = 2; // 下一节目，此处返回的结构体不包含显示不需要的信息

    // 当前转播信息
    repeated RelayAudio audios = 3; // 转播直播间音频流信息
    RelayStatus relay_status = 4; // 转播状态
    int64 update_time = 5; // 当前转播更新时间
}

message GetRelayScheduleDetailReq {
    uint32 official_channel_id = 1; // 官频id
    int64 schedule_update_time = 2; // 更新时间
}
// 当前排班信息
message GetRelayScheduleDetailResp {
    // 排班信息
    string desc_title = 1; // 官频标题
    string desc_image = 2; // 官频介绍
    string desc_small_image = 3; // 官频介绍小图

    RelaySchedule relay_schedule = 4; // 排班列表

    // 当前转播信息，用于当前节目单列表定位
    uint32 relay_section_id = 5; // 当前转播排班序号
    int64 relay_update_time = 6; // 用于客户端判断转播是否变更
}

message CancelRelayReq {
    uint32 official_channel_id = 1; // 官频id
    uint32 section_id = 2; // 排班序号
}
message CancelRelayResp {
}

message SetRelayScheduleReq {
    uint32 official_channel_id = 1;
    RelaySchedule schedule = 2;
    uint64 last_update_time = 3;    // 排班的上次更新时间
    string source = 4;              // 设置排班的来源方
}
message SetRelayScheduleResp {
}

message GetRelayScheduleReq {
    uint32 official_channel_id = 1;
    uint32 schedule_id = 2; // 排班计划场次标识
}
message GetRelayScheduleResp {
    RelaySchedule schedule = 1;
}

message GetRelayScheduleListReq {
    uint32 official_channel_id = 1;
    int64 schedule_start_time = 2; // 筛选在该时间以后开始的排期，不填返回全部
}
message GetRelayScheduleListResp {
    repeated RelaySchedule schedule = 1;
}

message GetRelayByScheduleReq {
    uint32 channel_id = 1;
}
message GetRelayByScheduleResp {
    uint32 official_channel_id = 1; // 官频id
    int64 start_time = 2; // 开始时间戳
    int64 end_time = 3; // 结束时间戳
    RelayStatus relay_status = 4; // 转播状态
}

message BatchGetRelayByScheduleExReq {
    repeated uint32 channel_ids = 1;
}
message BatchGetRelayByScheduleExResp {
    map<uint32, GetRelayByScheduleResp> relay_bys = 1;
}

message ReportRelayAudioReq {
    uint32 channel_id = 1; // 排班直播间id
    uint32 uid = 2;
    string stream_id = 3; // 为空代表删除
}
message ReportRelayAudioResp {
}

message RelaySection {
    uint32 section_id = 1; // 排班序号
    uint32 channel_id = 2; // 排班直播间id

    uint32 anchor_id = 3; // 排班主播id
    string anchor_account = 4; // 排班主播账号
    string anchor_nickname = 5; // 排班主播昵称
    int32 anchor_sex = 6; // 排班主播性别

    string banner = 7; // 展示图标
    string introduction_text = 8; // 排班直播介绍文案
    string introduction_img = 9; // 排班直播介绍图

    int64 start_time = 10; // 开始时间戳
    int64 end_time = 11; // 结束时间戳

    uint32 introduction_img_width = 12; // 排班直播介绍图宽
    uint32 introduction_img_height = 13; // 排班直播介绍图高

    repeated uint32 allow_anchor_tag_ids = 14; // 可报名主播品类
}

enum RelayStatus {
    UNKNOWN = 0; // 未转播
    WAITING = 1; // 等待中，节目切换时直播间未开播/正在pk触发
    RUNNING = 2; // 转播中
    BROKEN = 3; // 已中断，管理员中断触发
    PAUSE = 4; // 已暂停
}

message RelayAudio {
    uint32 uid = 1;
    string stream_id = 2;
}

message RelaySchedule {
    uint32 schedule_id = 1; // 排班计划场次标识， 重复代表修改
    repeated RelaySection sections = 2; // 排班列表
    uint32 next_schedule_id = 3; // 下一个排班计划
    int64 update_time = 4;
    int64  schedule_date = 5; // 排班场次日期
    string schedule_name = 6; // 排班场次名称
}

message GetOfficialChannelDescribeReq {
    repeated uint32 official_channel_ids = 1; // 官频id
}

message GetOfficialChannelDescribeResp {
    map<uint32, OfficialChannelDescribe> official_channel_desc = 1; //官频描述信息
}

message OfficialChannelDescribe {
    string identity_text = 1; //标识文案，如：音乐官频
    string status_text = 2; //状态文案
}

message OfficialChannelFollowInfo {
    string follow_text = 1;
}

message GetOfficialChannelFollowInfoReq {
    uint32 official_channel_id = 1;
}
message GetOfficialChannelFollowInfoResp {
    OfficialChannelFollowInfo info = 1;
}

message BatchGetOfficialChannelFollowInfoReq {
    repeated uint32 official_channel_id = 1;
}
message BatchGetOfficialChannelFollowInfoResp {
    map<uint32, OfficialChannelFollowInfo> infos = 1;
}
