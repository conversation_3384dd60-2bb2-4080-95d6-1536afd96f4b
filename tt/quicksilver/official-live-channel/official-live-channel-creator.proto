syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/official-live-channel";

package official_live_channel;

service OfficialLiveChannelCreator {
    // ----------------- 房间 ----------------------
    // 创建官频
    rpc CreateChannel (CreateChannelReq) returns (CreateChannelResp) {
    }

    // 获取官频列表
    rpc ListChannel (ListChannelReq) returns (ListChannelResp) {
    }

    // 修改房间信息
    rpc ModifyChannel (ModifyChannelReq) returns (ModifyChannelResp) {
    }

    // 获取房间信息
    rpc GetOfficialChannelInfo (GetOfficialChannelInfoReq) returns (GetOfficialChannelInfoResp) {
    }

    // 获取官频管理员列表
    rpc GetChannelAdmin (GetChannelAdminReq) returns (GetChannelAdminResp) {
    }

    // 设置官频管理员
    rpc SetChannelAdmin (SetChannelAdminReq) returns (SetChannelAdminResp) {
    }
}

message CreateChannelReq {
    string name = 1; // 房间名
}
message CreateChannelResp {
    uint32 official_channel_id = 1; // 官频id
}

message ListChannelReq {
}
message ListChannelResp {
    repeated uint32 official_channel_ids = 1; // 官频id
}

message ModifyChannelReq {
    uint32 official_channel_id = 1;

    string name = 2; // 房间名

    string desc_title = 3; // 官频标题
    string desc_image = 4; // 官频介绍
    string desc_small_image = 5; // 官频介绍小图
    string identity_text = 6;  //标识文案
    string follow_text = 7; // 跟随文案
}
message ModifyChannelResp {
}

enum ChannelAdminRole {
    CHANNEL_INVALID_ROLE = 0; // 无效
    CHANNEL_OWNER = 1; // 频道所有者
    CHANNEL_ADMIN = 2; // 频道管理员
    CHANNEL_NORMAL = 3; // 普通用户
    CHANNEL_ADMIN_SUPER = 4; // 频道超级管理员
}

message GetChannelAdminReq {
    uint32 official_channel_id = 1;
}
message GetChannelAdminResp {
    map<uint32, ChannelAdminRole> admins = 1; // uid -> role, 1: 频道所有者, 2: 频道管理员, 4: 超管
}

message SetChannelAdminReq {
    uint32 official_channel_id = 1;
    uint32 uid = 2;

    ChannelAdminRole role = 3; // role = CHANNEL_NORMAL_ROLE 代表删除管理员
}
message SetChannelAdminResp {
}

message GetOfficialChannelInfoReq{
    uint32 official_channel_id = 1;
}

message GetOfficialChannelInfoResp{
    string name = 1; // 房间名
    string desc_title = 2; // 官频标题
    string desc_image = 3; // 官频介绍
    string desc_small_image = 4; // 官频介绍小图
    string identity_text = 5;  //标识文案
    string follow_text = 6; // 跟随文案
}