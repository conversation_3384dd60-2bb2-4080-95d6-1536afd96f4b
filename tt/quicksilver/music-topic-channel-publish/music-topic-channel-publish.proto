syntax = "proto3";

package music_topic_channel_publish;

option go_package = "golang.52tt.com/protocol/services/music-topic-channel-publish";

service MusicChannel {
  //修改主题房字段
  rpc SetMusicChannelReleaseInfo(SetMusicChannelReleaseInfoReq) returns (SetMusicChannelReleaseInfoResp);
  //解散主题房
  rpc DismissMusicChannel(DismissMusicChannelReq) returns (DismissMusicChannelResp);
  //获取房间信息
  rpc GetMusicChannelByIds(GetMusicChannelByIdsReq) returns (GetMusicChannelByIdsResp);

  //切换房间玩法
  rpc SwitchChannelTab(SwitchChannelTabReq) returns (SwitchChannelTabResp);

  // 创建临时房
  rpc AddTemporaryChannel(AddTemporaryChannelReq) returns (AddTemporaryChannelResp);

  //获取发布中的房间
  rpc ListPublishingChannelIds(ListPublishingChannelIdsReq) returns (ListPublishingChannelIdsResp);

  //获取发布中的房间
  rpc IsPublishing(IsPublishingReq) returns (IsPublishingResp);

  //  //获取房间列表（兜底推荐）
  //  rpc GetMusicChannelList(GetMusicChannelListReq) returns (GetMusicChannelListResp);

//  //清除房间展示在tab
//  rpc DisappearChannel(DisappearChannelReq) returns (DisappearChannelResp);
//
//  //获取主题房数，以及示例用户
//  rpc GetOnlineInfo(GetOnlineInfoReq) returns (GetOnlineInfoResp);

//  // 获取指定房间类型人数
//  rpc GetChannelRoomUserNumber(GetChannelRoomUserNumberReq) returns (GetChannelRoomUserNumberResp);

  //历史记录保存
//  rpc SetExtraHistory(SetExtraHistoryReq) returns (SetExtraHistoryResp);
//  rpc GetExtraHistory(GetExtraHistoryReq) returns (GetExtraHistoryResp);

  //冻结主题房
//  rpc FreezeChannel(FreezeChannelReq) returns (FreezeChannelResp);
  //  //解除冻结主题房
  //  rpc UnfreezeChannel(UnfreezeChannelReq) returns (UnfreezeChannelResp);
  //  rpc GetChannelFreezeInfo(GetChannelFreezeInfoReq) returns (GetChannelFreezeInfoResp);
}

message IsOlderForMusicHomePageReq{
  uint32 uid = 1;
}


message IsOlderForMusicHomePageResp{
  bool is_older = 1;
}

message GetMusicChannelFilterV2Req{
  string filter_type = 1;//默认音乐首页
  uint32 market_id = 2;
  uint32 uid = 3;
}

message GetMusicChannelFilterV2Resp{
  repeated FilterItem filter_items = 1;
  message FilterItem {
    string title = 1;
    string filter_item_type = 2;//首页Item HOME_FILTER_ITEM,页面房间Item PAGE_FILTER_ITERM,页面帖子 PAGE_POST
    string filter_id = 3;//具体接口的通用参数，后续可能还需要不同类型有不同参数
    repeated FilterSubItem filter_sub_items = 4;
  }

  message FilterSubItem {
    string title = 1;
    string filter_sub_id = 2;//具体接口的通用参数，后续可能还需要不同类型有不同参数
    string filter_sub_item_type = 3;//SUB_ITEM_POST帖子,SUB_ITEM_MUSIC音乐流
  }

  enum FilterItemType {
    HOME_FILTER_ITEM = 0;
    PAGE_FILTER_ITERM = 1;
    PAGE_POST = 2;
  }
}

message IsPublishingReq{
  uint32 tab_id = 1;
  uint32 channel_id = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message IsPublishingResp{
  bool isPublishing = 1;
}

message ListPublishingChannelIdsReq {
  uint32 tab_id = 1;
  uint32 limit = 2;
  int64 time_offset = 3;//秒
}

message ListPublishingChannelIdsResp {
  repeated channel channels = 1;
  // buf:lint:ignore MESSAGE_PASCAL_CASE
  message channel {
    uint32 id = 1;
    int64 score = 2;
  }
}



message BlockOptionList {
  repeated BlockOption block_options = 1;
}

message BlockOption {
  uint32 block_id = 1;           //块id
  uint32 elem_id = 2;            //块里面的元素id
}

enum ChannelDisplayType {
  DISPLAY_AT_MAIN_PAGE = 0;           //大厅展示
  DISMISSED = 1;                      //不展示
  DISPLAY_AT_FIND_FRIEND = 2;         //找好友玩。跟随显示
  TEMPORARY = 3;                      //临时房
}

message MusicChannelReleaseInfo {
  uint32 id = 1;
  uint32 tab_id = 2;
  int64 release_time = 3;
  repeated BlockOption block_options = 4;    //房间选择的开房标签信息
  repeated ChannelDisplayType display_type = 5;
  bool show_geo_info = 6;                     //是否显示地理信息
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SetMusicChannelReleaseInfoReq {
  MusicChannelReleaseInfo Music_channel_release_info = 1;
  bool want_fresh = 2;                       //是否优先匹配萌新
  string release_ip = 3;                     //房间发布时候的IP
  string channel_name = 4;
  uint32 creator = 5;
}

message SetMusicChannelReleaseInfoResp {

}

message DismissMusicChannelReq {
  uint32 channel_id = 1;
}

message DismissMusicChannelResp {
  bool dismiss = 1;
}

message GetRecommendChannelListLoadMore {
  uint32 num = 1;
}

message GetMusicChannelListReq {
  uint32 uid = 1;
  uint32 limit = 2;
  repeated uint32 tab_id_list = 3;
}

message GetMusicChannelListResp {
  repeated MusicChannelReleaseInfo channel_list = 1;
}

message GetMusicChannelByIdsReq {
  repeated uint32 ids = 1;
  repeated ChannelDisplayType types = 2;
  bool return_all = 3;
}

message GetMusicChannelByIdsResp {
  repeated MusicChannelReleaseInfo info = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SwitchChannelTabReq{
  uint32 uid = 1;
  uint32 tab_id = 2;
  uint32 channel_id = 3;
  uint32 appId = 4;
  uint32 marketId = 5;
}
message SwitchChannelTabResp{
  string tab_name = 1;
  repeated string welcome_txt_list = 2;
  uint32 mic_mod = 3;
  uint32 tab_type = 4;
  uint32 tag_id = 5;
}

message DisappearChannelReq {
  string client_id = 1;
  uint64 acquire_duration = 2;        //超时此时间段可重入

  message Timeout {
    uint64 timeout_duration = 1;    //创建超出该时间段清除
  }

  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  message Keepalive {
    uint64 Keepalive_duration = 1; //不保持心跳超出该时间段
    uint32 member_count = 2;        //房间人数大于此数
  }

  message ReleaseTimeout {
    uint64 timeout_duration = 1;    //创建超出该时间段清除
  }

  Timeout timeout_event = 10;
  Keepalive keepalive_event = 11;
  ReleaseTimeout release_timeout_event = 12; //发布超出时间段 v5.5.0
}

message DisappearChannelResp {
  repeated uint32 channel_ids = 1;
}

message GetOnlineInfoReq {
  uint32 online_user_count = 1;
}

message GetOnlineInfoResp {
  uint32 room_count = 1;
  repeated uint32 online_user_list = 2;
}

message FreezeChannelReq {
  repeated uint32 channel_id_list = 1;
  int64 freeze_time = 2;                   //冻结多久，单位是秒，永久冻结传-1
}

message FreezeChannelResp {

}

message UnfreezeChannelReq {
  repeated uint32 channel_id_list = 1;

}

message UnfreezeChannelResp {

}

message GetChannelFreezeInfoReq {
  uint32 channel_id = 1;
}

message GetChannelFreezeInfoResp {
  int64 freeze_time = 1;
}

enum HistoryType {
  CreateHistory = 0;
  UpdateHistory = 1;
}

message SetExtraHistoryReq {
  uint32 channel_id = 1;
  HistoryType history_type = 2;
  int64 expire_after = 3;     //秒为单位，小于等于0即不过期
}

message SetExtraHistoryResp {

}

message GetExtraHistoryReq {
  uint32 channel_id = 1;
  HistoryType history_type = 2;
}

message GetExtraHistoryResp {
  string value = 1;
}

message GetChannelPlayModelReq {
  uint32 channel_id = 1;
}

message GetChannelPlayModelResp {
  uint32 tab_id = 1; // 玩法id
}

message GetChannelRoomUserNumberReq {
  repeated uint32 tab_id = 1; // 如果不指定，则返回全部
}

message GetChannelRoomUserNumberResp {
  message RoomUserInfo {
    uint32 tab_id = 1;
    int64 total_user_number = 2;
  }
  repeated RoomUserInfo room_user_info = 1;
}

message AddTemporaryChannelReq {
  MusicChannelReleaseInfo channel = 1; // 复用channel
}

message AddTemporaryChannelResp {}

