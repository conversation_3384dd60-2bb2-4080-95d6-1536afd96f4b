syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channelmemberviprank-go";
package channelmemberviprank_go;
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service ChannelMemberVipRankGo {
  // 获取房间小时榜
  rpc GetChannelHourRankList(GetChannelHourRankListReq) returns (GetChannelHourRankListResp){}

  // 通过房间id获取房间在小时榜中的排名信息
  rpc GetHourRankById(GetHourRankByIdReq) returns (GetHourRankByIdResp){}

  // 获取房间小时榜所有分类top1 此接口仅欢游app调用
  rpc GetChannelHourRankTop1 (GetChannelHourRankTop1Req) returns (GetChannelHourRankTop1Resp) {}

  // 获取所有分类小时榜
  rpc GetHourRankAllTagList (GetHourRankAllListReq) returns (GetHourRankAllListResp) {}

  // 手动恢复小时榜数据
  rpc RecoverChannelHourRank (Empty) returns (Empty) {}
  // 重置指定房间的小时榜排名数据
  rpc RecoverChannelHourRankByChannelId (RecoverChannelHourRankByChannelIdReq) returns (Empty) {}

  // 创建小时榜奖励分
  rpc CreateHourRankAwardScore (CreateHourRankAwardScoreReq) returns (CreateHourRankAwardScoreResp) {}

  rpc TestPushHourRankBreakingNews(TestPushHourRankBreakingNewsReq)  returns (Empty) {}
  rpc TestSettleHourRank(TestSettleHourRankReq) returns (Empty) {}


  /*********对账接口V2**********/
  rpc GetConsumeTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetConsumeOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  rpc FixHourRankScoreByOrderId(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}


}

message TestPushHourRankBreakingNewsReq {
  uint32 cid = 1;
}

message TestSettleHourRankReq {
  enum SettleType {
    HourRank = 0;
    HourMemberRank = 1;
    HourNewStarRank = 2;
  }
  SettleType settle_type = 1;
  bool certain = 2;
}

enum SourceType {
  UnknownType = 0;
  SourceTypePresent = 1;
  SourceTypeKnight = 4;
  SourceTypeWerewolf = 5;
  SourceTypeAwardScore = 6;
}

enum AwardScoreSourceType {
  AwardScoreSourceTypeInvalid = 0;
  AwardScoreSourceTypeGrant = 1; // 发放
  AwardScoreSourceTypePresent = 2; // 收礼
  AwardScoreSourceTypeTopN = 3; // TopN
}

message Empty {
}

message GetChannelHourRankListReq {
  uint32 type = 1;       // see ChannelHourRankType
  uint32 hour = 2;
  uint32 begin = 3;
  uint32 limit = 4;

  // 需要获取本房间的排名信息才填以下字段
  uint32 cid = 5;            // 当前房间id
  uint32 view_cnt = 6; // 半屏页榜单上配置的展示个数
  uint32 multi_diem_rank_type = 7; // 多维度榜单类型 see MultiDimChannelRankType
  uint32 uid = 8; // 成员uid
}

message GetChannelHourRankListResp {
  repeated ChannelHourRankInfo rank_info_list = 1;
  uint32 real_view_cnt = 2;              // 当前半屏页榜单上实际展示个数
  ChannelHourRankInfo my_rank_info = 3;  // 当前房间的排名信息
  repeated ChannelHourRankInfo last_top_rank_list = 7; // 上一小时的TopN
}

message ChannelHourRankInfo {
  uint32 cid = 1;
  uint32 score = 2;   // 分数
  uint32 rank = 3;   // 排名
  uint32 d_value = 4; // 分数差值（当排名为第一名时d_value为与第二名的差值，否则d_value为与上一名的差值）
  uint32 tag_id = 5; // (仅在ChannelHourRankType 为 TotalHourRank 时有效)房间所在分类的tag_id
  uint32 uid = 6; // 成员uid
  uint32 award_score = 7; // 奖励分
}

message GetHourRankByIdReq {
  uint32 cid = 1;
  uint32 type = 2;       // see ChannelHourRankType
  uint32 hour = 3;
  uint32 view_cnt = 4;   // 半屏页榜单上配置的展示个数
  uint32 multi_diem_rank_type = 5; // 多维度榜单类型 see MultiDimChannelRankType
}

message GetHourRankByIdResp {
  ChannelHourRankInfo rank_info = 1;
  uint32 real_view_cnt = 2;          // 当前半屏页榜单上实际展示个数
}


message GetChannelHourRankTop1Req {
  uint32 hour = 1;
}
message GetChannelHourRankTop1Resp {
  uint32 hour = 1;
  repeated GetChannelHourRankTop1Info rank_list = 2;
}
message GetChannelHourRankTop1Info {
  uint32 tag_id = 1;
  string tag_name = 2;
  uint32 channel_id = 3;
  uint32 score = 4;
}

message RecoverChannelHourRankByChannelIdReq {
  repeated uint32 channel_id_list = 1;
}

message CreateHourRankAwardScoreReq {
  // 发放目标
  enum DestType {
    DestTypeInvalid = 0;
    DestTypeChannel = 1; // 房间
    DestTypeMember = 2; // 成员
  }
  DestType dest_type = 1;
  uint32 obj_id = 2; // 发放目标 channel_id || uid
  uint32 score = 3; // 发放分数
  int64 active_time = 4; // 生效时间戳
  string order_id = 5; // 上游订单号
  int64 start_time = 6; // 生效时间范围（active_time不为空时范围失效）
  int64 end_time = 7; // 生效时间范围
}
message CreateHourRankAwardScoreResp {

}


message GetHourRankAllListReq {
  uint32 hour = 1;
}

message GetHourRankAllListResp {
  message HourRankType {
    uint32 tag_id = 1; // 房间标签（品类）
    uint32 multi_diem_rank_type = 2; // 多维度榜单类型 see MultiDimChannelRankType
    repeated ChannelHourRankInfo rank_list = 3;
  }
  repeated HourRankType rank_type_list = 1; // 榜单类型
  int64 server_time = 2; // 服务器时间
}