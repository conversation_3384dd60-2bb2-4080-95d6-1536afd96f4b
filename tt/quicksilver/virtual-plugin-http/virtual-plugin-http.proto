syntax="proto3";

option go_package = "golang.52tt.com/protocol/services/virtual-plugin-http";
// namespace
package virtual_plugin_http;

// 与虚拟主播插件平台喵播的交互HTTP-API协议定义
//https://q9jvw0u5f5.feishu.cn/wiki/FiU9w38cKiIbebkqM4qczQEgnmj

 
//API-获取礼物配置
message GetGiftConfsReq {
	string chn_secret = 1;
	string req_time   = 2;
	string sign       = 3;
}

message GiftConf {
	uint32 gift_id   = 1;
	string gift_name = 2;
	uint32 price     = 3; //礼物价格
	string gift_url  = 4; //礼物图标
	uint32 type      = 5; //礼物价格类型 1红钻，2T豆
}
message GetGiftConfsRsp {
	uint32 err_code = 1;
	string err_msg  = 2;
	repeated GiftConf data = 3;
}

//API -获取主播信息
message GetAnchorReq {
	string chn_secret = 1;
	string req_time   = 2;
	string sign       = 3;
}
message AnchorInfo {
	string  ttid = 1;
	string  name = 2;
	string  head_url = 3;
}
message GetAnchorRsp {
	uint32 err_code = 1;
	string err_msg  = 2;
	AnchorInfo data = 3;	
}


//API -获取普通表情
message GetEmojiReq {
	string chn_secret = 1;
	string req_time   = 2;
	string sign       = 3;
}
message EmojiConf {
	string name = 1;
	string url  = 2;
}
message GetEmojiRsp {
	uint32 err_code = 1;
	string err_msg  = 2;
	repeated EmojiConf data = 3;
}

//API -定时轮询房间消息
message ChannelMsg {
	string sender_id   = 1;      //用户id
	string sender_name = 2;      //用户呢称
	string sender_head = 3;      //用户头像
	uint32 msg_type    = 4;      //消息类型
	string content     = 5;      //内容
	uint32 send_time   = 6;      //发送时间戳
}
message ChannelMsgData {
	uint32 last_seq  = 1;
	repeated ChannelMsg msg_list = 2;
}

message GetChannelMsgReq {
	string chn_secret = 1;
	string req_time   = 2;
	string sign       = 3;
	uint32 last_seq   = 4;
}
message GetChannelMsgRsp {
	uint32 err_code = 1;
	string err_msg  = 2;
	ChannelMsgData data = 3;
}


//API -定时轮询送礼消息
message SendGiftMsg {
	string sender_id   = 1;      //送礼者ID
	string sender_name = 2;      //送礼者呢称
	string sender_head = 3;      //送礼者头像
	string receiver_id = 4;      //收礼者ID
	string receiver_name = 5;      //收礼者呢称
	string receiver_head = 6;      //收礼者头像
	uint32 gift_id  = 7;           //礼物ID
	uint32 gift_num = 8;           //送礼数量
	uint32 send_time   = 9;      //发送时间戳
}
message SendGiftData {
	uint32 last_time  = 1;
	repeated SendGiftMsg msg_list = 2;
}

message GetGiftMsgReq {
	string chn_secret = 1;
	string req_time   = 2;
	string sign       = 3;
	uint32 last_time  = 4;
}
message GetGiftMsgRsp {
	uint32 err_code = 1;
	string err_msg  = 2;
	SendGiftData data = 3;
}


//API-定时轮询房间热度值
message ChannelHot {
	uint64 hot = 1;
}
message GetChannelHotReq {
	string chn_secret = 1;
	string req_time   = 2;
	string sign       = 3;
}
message GetChannelHotRsp {
	uint32 err_code = 1;
	string err_msg  = 2;
	ChannelHot data = 3;
}
