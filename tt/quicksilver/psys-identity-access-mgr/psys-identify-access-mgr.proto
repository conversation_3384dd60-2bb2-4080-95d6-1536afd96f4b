syntax = "proto3";

package psys_identify_access_mgr;
option go_package = "golang.52tt.com/protocol/services/psys-identify-access-mgr";

import "tt/quicksilver/extension/options/options.proto";

service PSysIdentifyAccessMgr {
  option (service.options.service_ext) = {
    service_name: "psys-identify-access-mgr"
  };

  //业务方注册,删除
  rpc RegisterBiz(RegisterBizReq) returns (RegisterBizResp) {}
  rpc DeleteBiz(DeleteBizReq) returns (DeleteBizResp) {}
  //密钥生成,删除
  rpc GenerateKey(GenerateKeyReq) returns (GenerateKeyReq) {}
  rpc DeleteKey(DeleteKeyReq) returns (DeleteKeyResp) {}

  //平台能力注册
  rpc RegisterPSys(RegisterPSysReq) returns (RegisterPSysResp) {}
  //增删改平台能力的服务信息
  rpc UpdatePSysSvc(UpdatePSysSvcReq) returns (UpdatePSysSvcResp) {}
  rpc AddPSysSvc(AddPSysSvcReq) returns (AddPSysSvcResp) {}
  rpc DeletePSysSvc(DeletePSysSvcReq) returns (DeletePSysSvcResp) {}

  //申请，删除访问权限
  rpc ApplyAccessPermission(ApplyAccessPermissionReq) returns (ApplyAccessPermissionResp) {}
  rpc DeleteAccessPermission(DeleteAccessPermissionReq) returns (DeleteAccessPermissionResp) {}

  //Get
  rpc GetAllBiz(GetAllBizReq) returns (GetAllBizResp) {}
  rpc GetAllPSys(GetAllPSysReq) returns (GetAllPSysResp) {}
}

message RegisterBizReq {
  string biz_id = 1;
  string biz_desc = 2;
}
message PriKeyInfo {
  string key_id = 1;
  string private_key = 2;
}
message RegisterBizResp {
  PriKeyInfo pri_key_info = 1;        //注册成功后，生成默认密钥对
}
message DeleteBizReq {
  string biz_id = 1;
}
message DeleteBizResp {
}
message GenerateKeyReq {
  string biz_id = 1;
  string key_desc = 2;
}
message GenerateKeyResp {
  PriKeyInfo pri_key_info = 1;
}
message DeleteKeyReq {
  string biz_id = 1;
  string key_id = 2;
}
message DeleteKeyResp {
}

enum ApiMatchType {
  Prefix = 0;
  Exact = 1;
}
message ApiInfo {
  ApiMatchType match_type = 1;
  string match_value = 2;
}
enum SvcProtocolType {
  HTTP = 0;
  GRPC = 1;
}
message PSysSvcInfo {
  string svc_name = 1;
  SvcProtocolType protocol = 2;
  string svc_addr = 3;
  repeated ApiInfo svc_api_info = 4;
}
message RegisterPSysReq {
  string p_sys_id = 1;
  string p_sys_desc = 2;
  repeated PSysSvcInfo svc_list = 3;
}
message RegisterPSysResp{
}
message UpdatePSysSvcReq {
  string p_sys_id = 1;
  map<string, PSysSvcInfo> name_to_svc = 2;
}
message UpdatePSysSvcResp {
}
message AddPSysSvcReq {
  string p_sys_id = 1;
  repeated PSysSvcInfo svc_list = 2;
}
message AddPSysSvcResp {
}
message DeletePSysSvcReq {
  string p_sys_id = 1;
  repeated string svc_name_list = 2;
}
message DeletePSysSvcResp {
}
message ApplyAccessPermissionReq {
  string biz_id = 1;
  repeated string p_sys_id_list = 2;
}
message ApplyAccessPermissionResp {
}
message DeleteAccessPermissionReq {
  string biz_id = 1;
  repeated string p_sys_id_list = 2;
}
message DeleteAccessPermissionResp {
}

message PubKeyInfo {
  string key_id = 1;
  string public_key = 2;
  string key_desc = 3;
}
message BizInfo {
  string biz_id = 1;
  string biz_desc = 2;
  repeated PubKeyInfo key_list = 3;
}
message GetAllBizReq {
}
message GetAllBizResp {
  repeated BizInfo biz_list = 1;
}
message GetAllPSysReq {
}
message PSysInfo {
  string p_sys_id = 1;
  string p_sys_desc = 2;
  repeated PSysSvcInfo svc_list = 3;
  repeated string permit_access_biz_id_list = 4;
}
message GetAllPSysResp {
  repeated PSysInfo psys_info_list = 1;
}