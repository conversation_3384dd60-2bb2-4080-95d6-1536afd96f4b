syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/huntmonstermission";
package huntmonstermission;

import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service HuntMonsterMission {
      rpc GetUserHuntMonsterMission (GetUserHuntMonsterMissionReq) returns (GetUserHuntMonsterMissionResp) {
      }
      rpc TestTools (TestToolsReq) returns (TestToolsResp) {
      }
      rpc CheckChannelIsRec (CheckChannelIsRecReq) returns (CheckChannelIsRecResp) {
      }
      rpc GetUserHuntMissionInfo (GetUserHuntMissionInfoReq) returns (GetUserHuntMissionInfoResp) {
      }

  // ===================== 礼物kafka对账 =========================
      //礼物->打龙道具对账
      rpc TimeRangeCount (ReconcileV2.TimeRangeReq ) returns (ReconcileV2.CountResp ) {}
      rpc TimeRangeOrderIds (ReconcileV2.TimeRangeReq ) returns (ReconcileV2.OrderIdsResp ) {}
      // 补单打龙道具
      rpc FixPresentCountOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}
}

enum EnumMissionType {
    E_UNKOWN = 0;
    E_STAY_CHANNEL_ONE_MINUTE = 1;     // 进房停留1分钟
    E_STAY_CHANNEL_CONTINUE = 2;      // 进房连续停留
    E_ENTER_CHANNEL_HOUR_RANK = 3;      // 从小时榜进房
    E_SEND_PRESENT = 4;             // 送任意T豆礼物
    E_SEND_ONE_THOUSAND_PRESENT = 5;  //单笔送≥1000T豆的礼物
    E_FIRST_SEND_TBEAN_PRESENT = 6;   // 首次送T豆礼物
    E_ADD_FANS_GROUP = 7;             // 加入粉丝团
}

message GetUserHuntMonsterMissionReq {
    uint32 uid = 1;
}
message GetUserHuntMonsterMissionResp {
   uint32 stay_channel_one_cnt = 1;    // 在房间停留1分钟次数
   uint32 stay_channel_five_cnt = 2;   // 在房间停留5分钟次数
   uint32 enter_channel_hour_rank_cnt = 3;   // 从小时榜进房次数
   uint32 send_present_cnt = 4;     // 送任意T豆礼物次数
   uint32 send_one_thousand_present_cnt = 5;  // 单笔送≥1000T豆的礼物次数
   uint32 first_send_tbean_present_cnt = 6;   // 首次送T豆礼物次数
   uint32 add_fans_group_cnt = 7;         // 加入粉丝团次数
}

message TestToolsReq {
   uint32 uid = 1;
}
message TestToolsResp {
}

message CheckChannelIsRecReq {
   uint32 channel_id = 1;
}
message CheckChannelIsRecResp {
   bool  is_rec = 1;
}

message GetUserHuntMissionInfoReq {
   uint32  uid = 1;
   uint32  mission_type = 2;   // see EnumMissionType  same as ga_base.proto HuntMonsterMissionType
   uint32  channel_id = 3;
}
message GetUserHuntMissionInfoResp {
   bool   is_finish = 1;
   string pop_msg = 2;
   int64  update_ms = 3;
}


