syntax = "proto3";

package ugc_community_http_logic;

import "google/api/annotations.proto";
import "tt/quicksilver/ugc-community/ugc-community.proto";
import "tt/quicksilver/ugc-community-middle/ugc-community-middle.proto";

option go_package = "golang.52tt.com/protocol/services/ugc-community-http-logic/ugc-community-http-logic";

service UgcCommunityHttpLogic {
    // 生成发布帖子需要的参数
    rpc GeneratePublishPostParam(GeneratePublishPostParamRequest) returns (GeneratePublishPostParamResponse) {
        option (google.api.http) = {
            post: "/ugc-community-http-logic/post/publish/param/generate"
        };
    }

    // 发布帖子
    rpc PublishPost(PublishPostRequest) returns (PublishPostResponse) {
        option (google.api.http) = {
            post: "/ugc-community-http-logic/post/publish"
        };
    }

    // 删除帖子
    rpc DeletePost(DeletePostRequest) returns (DeletePostResponse) {
        option (google.api.http) = {
            post: "/ugc-community-http-logic/post/delete"
        };
    }

    // 发评论
    rpc CommentSend(CommentSendRequest) returns (CommentSendResponse) {
        option (google.api.http) = {
            post: "/ugc-community-http-logic/comment/send"
            body: "*"
        };
    }

    // 拉评论
    rpc CommentFetch(CommentFetchRequest) returns (CommentFetchResponse) {
        option (google.api.http) = {
            post: "/ugc-community-http-logic/comment/fetch"
            body: "*"
        };
    }

    // 删除评论
    rpc CommentDelete(CommentDeleteRequest) returns (CommentDeleteResponse) {
        option (google.api.http) = {
            post: "/ugc-community-http-logic/comment/delete"
            body: "*"
        };
    }

    rpc GetNewsFeeds(GetNewsFeedsReq) returns (GetNewsFeedsResp) {
        option (google.api.http) = {
            post: "/ugc-community-http-logic/post/getnewsfeeds"
            body: "*"
        };
    }

    rpc GetPost(GetPostReq) returns (GetPostResp) {
        option (google.api.http) = {
            post: "/ugc-community-http-logic/post/getpost"
            body: "*"
        };
    }

    // 点赞or取消点赞
    rpc Attitude(AttitudeReq) returns (AttitudeResp) {
      option (google.api.http) = {
        post: "/ugc-community-http-logic/attitude/attitude"
        body: "*"
      };
    }

    // 获取主题列表
    rpc GetSubjectTabList(GetSubjectTabListRequest) returns (GetSubjectTabListResponse) {
      option (google.api.http) = {
        post: "/ugc-community-http-logic/subject/getsubjecttablist"
        body: "*"
      };
    }

    // 获取话题列表
    rpc GetTopicList(GetTopicListRequest) returns (GetTopicListResponse) {
        option (google.api.http) = {
            post: "/ugc-community-http-logic/topic/list"
        };
    }
}


message BaseRequest {
    string device_id = 1;
    uint32 market_id = 2;
    uint32 client_type = 3;
    uint32 client_version = 4;
}

// 附件
message Attachment {
    // 附件类型
    enum Type {
        TYPE_UNSPECIFIED = 0;
        // 图片
        TYPE_IMAGE = 1;
    }
    // 类型
    Type type = 1;
    // obs key
    string key = 2;
    string content =3;
    string size = 4;  //宽*高
}

// 话题
message TopicInfo {
    // 话题ID
    string id = 1;
    // 话题类型
    uint32 type = 2;
    // 话题名称
    string name = 3;
}


message AigcCommunityPost {
    uint32 role_id = 1;
    uint32 role_type = 2;
    string role_name = 3;
    string role_avatar = 4;
    string role_character = 5;

    repeated ugc_community.AigcCommunityPost.ChatRecord chat_records = 6;
}

message GeneratePublishPostParamRequest {
    BaseRequest base_req = 1;
    // 需要上传的附件列表 不需要传key
    repeated Attachment attachments = 2;
}

message GeneratePublishPostParamResponse {
    // 帖子ID，发布时带上
    string id = 1;
    string obs_app = 2;
    string obs_scope = 3;
    // 带有key的附件列表
    repeated Attachment attachments = 4;
    string obs_token = 5;
}

message PublishPostRequest {
    message Post {
        // 帖子ID
        string id = 1;
        // 帖子类型
        ugc_community.PostType type = 2;
        // 帖子状态
        ugc_community.PostState state = 3;
        // 发布来源
        ugc_community.PostOrigin origin = 4;

        // 内容
        string content = 5;
        // 附件列表
        repeated Attachment attachments = 6;

        // 业务类型
        ugc_community.PostBizType biz_type = 7;
        // 业务数据 角色id放里面
        map<string, string> biz_data = 8;
        
        // 话题ID
        repeated string topic_id_list = 9;

        // 业务序列化数据，取代biz_data
        bytes biz_bytes = 10;
    }

    BaseRequest base_req = 1;
    Post post = 2;
    // 发帖引导任务token
    string task_token = 3;
}

message PublishPostResponse {
}

message DeletePostRequest {
    BaseRequest base_req = 1;
    // 帖子ID
    string id = 2;
}

message DeletePostResponse {
}


message CommentSendRequest{
    BaseRequest base_req = 1;
    // 帖子id
    string post_id = 2;
    // 首评论id
    string root_parent_id = 3;
    // 回复评论id
    string parent_id = 4;
    // 回复内容
    string content = 5;
}

message CommentSendResponse{
}


message CommentFetchRequest{
    BaseRequest base_req = 1;
    // 帖子id
    string post_id = 2;
    // 上一页最后的首次评id，分页使用
    string last_comment_id = 3;
}

message CommentFetchResponse{
    repeated ugc_community_middle.CommentItem comments = 1;
    bool is_load_finish = 2;
}

// 删除评论
message CommentDeleteRequest{
    BaseRequest base_req = 1;
    string comment_id = 2;
}

message CommentDeleteResponse{
}

// ugc中的用户信息
message UserUgcCommunity {
    uint32 uid = 1; // id
    string account = 2; // 账号
    string alias = 3; // 数字账号
    string nickname = 4; // 昵称
    uint32 gender = 5; // 性别
}

enum PostSource {
    SOURCE_AI_DISTRICT_UNSPECIFIED = 0;
    SOURCE_AI_DISTRICT_RECOMMENDATION = 1;      // ai社区推荐流
    SOURCE_AI_DISTRICT_PERSON = 2;          // ai社区个人流
}

// 查看帖子详情
message PostInfo {
    string post_id = 1;
    UserUgcCommunity post_owner = 2;
    ugc_community.PostType post_type = 3;
    string content = 4;
    repeated Attachment attachments = 5;
    int64 post_time = 6; // 发帖时间, unix second
    uint32 comment_count = 7;
    uint32 attitude_count = 8;

    ugc_community.PostOrigin origin = 9; // 发帖来源

    ugc_community.PostBizType biz_type = 10; // 业务类型

    map<string, string> biz_data = 11; // 业务数据 角色id放里面
    ugc_community_middle.CommentItem hot_comment = 12; //热门评论
    bool is_attitude = 13; //是否点赞
    ugc_community.PostState state = 14;  //匿名

    // 帖子关联的话题
    repeated TopicInfo topic_list = 15;

    // 业务序列化数据，取代biz_data
    bytes biz_bytes = 16;
    bool had_followed = 17; // 是否关注发帖人

}

message GetPostReq {
    BaseRequest base_req = 1;
    string post_id = 2;
    PostSource post_source = 3;
}

message GetPostResp {
    PostInfo post_info = 1;
}

message Feed {
    // 帖子
    PostInfo post = 1;

    string feed_id = 2;
}

message GetNewsFeedsReq {
    BaseRequest base_req = 1;

    ugc_community_middle.NewFeedsLoadMore load_more = 2; // 首次拉取不传, 加载更多时原封不动地填入上一次GetNewsFeedsResp中的load_more字段
    uint32 get_mode = 3; //1代表下一页，2代表刷新
    repeated string browse_post_id = 4; //请求列表未曝光帖子列表
    PostSource post_source = 5;

    // 主题ID
    string subject_id = 6;
    // 角色ID
    uint32 role_id = 7;
}


message GetNewsFeedsResp {
    repeated Feed feeds = 1; // feed列表, 数量可能会超过请求指定的count
    ugc_community_middle.NewFeedsLoadMore load_more = 2; // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了
    string footprint = 3; // 推荐trace id
}

enum AttitudeAction {
  ATTITUDE_ACTION_UNSPECIFIED = 0;
  ATTITUDE_ACTION_LIKE = 1; // 点赞
  ATTITUDE_ACTION_DISLIKE = 2; // 取消点赞
}

enum ObjectType {
  OBJECT_TYPE_UNSPECIFIED = 0;
  OBJECT_TYPE_POST = 1; // 帖子
  OBJECT_TYPE_COMMENT = 2; // 评论
}


message AttitudeReq{
    BaseRequest base_req = 1;
    AttitudeAction action = 2; // 操作类型(点赞or取消点赞)AttitudeAction
    ObjectType object_type = 3; // 对象类型(ObjectType)帖子or评论
    string id = 4; // id
}

message AttitudeResp{

}

message GetSubjectTabListRequest{
  BaseRequest base_req = 1;
}

message StickyPostInfo {
  string id = 1;
  string title = 2;
  string icon = 3;
}

// 主题
message SubjectTabInfo {
  // 主题ID
  string id = 1;
  // 主题名称
  string name = 2;
  // 主题下的置顶帖子信息
  repeated StickyPostInfo sticky_post_infos = 3;
}
message GetSubjectTabListResponse{
  repeated SubjectTabInfo subject_infos = 1;
}

message GetTopicListRequest {
    BaseRequest base_req = 1;
    
    // 分页游标，第一页传空
    string cursor = 2;
    // 每页返回数量，上限200
    uint32 limit = 3;
}

message GetTopicListResponse {
    // 下一页请求游标，为空表示到底
    string next_cursor = 1;
    repeated TopicInfo list = 2;
}
