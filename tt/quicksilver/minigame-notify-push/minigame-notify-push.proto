syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/minigame-notify-push";
package minigame_notify_push;

service MiniGameNotifyPush {
  rpc BatchPushPwnKRaceToUser (BatchPushPwnKRaceToUserReq) returns (BatchPushPwnKRaceToUserResp) {}
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchPushPwnKRaceToUserReq {
  repeated uint32 uids = 1;
  PushItem push_Item = 2;
}

message PushItem {
  string title = 1;     // 文案标题
  string content = 2;   // 文案内容
  string icon_url = 3;
  string jump_url = 4;  // 跳转短链
  int64 begin_at = 5;  // 开始时间戳
  int64 expire_at = 6; // 过期时间戳
}

message BatchPushPwnKRaceToUserResp {

}

