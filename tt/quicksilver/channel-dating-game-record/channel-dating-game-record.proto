syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-dating-game-record";

package channel_dating_game_record;

service ChannelDatingGameRecord {
    // 获取佩戴中的铭牌 fellow-svr
    rpc GetNameplateInUse (GetNameplateInUseRequest) returns (GetNameplateInUseResponse);
    
    // 设置佩戴中的铭牌 http
    rpc SetNameplateInUse (SetNameplateInUseRequest) returns (SetNameplateInUseResponse);
    

    // 获取相亲房牵手记录 http
    rpc GetChannelDatingGameRecord (GetChannelDatingGameRecordRequest) returns (GetChannelDatingGameRecordResponse);

    rpc TestSetChannelDatingGameRecord (SetChannelDatingGameRecordRequest) returns (SetChannelDatingGameRecordResponse);
}
 
message GetNameplateInUseRequest {
    uint32 uid = 1;
    repeated uint32 target_uids = 2;
}

message GetNameplateInUseResponse {
    map<uint32, DatingGameRecord> record_map = 1;
}


message SetNameplateInUseRequest {
    uint32 from_uid = 1;
    uint32 target_uid = 2;
    uint32 scene_id = 3;    //场景ID
    string scene_name = 4;
}

message SetNameplateInUseResponse {
}

message GetChannelDatingGameRecordRequest {
    uint32 uid = 1;
    uint32 target_uid = 2;
}

message DatingGameRecord {
    uint32 scene_id = 1;     //场景ID
    string scene_name = 2;   //场景名称
    string unique_nameplate = 3;    //唯一铭牌 
    string multi_nameplate = 4;    //非唯一铭牌 
    string background = 5;   //背景
    uint32 scene_count = 6;  //牵手次数
    bool is_wear = 7;        //是否在穿戴中
    string first_date = 8;   //首次牵手日期
} 

message FirstDatingGameRecord {
    uint32 scene_id = 1;     //场景ID
    string scene_name = 2;   //场景名称
    uint32 channel_id = 3;   //房间ID
    string date = 4;   //日期
}

message GetChannelDatingGameRecordResponse {
    uint32 uid = 1;
    uint32 target_uid = 2;
    repeated DatingGameRecord list = 3;    //牵手场景记录
    DatingGameRecord in_use = 4;           //佩戴中的场景
    FirstDatingGameRecord first_record = 5;  //首次牵手场景
}


message SetChannelDatingGameRecordRequest {
    uint32 uid = 1;
    uint32 target_uid = 2;
    uint32 scene_id = 3; 
    string scene_name = 4;
    uint32 channel_id = 5;        
}


message SetChannelDatingGameRecordResponse {
}

