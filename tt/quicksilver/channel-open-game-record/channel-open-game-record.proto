syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-open-game-record";

package channel_open_game_record;

service ChannelOpenGameRecord {
    rpc BatchGetRecordByUid (BatchGetRecordByUidReq) returns (BatchGetRecordByUidResp) {
    }
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchGetRecordByUidReq {
    repeated uint32 uid_list = 1;
    uint32 gameId = 2;
}

message BatchGetRecordByUidResp {
    map<uint32, GameRecord> game_record_map = 1;
}

message GameRecord {
    uint32 play_games = 1;
    uint32 play_games_win = 2;
    uint32 play_games_break = 3;
}