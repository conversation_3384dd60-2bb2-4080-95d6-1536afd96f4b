syntax = "proto3";

import "game_tmp_channel/game-tmp-channel_.proto";

option go_package = "golang.52tt.com/protocol/services/competition-entrance";

package competition_entrance;

service CompetitionEntrance {
  // 赛事创建/修改
  rpc UpsertCompetition(UpsertCompetitionReq) returns (UpsertCompetitionRsp) {}
  // 赛事列表
  rpc GetCompetitionList(GetCompetitionListReq) returns (GetCompetitionListRsp) {}

  // 赛事入口创建/修改
  rpc UpsertEntrance(UpsertEntranceReq) returns (UpsertEntranceRsp) {}
  // 赛事入口删除
  rpc DelEntrance(DelEntranceReq) returns (DelEntranceRsp) {}
  // 赛事入口列表
  rpc GetEntranceList(GetEntranceListReq) returns (GetEntranceListRsp) {}

  // 生效中的赛事入口列表
  rpc GetActiveEntrance(GetActiveEntranceReq) returns (GetActiveEntranceRsp) {}

  //创建赛事临时房
  rpc CreateGameTmpChannel (CreateGameTmpChannelReq) returns (CreateGameTmpChannelResp) {}
  //更新房间公告
  rpc UpdateAnnouncement (UpdateAnnouncementReq) returns (UpdateAnnouncementResp) {}
  //更新赛事信息
  rpc UpdateContestInfo (UpdateContestInfoReq) returns (UpdateContestInfoResp) {}
  //更新玩家名单
  rpc UpdatePlayer (UpdatePlayerReq) returns (UpdatePlayerResp) {}
  //更新赛事按钮
  rpc UpdateButton (UpdateButtonReq) returns (UpdateButtonResp) {}
  //获取赛事临时房配置
  rpc GetGameTmpChannelCfg (GetGameTmpChannelCfgReq) returns (GetGameTmpChannelCfgResp) {}

  // 创建/修改赛事组件(运营后台)
  rpc UpsertComponent (UpsertComponentReq) returns (UpsertComponentResp){}
  // 删除赛事组件(运营后台)
  rpc DelComponent (DelComponentReq) returns (DelComponentResp){}
  // 获取赛事组件列表(运营后台)
  rpc GetComponentList (GetComponentListReq) returns (GetComponentListResp){}

  // 获取生效中的赛事组件列表
  rpc GetActiveComponentList (GetActiveComponentListReq) returns (GetActiveComponentListResp){}

  // 推送房间赛事通知(提供给赛事服务)
  rpc PushChannelMsg(PushChannelMsgReq) returns (PushChannelMsgResp){}
}

// 赛事配置
message Competition {
  enum App {
    AppNone = 0;
    AppTT = 1;
    AppHuanYou = 2;
  }

  message Link {
    App app = 1;
    string url = 2;
  }

  // 赛事 id
  uint32 id = 1;
  // 更新时间
  uint32 updated_at = 2;
  // 赛事名称
  string name = 3;
  // 平台跳转链接
  repeated Link link_list = 4;
}

message UpsertCompetitionReq {
  Competition competition = 1;
}

message UpsertCompetitionRsp {
}

message GetCompetitionListReq {
  uint32 offset = 1;
  uint32 limit = 2;

  uint32 id = 3;
  string name = 4;
}

message GetCompetitionListRsp {
  repeated Competition list = 1;
  uint32 total = 2;
}

// 赛事入口
message Entrance {
  // 推送人群
  enum CrowdType {
    CrowdTypeNone = 0;
    CrowdTypeAll = 1; // 全部用户
    CrowdTypeGroup = 2; // 根据人群包 id
  }

  // 赛事入口id
  uint32 id = 1;

  // 赛事id
  uint32 competition_id = 2;
  // 赛事名称
  string competition_name = 3;

  // 生效开始时间
  uint32 begin_at = 4;
  // 生效结束时间
  uint32 end_at = 5;

  // 推送人群
  CrowdType crowd_type = 6;
  // 人群包id
  string crowd_group_id = 7;

  // 关联赛事业务
  string biz_id = 8;

  // 优先级
  uint32 priority = 9;

  // 赛事副标题
  string competition_subtitle = 10;
  // 赛事图
  string competition_img = 11;

  // 替换赛事入口副标题
  string entrance_subtitle = 12;
  // 替换赛事入口图
  string entrance_background = 13;

  // 替换赛事入口副标题1
  string entrance_subtitle1 = 14;
  // 替换赛事入口图1
  string entrance_background1 = 15;

  // 替换赛事入口副标题2
  string entrance_subtitle2 = 16;
  // 替换赛事入口图2
  string entrance_background2 = 17;

  // 替换赛事入口副标题3(欢游专用)
  string entrance_subtitle3 = 18;
  // 替换赛事入口图3(欢游专用)
  string entrance_background3 = 19;
  //是否热门推荐
  bool is_hot_rec = 20;
}

message UpsertEntranceReq {
  Entrance entrance = 1;
}

message UpsertEntranceRsp {
}

message DelEntranceReq {
  uint32 id = 1;
}

message DelEntranceRsp {
}

message GetEntranceListReq {
  enum Status {
    StatusNone = 0;
    StatusActive = 1; // 生效中
    StatusInEffective = 2; // 未生效
    StatusExpired = 3; // 已过期
  }

  uint32 offset = 1;
  uint32 limit = 2;

  // 赛事名称
  string competition_name = 3;
  // 配置状态
  Status status = 4;
  // 生效时间 开始
  uint32 begin_at = 5;
  // 生效时间 结束
  uint32 end_at = 6;
}

message GetEntranceListRsp {
  repeated Entrance list = 1;
  uint32 total = 2;
}

message GetActiveEntranceReq {
}

message GetActiveEntranceRsp {
  message Entrance {
    string subtitle = 1;
    string background = 2;

    uint32 priority = 3;
    uint32 updated_at = 11;

    uint32 crowd_type = 4;
    string crowd_group_id = 5;

    string biz_id = 6;

    string competition_name = 7;
    string competition_subtitle = 8;
    string competition_background = 9;

    string subtitle1 = 12;
    string background1 = 13;

    string subtitle2 = 14;
    string background2 = 15;

    string subtitle3 = 16;
    string background3 = 17;

    repeated Competition.Link competition_links = 10;
    //是否热门推荐
    bool is_hot_rec = 18;
  }

  repeated Entrance list = 1;
}

message CreateGameTmpChannelReq {
  string activity_id = 1;                 //赛事活动ID，用于赛事统计
  string channel_name = 2;                //房间名
  uint64 background_id = 3;               //房间背景ID
  uint64 expire_timestamp = 4;            //失效时间，单位秒
  ga.game_tmp_channel.AnnouncementInfo announcement_info = 5; //房间公告
  ga.game_tmp_channel.ContestInfo contest_info = 6;           //赛事信息
  uint32 capacity = 7;                                        //队伍最大人数
  repeated ga.game_tmp_channel.PlayerLabel players = 8;       //玩家信息
  ga.game_tmp_channel.ButtonInfo button_info = 9;             //赛事按钮信息
}

message CreateGameTmpChannelResp {
  uint32 channel_id = 1;
}

message UpdateAnnouncementReq {
  uint32 channel_id = 1;
  ga.game_tmp_channel.AnnouncementInfo announcement_info = 2;
}

message UpdateAnnouncementResp {
}

message UpdateContestInfoReq {
  uint32 channel_id = 1;
  ga.game_tmp_channel.ContestInfo contest_info = 2;
}

message UpdateContestInfoResp {
}

message UpdatePlayerReq {
  uint32 channel_id = 1;
  uint32 capacity = 2; //队伍最大人数
  repeated ga.game_tmp_channel.PlayerLabel players = 3;
}

message UpdatePlayerResp {
}

message UpdateButtonReq {
  uint32 channel_id = 1;
  ga.game_tmp_channel.ButtonInfo button_info = 2;
}

message UpdateButtonResp {
}

message GetGameTmpChannelCfgReq {
  uint32 channel_id = 1;
}

message GetGameTmpChannelCfgResp {
  string activity_id = 1;                               //赛事活动ID，用于赛事统计
  ga.game_tmp_channel.ContestInfo contest_info = 2;     //赛事信息
  uint32 capacity = 3;                                  //队伍最大人数
  repeated ga.game_tmp_channel.PlayerLabel players = 4; //玩家信息
  ga.game_tmp_channel.ButtonInfo button_info = 5;       //赛事按钮信息
}

message Component {
  // 配置唯一id
  string id = 1;
  // 房间tabId
  uint32 tab_id = 2;
  // 赛事id
  uint32 competition_id = 3;
  // 赛事名称
  string competition_name = 4;
  // 入口icon
  string entrance_icon = 5;
  // 是否外显
  bool show_status = 6;
  // 外显名称
  string show_name = 7;
  // 配置生效开始时间， 单位：ms
  int64 begin_at = 8;
  // 生效结束时间， 单位：ms
  int64 end_at = 9;
  // 1-未生效，2-生效中，3-已过期
  uint32 status = 10;
  // 关联赛事业务，用于调用赛事服务那边接口
  string biz_id = 11;
  // 优先级，排序用
  uint32 priority = 12;
}

message ActiveComponent {
  uint32 competition_id = 1;  // 赛事id
  string entrance_icon = 2;   // 入口icon
  string show_name = 3;       // 外显名称
  string biz_id = 4;          // 关联赛事业务，用于调用赛事服务那边接口
  uint32 priority = 5;        // 优先级，排序用
  int64 created_at = 6;       // 创建时间，优先级相同时以最新创建的优先
  // 赛事详情链接
  repeated Competition.Link competition_links = 7;
}

message UpsertComponentReq {
  Component component = 1;
}

message UpsertComponentResp {
}

message DelComponentReq {
  string id = 1;
}

message DelComponentResp {
}

message GetComponentListReq {
  uint32 page = 1; // 页
  uint32 page_size = 2;
}

message GetComponentListResp {
  repeated Component list = 1;
  uint32 total = 2;
}

message GetActiveComponentListReq {
  uint32 tab_id = 1;
}

message GetActiveComponentListResp {
  repeated ActiveComponent list = 1;
}

message PushChannelMsgReq {
  uint32 cid = 1;       // 房间id
  uint32 msg_type = 2;  // 消息类型
  bytes pb_content = 3;  // 消息内容
  string biz_id = 4;    // 关联赛事业务
}

message PushChannelMsgResp {
}