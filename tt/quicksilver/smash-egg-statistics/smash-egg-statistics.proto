syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/smash-egg-statistics";
package smash_egg_statistics;

//兑换记录
message ConversionRecord{
  uint32 conversion_id = 1;
  //  map<uint32, uint32> debrisid_2_num = 2; //需要的碎片ID和数量
  string gift_id = 3;
  uint32 gift_num = 4;
  string gift_name = 5;
  uint32 conversion_type = 6; //兑换类型
  uint32 show_type = 7; //分类类型
  uint32 hold_day = 9; //持有天数

  uint32 uid = 10;
  uint32 target_uid = 11;
  uint32 price = 12;
  uint32 create_at = 13;
  string debrisid_name_num = 14; //需要的碎片名称和数量
}

message GetConversionRecordReq{
  uint32 uid = 1;
  uint32 page = 2;
  uint32 limit = 3;
  uint32 begin_time = 4;
  uint32 end_time = 5;
  uint32 target_uid = 6;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetConversionRecordResp{
  repeated ConversionRecord conversion_record_list = 1;
  uint32 page_count = 2;
  uint32 total = 3;
  uint32 sumPrice = 4;
}

message SmashEggStatistics{
  uint32  statistics_date = 1;          //日期
  float   total_yield_rate = 2;         //总收益率
  float   normal_yield_rate = 3;        //变身前收益率
  float   morph_yield_rate = 4;         //变身后收益率
  float   normal_fee_amount = 5;        //变身前抽奖金额
  float   morph_fee_amount = 6;         //变身后抽奖金额
  int32   n_bingo_quantity = 7;         //N值中奖个数
  float   n_bingo_rate = 8;             //N值中奖概率
  int32   p_bingo_quantity = 9;         //奖池中将个数
  float   p_bingo_rate = 10;            //奖池中奖概率
  int32   n_count = 12;                 //N值个数
  int32   n_people = 13;                //N值用户数
  int64   n_sum = 14;                   //N值总数
  int32   n_avg = 15;                   //N值平均值
  float   normal_yield = 16;            //变身前收益
  float   morph_yield = 17;             //变身后收益
}

message GetYieldStatisticsReq{
  uint32 begin_time = 4;
  uint32 end_time = 5;
}

message GetYieldStatisticsResp{
  repeated SmashEggStatistics smash_egg_statistics = 1;
}

message NStatistics{
  uint32   statistics_date = 1;
  int32    n_count = 2;
  int32    n_people = 3;
  int64    n_sum = 4;
  int32    n_avg = 5;
  string   n_detail = 6;
}

message GetNStatisticsReq{
  uint32 begin_time = 4;
  uint32 end_time = 5;
}

message GetNStatisticsResp{
  int32     n_count = 1;
  int32     n_people = 2;
  int64     n_sum = 3;
  int32     n_avg = 4;
  repeated  uint32 x = 7;
  repeated  uint32 y = 8;
  float    total_avg_yield_rate = 9;         //平均总收益率
  float    normal_avg_yield_rate = 10;        //平均变身前收益率
  float    morph_avg_yield_rate = 11;         //平均变身后收益率
}

service StatisticsServer {

  //  查询砸蛋记录
  /*  rpc GetSmashRecord (GetSmashRecordReq) returns (GetSmashRecordResp){

    }*/

  //  查询兑换记录
  rpc GetConversionRecord (GetConversionRecordReq) returns (GetConversionRecordResp){

  }

  //  查询收益统计
  rpc GetYieldStatistics (GetYieldStatisticsReq) returns (GetYieldStatisticsResp){

  }

  //  查询N值统计
  rpc GetNStatistics (GetNStatisticsReq) returns (GetNStatisticsResp){

  }

}