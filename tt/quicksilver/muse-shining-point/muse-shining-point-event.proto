syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package muse_shining_point.event;
option go_package = "golang.52tt.com/protocol/services/muse-shining-point/event";
import "tt/quicksilver/muse-shining-point/muse-shining-point.proto";

// 闪光点更新全量事件
message ShiningPointUpdateEvent{
  repeated muse_shining_point.shining_point.ShiningPointClassify point_classify_list = 1; // 闪光点分类列表
}

// 用户闪光点更新事件
message UserUpdateShiningPointEvent{
  uint32 uid = 1;
  repeated muse_shining_point.shining_point.ShiningPointInfo point_list = 2; // 闪光点列表
}