syntax = "proto3";


option go_package = "golang.52tt.com/protocol/services/revenue-base-http-logic";
package revenue_base_http_logic;


//营收基础-HTTP服务协议定义

// -------------------- 珍宝馆协议定义 begin ----------------------------------

message GetTreasurePrivilegeHistoryReq{
  uint32 uid = 1;  // 要获取记录的用户uid
}

message GetTreasurePrivilegeHistoryResp{
  repeated TreasurePrivilegeHistory history_list = 1;
}

message TreasurePrivilegeHistory {
  string gift_icon = 1; // 礼物缩略图
  string gift_name = 2; // 礼物名
  uint32 gift_price = 3; // 礼物价格
  uint32 gain_time = 4; // 权限获取时间戳
  uint32 fin_time = 5 ; // 权限到期时间 (截止时间，秒级时间戳)
  uint32 fin_seconds = 6 ; // 权限到期时间（相对时间，秒级）
}

// -------------------- 珍宝馆协议定义 end   ----------------------------------

// -------------------- 用户召回协议定义 begin ----------------------------------
message EmptyResp{}

message SendRecallReq {
  uint32 target_uid = 1; // 对方uid
  uint32 recall_type = 2; // 召回方式，see RecallType
}
message GetRecallPrizeReq {
  uint32 target_uid = 1; // 对方uid
}
message GetRecallListResp {
  UserProfile op_user_info = 1;
  repeated RecallUserInfo list = 2;
}
message RecallUserInfo {
  uint32 uid = 1;
  string account = 2;
  string alias = 3;
  uint32 sex = 4;//性别，0女 1男
  string nickname = 5; // 昵称
  uint32 status = 6; // 状态 see UserRecallStatus
  uint32 fellow_type = 7;// 挚友 FellowType
  string content = 8;// 文案
  uint32 gift_price = 9;// 礼物价值 xx豆
  uint32 gift_cnt = 10;
  string gift_name = 11; // 礼物名称
  string gift_icon_url = 12; // 礼物图标

  string invite_token = 13;
  string remark = 14;//流失用户给邀请方的备注,不为空就是有备注

  string invite_url = 15;
}
message UserProfile {
  uint32 uid = 1;
  string account = 2; //账号
  string nickname = 3; // 昵称
  string account_alias = 4; // 靓号，预留字段，暂时未赋值
  uint32 sex = 5; //用户性别
}

message GetInviteInfoReq {
  string invite_ttid = 1;
  string to_ttid = 2;
  string token = 3;
}
message GetInviteInfoResp {
  UserInfo invite_user_info = 1;
  UserInfo to_user_info = 2;
  repeated GiftInfo gift_list = 3;
  uint32 status = 4;
}
enum UserRecallBindStatus {
  UserRecallBindStatus_Null = 0; //0 没有邀请关系（玩法暂不可参与喔），
  UserRecallBindStatus_NoBind = 1; //1 有邀请，但未绑定，
  UserRecallBindStatus_Bind = 2;  //2 有邀请，绑定的是邀请方，
  UserRecallBindStatus_BindOther = 3; //3 有邀请，绑定的是别人
}

message UserInfo {
  string account = 1;
  string nickname = 2;
  string account_alias = 3; // 靓号
}
message GiftInfo {
  string gift_icon = 1; // 礼物图标
  string gift_name = 2; // 礼物名称
  uint32 gift_price = 3; // 礼物价值  单位T豆
  uint32 gift_cnt = 4; // 个数
  uint32 nob_level = 5; // 贵族体验卡等级，如果有的话
  uint32 expire_day = 6; // 过期天
}


message GetBindResp {
  uint32 bind_uid = 1;    //0 代表没有绑定
  string bind_nick = 2;
  string bind_account = 3;
}

message GetUserInfoReq {
  string ttid = 1;
}

message GetUserInfoResp {
  uint32 uid = 1;
  string nick = 2;
  string account = 3;
}

message BindReq {
  string ttid = 1;
}

// -------------------- 用户召回协议定义 end ----------------------------------

// -------------------- VIP begin ----------------------------------
// 获取用户vip礼包入口信息
message GetUserVipGiftPackageEntranceReq {

}
message GetUserVipGiftPackageEntranceResp {
  uint32 uid = 1;
  uint64 rich_val = 2; // 财富值
  uint32 cur_vip_level = 3; // vip等级
  bool is_condition_met = 4; // 是否满足领取条件
  bool show_red_dot = 5; // 是否显示红点
  bool is_blocked = 6; // 是否被ban
}

// 获取用户vip礼包信息
message GetUserVipGiftPackageInfoReq {

}
message GetUserVipGiftPackageInfoResp {
  uint32 uid = 1;
  uint64 rich_val = 2; // 财富值
  uint32 cur_vip_level = 3; // vip等级
  uint64 vip5_rich_val = 5; // vip5等级所需财富值
  bool is_condition_met = 6; // 是否满足领取条件
  bool is_first_met = 7; // 是否达到条件并进入领取页面（用于展示弹框）
  bool is_received = 8; // 是否已领取
  bool is_received_from_op = 9; // 是否已通过运营手动发放

  // 待领取状态
  repeated VipGiftPackageItem vip_gift_list = 10; // vip礼包礼物列表
  uint64 available_gift_total_price = 11; // 可领取礼物总价值

  // 已领取状态
  repeated VipGiftPackageItem received_gift_list = 12; // 已领取礼物列表
  uint64 received_gift_total_price = 13; // 已领取礼物总价值
  uint64 received_time = 14; // 领取时间
  string confirm_text = 15; // 确认文案
  string username = 16; // tid
  string nickname = 17; // 昵称
}

// 礼包礼物信息
message VipGiftPackageItem {
  uint32 gift_id = 1; // 礼物id
  string gift_name = 2; // 礼物名称
  uint32 gift_count = 3; // 礼物数量
  uint64 gift_price = 4; // 礼物价格
  string gift_icon = 5; // 礼物icon
  uint32 ranking = 6; // 排序
}

// 提交领取礼包
message SubmitReceiveVipGiftPackageReq {
  repeated VipGiftPackageItem gift_list = 1; // 礼物ID+数量
}
message SubmitReceiveVipGiftPackageResp {

}
// -------------------- VIP end ----------------------------------

// -------------------- 神秘人 begin ----------------------------------
message BuyYKWReq{
  string uid = 1;
  string request_id = 2;
  uint32 client_version = 3;
  uint32 client_type = 4;
  string face_auth_token = 5;
  string device_id = 6;
  string face_auth_provider_code = 7;
  uint32 order_type_id = 8; // 订单类型，根据下单类型确认扣费多少，默认以30天为扣费标准兼容
  string face_auth_provider_result_data = 9;
  string face_auth_result_token = 10;
}

message BuyYKWResp{
  uint32 auth_scene = 1;
  string request_id = 2;
  string face_auth_context_json = 3;
}

message YKWConfInfo {
  uint32 price = 1;
}

// 神秘人订单类型信息
message YKWOrderType {
  uint32 order_type_id = 1;    // 订单类型
  uint32 price = 2;            // 订单实际单价
  uint32 effective_time = 3;   // 订单生效时间（单位：秒）
  string discount = 4;         // 订单折扣
  uint32 original_price = 5;   // 订单原价
}

message GetYKWInfoReq{
  string uid = 1;
}

message GetYKWInfoResp{
  string nickname = 1;
  uint32 status = 2;
  uint32 switch = 3;
  uint32 server_time = 4;
  uint32 expire_time = 5;

  YKWConfInfo ykw_conf = 6;
  repeated YKWOrderType ykw_order_types = 7; // 订单类型列表
  int32 nob_level = 8;                       // 神秘人可以开通的等级
  string nob_name = 9;                       // 可开通等级名称
  uint32 is_open_enter_stealth = 10;         // 当前用户是否开启进房隐身
  uint32 can_be_open = 11;                   // 当前用户是否可以进行开通操作
  uint32 enter_notice = 12;                  // 进房提醒开关
}
// -------------------- 神秘人 end ----------------------------------


// -------------------- 佩戴皮肤 begin ----------------------------------
message GetBuyCommodityDataSuccessRequest {
  //uint32 uid = 1;                       // 用户ID
  string big_trade_no = 2;              // 大订单号
}

message GetBuyCommodityDataSuccessResponse {
  repeated string buy_skin_name_list = 1 [deprecated = true]; // 已购买皮肤名列表
  string suit_name = 2;            // 套装名
  uint32 suit_resource_cnt = 3;  // 套装资源数量
  uint32 item_cnt = 4;  // 单品数量
  repeated string skin_name_list = 5 [deprecated = true];  // 当前佩戴的皮肤名列表
  int32 user_sex = 6;  // 用户性别
  repeated uint32  buy_id_list = 7; // 已购买皮肤ID列表
  repeated uint32  skin_id_list = 8; // 当前佩戴的皮肤ID列表
}
// -------------------- 佩戴皮肤 end ----------------------------------


// -------------------- 财富榜 begin ----------------------------------
// from c++ accounthttplogic
message NumericRankReq {
  enum Type {
    TYPE_UNKNOWN = 0; // 未知
    TYPE_CHARM = 1; // 魅力榜
    TYPE_RICH = 2; // 财富榜
  }
  enum TimeSpan {
    TIME_SPAN_UNKNOWN = 0; // 未知
    TIME_SPAN_DAY = 1; // 日
    TIME_SPAN_WEEK = 2; // 周
    TIME_SPAN_MONTH = 3; // 月
  }
  Type type = 1; // 1 魅力榜 2 财富榜
  TimeSpan time_span = 2; // 1 日 2 周 3 月
  uint32 page = 3;
  uint32 page_num = 4;
}
message NumericRankResp {
  message User {
    string account = 1; // username
    bool animate_head_frame = 2; // 是否是动图
    uint32 channel_id = 3;
    uint64 charm_value = 4; // 魅力值/1000
    string extend_json = 5;
    string head_frame = 6; // 头像框url
    string headwear_key = 7; // 头像框url
    uint32 live_status = 8; // 在房状态
    string name = 9; // 昵称
    uint64 rich_value = 10; // 财富值/1000
    uint32 uid = 11;
    uint64 distance_forecast = 12; // 差值
    uint32 rank = 13; // 本次榜单排名，就是上个周期的
    uint32 ranking_forecast = 14; // 预计排名
  }
  repeated User list = 1; // 榜单
  int64 server_time = 2;
  uint32 total = 3;
  User user = 4; // 我的
}
// -------------------- 财富榜 end ----------------------------------