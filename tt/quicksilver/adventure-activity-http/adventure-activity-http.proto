syntax = "proto3";

package adventure_activity_http;
option go_package = "adventure-activity-http";

enum AwardType {
  AWARD_TYPE_UNSPECIFIED = 0; // 未知
  AWARD_TYPE_GIFT = 1; // 礼物奖励
  AWARD_TYPE_DRESS = 2; // 装扮奖励
}

// 奖励信息
message AwardInfo {
  string award_id = 1;   // 卡牌奖励ID，唯一标识一个卡牌奖励
  string award_name = 2; // 奖励名称
  string award_pic = 3;  // 奖励图标
  string award_desc = 4; // 奖励价值描述，比如xx豆，xx天等；不填时不展示
  uint32 award_type = 5; // 奖励类型, see AwardType
  uint32 amount = 6;     // 奖励数量
  int64 exp_time = 7;    // 奖励过期时间, 秒级时间戳
  
  uint32 unit_price = 8; // 奖励单价，“豆”
}

// 关卡信息
message LevelCfg {
  uint32 level_id = 1; // 关卡ID
  string level_name = 2; // 关卡名称
  uint32 max_n_fixed = 3;  // 固定保底值
  repeated AwardInfo card_list = 4; // 关卡卡牌奖励信息列表
  AwardInfo level_award = 5; // 通关奖励信息
}

message UserPlayFile {
  uint32 level_id = 1; // 当前关卡ID
  uint32 current_chance = 2; // 当前抽奖机会数量
  uint32 user_n = 3; // 用户当前的冒险值
  map<string, uint32> card_collection =4; // 用户当前拥有的卡牌信息，key为卡牌ID，value为数量
}

// 购买礼包配置信息
message BuyPropCfg{
    AwardInfo gift_info = 1; // 礼物信息
    repeated uint32 buy_amount_options = 3; // 购买数量选项列表, 单位为个
    uint32 unit_price = 4; // 购买单价, 单位为T豆
    uint32 daily_buy_limit = 5; // 每日购买数量限制
    uint32 single_buy_limit = 6;  // 单次购买数量限制
}

// TOPN 加码奖励信息
message TopNAwardCfg {
  uint32 top_n_limit = 1; // 前N名登顶加码名额
  repeated AwardInfo award_list = 2; // 前n名加码奖励列表
  string popup_top_text = 3; //加码弹窗上方文案，<>内为高亮文案
  string popup_down_text = 4; // 加码弹窗下方文案。
}

// 获取活动信息 url: /tt-revenue-http-logic/adventure-activity/get_curr_game_info
message GetCurrGameInfoRequest { 
  uint32 channel_id = 1; // 如果当前在房间内，则传入房间ID
}

message SimpleUserInfo{
    uint32 uid = 1;
    string nickname = 2;
    string username = 3;
}

message GetCurrGameInfoResponse {
  uint32 activity_id = 1; // 活动ID
  string activity_name = 2; // 活动名称
  int64 begin_time = 3; // 活动开启时间
  int64 end_time = 4; // 活动结束时间
  BuyPropCfg buy_prop_cfg = 5; // 购买礼包礼物配置
  repeated LevelCfg level_list = 6; // 关卡配置列表
  TopNAwardCfg top_n_award_info = 7; // TOPN加码奖励信息
  
  UserPlayFile user_play_file = 8; // 用户的关卡存档
  
  int64 server_ts = 9;  // 服务器时间，秒级时间戳
  
  bool has_access = 10;  // 是否有权限
  string no_permission_text = 11; // 无权限的用户展示文案
  
  SimpleUserInfo user_info = 12;
}

// 购买礼包  url: /tt-revenue-http-logic/adventure-activity/buy_prop
message BuyPropRequest {
  uint32 channel_id = 1; // 如果当前在房间内，则传入房间ID
  uint32 buy_amount = 2; // 购买数量, 单位为个
  
  string face_auth_result_token = 3; // 人脸结果token
}

message BuyPropResponse {
  uint32 final_chance_amount = 1; // 最终数量
  uint64 balance = 2; // T豆余额
  uint32 daily_buy_cnt_remain = 3; //每日剩余的可购买数量
  
  string face_auth_context_json = 4; // 风控人脸上下文信息
}

enum DrawResultType {
  DRAW_RESULT_UNSPECIFIED = 0;     // 无效值
  DRAW_RESULT_CARD_LIGHTED = 1;    // 点亮卡牌
  DRAW_RESULT_LEVEL_COMPLETED = 2; // 通关成功
  DRAW_RESULT_PEAK_REACHED = 3;    // 登顶奖励
  DRAW_RESULT_TOP_N_BOUNS = 4;     // 前n名加码奖励
}

// 抽奖结果
message DrawResult {
  uint32 level_id = 1; // 关卡ID
  repeated AwardInfo award_list = 2; // 奖励列表
  uint32 result_type = 3; // 抽奖结果类型, see LotteryResultType
  int64 create_time = 4; // 创建时间（用户记录使用）

  uint32 ranking = 5; // 加码奖名次
}

// 抽奖 url: /tt-revenue-http-logic/adventure-activity/draw_card
message DrawCardRequest {
  uint32 channel_id = 1; // 如果当前在房间内，则传入房间ID
  uint32 level_id = 2; // 抽奖关卡ID
  uint32 amount = 3; // 抽奖数量, 单位为次
  
  enum Mode{
      MODE_MANUL = 0; // 手动
      MODE_AUTO = 1;  // 自动
  }
  uint32 mode = 4;   // 抽奖模式
}

message DrawCardResponse {
  repeated DrawResult result_list = 1; // 抽奖结果列表，依次展示
  UserPlayFile user_play_file = 2; // 更新后的用户关卡存档
}

// 平台中奖记录 轮播用
message PlatformWinningRecord{
    uint32 uid = 1; // 用户ID
    string nickname = 2; // 获奖用户名
    string username = 3;
    uint32 level_id = 4; // 关卡ID
    AwardInfo award_info = 5; // 奖品信息
    uint32 result_type = 6; // 抽奖结果类型, see LotteryResultType
}

// 获取平台中奖记录 url: /tt-revenue-http-logic/adventure-activity/get_platform_winning_info
message GetPlatformWinningRecordRequest {
  string version = 1; // 版本号,首次请求时传入空字符串，后续请求传入上次返回的版本号
}

message GetPlatformWinningRecordResponse {
  string new_version = 1; // 新版本号
  repeated PlatformWinningRecord record_list = 2; // 平台中奖记录列表，用于轮播展示
  uint32 top_n_remain = 3; // 剩余的加码奖名额
  uint32 top_n_limit = 4;  // topn加码奖数量
  
  uint32 req_duration_sec = 5;    // 下次请求的时间间隔
}

// 获取用户冒险记录 url: /tt-revenue-http-logic/adventure-activity/get_user_record
message GetUserAdventureRecordRequest {
    enum RecordType {
        RECORD_TYPE_UNSPECIFIED = 0; // 无效值
        RECORD_TYPE_CARD_LIGHTED = 2; // 点亮记录
        RECORD_TYPE_COMPLETED = 3; // 其他打卡/登顶记录
    }

    uint32 record_type = 1; // 记录类型, see RecordType
    string offset = 2; // 分页偏移量, 用于分页查询, 首次请求时传入空字符串，后续请求传入上次返回的offset
    uint32 limit = 3; // 分页限制, 每页记录数
}

message GetUserAdventureRecordResponse {
    string next_offset = 1; // 新的分页偏移量, 用于下一次分页查询,当返回的next_offset为空字符串时表示没有更多记录
    repeated DrawResult record_list = 2; // 用户冒险记录
}

// 获取用户T豆余额 url: /tt-revenue-http-logic/adventure-activity/get_user_balance
message GetUserBalanceRequest{}

message GetUserBalanceRespone{
    uint64 balance = 1;   // 用户豆豆余额
    uint32 daily_buy_cnt_remain = 2; //每日剩余的可购买数量
}