syntax = "proto3";

import "tt/quicksilver/extension/options/options.proto";

option go_package = "golang.52tt.com/protocol/services/tt-rev-channel-mode-mgr";
package tt_rev_channel_mode_mgr;

import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";



service ChannelModeMgr {

  option (service.options.service_ext) = {
    service_name: "tt-rev-channel-mode-mgr"
  };

  // 获取房间开启的玩法列表
  rpc GetChannelMode(GetChannelModeReq) returns (GetChannelModeResp) {}

  // 设置房间开启的玩法列表
  rpc SetChannelMode(SetChannelModeReq) returns (SetChannelModeResp) {}

  // 获取房间管理员
  rpc GetChannelOperator(GetChannelOperatorReq) returns (GetChannelOperatorResp) {}

  // T豆消费数据对账
  rpc GetConsumeTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetConsumeOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  rpc GenFinancialFile(ReconcileV2.GenFinancialFileReq) returns (ReconcileV2.GenFinancialFileResp) {}
  // 获取订单明细
  rpc GetOrderLogByOrderIds(GetOrderLogByOrderIdsReq) returns (GetOrderLogByOrderIdsResp) {}

  // 花费T豆购买身份/发言次数
  rpc BuyWerwolfItem(BuyWerwolfItemReq) returns (BuyWerwolfItemResp) {}

  /* ================运营后台相关================ */
  // 获取房间玩法入口
  rpc BatchGetChannelModeEntry (BatchGetChannelModeEntryReq) returns (BatchGetChannelModeEntryResp) {}

  // 管理房间玩法入口
  rpc BatchOperateChannelMode (BatchOperateChannelModeReq) returns (BatchOperateChannelModeResp) {}
  
  // 获取玩法的房间列表
  rpc BatchGetChannelIdByLayoutType (BatchGetChannelIdByLayoutTypeReq) returns (BatchGetChannelIdByLayoutTypeResp) {}
}




// 获取房间玩法列表状态
message GetChannelModeReq {
    uint32 channel_id = 1;
}


message ChannelScheme  {
    uint32 scheme_id = 1;       // ID  自增
    string scheme_name = 2;     // 名称
    uint32 layout_type = 3;     // 枚举 see channel-scheme_.proto SchemeLayoutType
    string icon_select = 4;      //  选中状态ICON
    string icon_un_select = 5;   //  非选中状态ICON
    bool is_open = 6;           // 当前是否开始了该玩法
    string pc_icon = 7;         //  PC ICON
}

// 获取房间玩法列表状态应
message GetChannelModeResp {
    uint32 channel_id = 1;
    repeated ChannelScheme schemes = 2;
}


// 设置房间当前玩法
message SetChannelModeReq {
    uint32 channel_id = 1;
    uint32 scheme_id = 2;  //ChannelScheme  scheme_id
}

message SetChannelModeResp {
}

message SwitchChannelMode {
    uint32 scheme_id = 1;       // ID
    bool  is_open = 2;          // 是否开启
    string desc = 3;            // 切换玩法提示
}


message GetChannelOperatorReq {
    uint32 channel_id = 1;
}

message GetChannelOperatorResp {
    uint32 channel_id = 1;
    uint32 operator = 2;   // 主持人 uid
}

/* =================运营后台相关================ */
message BatchGetChannelModeEntryReq {
    repeated uint32 channel_id = 1;
    uint32 layout_type = 2;  //ChannelScheme_.proto  schemeLayoutType
}


message BatchGetChannelModeEntryResp {
   repeated uint32 has_entry_channel = 1; // 返回有权限的channelId
}

enum OperationType {
    UNKNOWN = 0;
    OPEN = 1;
    CLOSE = 2;
}

message BatchOperateChannelModeReq {
    repeated uint32 cid_list = 1;
    uint32 layout_type = 2;
    OperationType operation_type = 3;
}

message BatchOperateChannelModeResp {
}

message BuyWerwolfItemReq {
    uint32 channel_id = 1;
    string open_id = 2;    //open_id
    string set_id = 3;     //当前游戏场次
    uint32 price = 4;      //T豆价格
    uint32 item_id = 5;    //物品ID
    string item_name = 6;  //物品名称 eg:狼人/预言家/发言机会
    uint32 game_id = 7;    //game_id
}

message ErrorInfo {
    int32 code     = 1;
    string msg = 2;
 }

message BuyWerwolfItemResp {
    string order_id = 1; //冻结T豆成功的订单号
    ErrorInfo error_info = 2; //错误信息
}

message GetWerwolfOrderSummaryReq {
    uint32 cid_list = 1;
    uint32 layout_type = 2;
}


message WerwolfOrderLog
{
    uint32 from_uid = 1;
    uint32 target_uid = 2;
    uint32 change_score = 3;  //产生的积分
    string order_id = 4;
    uint32 create_time = 5;
    string deal_token = 6; //校验信息
    uint32 channel_id = 7; 
    uint32 price = 8;        //消耗T豆 
}

//(对账用) 根据orderId获取订单
message GetOrderLogByOrderIdsReq {
    repeated string order_id_list = 1;
}

message GetOrderLogByOrderIdsResp {
    repeated WerwolfOrderLog order_log_list = 1;
}

message BatchGetChannelIdByLayoutTypeReq {
    uint32 layout_type = 1;
}

message BatchGetChannelIdByLayoutTypeResp {
    repeated uint32 channel_id = 1;
}


