syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/user-visitor-record";
package user_visitor_record;

service UserVisitorRecord {
    rpc ReportUserVisitorRecord (ReportUserVisitorRecordReq) returns (ReportUserVisitorRecordResp) {
    }
    rpc GetUserVisitorRecordList (GetUserVisitorRecordListReq) returns (GetUserVisitorRecordListResp) {
    }
    rpc GetUserBeVisitorRecordList (GetUserBeVisitorRecordListReq) returns (GetUserBeVisitorRecordListResp) {
    }
    rpc GetUserBeVisitorRecordCount (GetUserBeVisitorRecordCountReq) returns (GetUserBeVisitorRecordCountResp) {
    }
    rpc GetAllTaskStatus (GetAllTaskStatusReq) returns (GetAllTaskStatusResp) {
    }
    rpc SetTaskStatus (SetTaskStatusReq) returns (SetTaskStatusResp) {
    }
    rpc UpsertTask (UpsertTaskReq) returns (UpsertTaskResp) {
    }
    rpc UploadAlbumsNotice (UploadAlbumsNoticeReq) returns (UploadAlbumsNoticeResp) {
    }
    // 设置用户是否显示访客数
    rpc SetShowUserBeVisitorRecordCount (SetShowUserBeVisitorRecordCountReq) returns (SetShowUserBeVisitorRecordCountResp) {
    }
    // 获取用户是否显示访客数
    rpc GetShowUserBeVisitorRecordCount (GetShowUserBeVisitorRecordCountReq) returns (GetShowUserBeVisitorRecordCountResp) {
    }
    // 获取用户设置的隐藏列表
    rpc GetHideList (GetHideListReq) returns (GetHideListResp) {
    }
    // 获取用户对其他用户的隐藏设置
    rpc GetHideStatusByUid (GetHideStatusByUidReq) returns (GetHideStatusByUidResp) {
    }
    // 设置用户对其他用户的隐藏
    rpc SetHideStatusByUid (SetHideStatusByUidReq) returns (SetHideStatusByUidResp) {
    }
}

// 访问记录上报接口，支持批量上报
message ReportUserVisitorRecordReq {
    uint32 uid = 1;
    map<uint32, uint32> report_map = 2;
}
message ReportUserVisitorRecordResp {
}

// 访问记录
message Record {
    uint32 uid = 1;
    string name = 2;
    string acoount = 3;
    uint32 sex = 4;
    uint32 count = 5;
    uint32 update_time = 6;
    uint32 super_player_level = 7;
    bool hide = 8;

    // 文案，非会员是用户显示的文案
    string tips_msg = 9;
    string highlight_msg = 10;
    string btn_msg = 11;

    bool is_year_member = 12; //是否年费会员
}
// 获取访问记录列表
message GetUserVisitorRecordListReq {
    uint32 uid = 1;
    uint32 offset = 2;
    uint32 limit = 3;
    uint32 client_ip = 4;
    uint32 client_type = 5;
    uint32 client_version = 6;
}
message GetUserVisitorRecordListResp {
    repeated Record records = 1;
    uint32 show_count = 2;
}

// 获取被访问记录列表
message GetUserBeVisitorRecordListReq {
    uint32 uid = 1;
    uint32 offset = 2;
    uint32 limit = 3;
    uint32 client_ip = 4;
    uint32 client_type = 5;
    uint32 client_version = 6;
}
message GetUserBeVisitorRecordListResp {
    repeated Record records = 1;
    uint32 show_count = 2;
    // 是否到达底部
    bool is_end = 3;
}

// 获取被访问记录数
message GetUserBeVisitorRecordCountReq {
    uint32 uid = 1;
}
message GetUserBeVisitorRecordCountResp {
    uint32 count = 1;
    string growth = 2;
    bool show = 3;
}

// 获取任务信息
message Task {
    uint32 id = 1;
    string head_url = 2;
    string name = 3;
    string desc = 4;
    bool success = 5;
    string goto_url = 6;
}
// 获取任务状态
message GetAllTaskStatusReq {
    uint32 uid = 1;
}
message GetAllTaskStatusResp {
    repeated Task tasks = 1;
}

// 设置任务状态
message SetTaskStatusReq {
    uint32 uid = 1;
    uint32 id = 2;
    bool success = 3;
}
message SetTaskStatusResp {
}

// 修改或添加任务
message UpsertTaskReq {
    uint32 id = 1;
    string head_url = 2;
    string name = 3;
    string desc = 4;
    string goto_url = 5;
}
message UpsertTaskResp {
}

// 图片上传回调
message UploadAlbumsNoticeReq {
    uint32 uid = 1;
    uint32 count = 2;
}
message UploadAlbumsNoticeResp {
}

// 设置用户是否显示访客数
message SetShowUserBeVisitorRecordCountReq {
    uint32 uid = 1;
    bool show = 2;
}

message SetShowUserBeVisitorRecordCountResp {
}

// 获取用户是否显示访客数
message GetShowUserBeVisitorRecordCountReq {
    uint32 uid = 1;
}

message GetShowUserBeVisitorRecordCountResp {
    bool show = 1;
}

// 获取用户是否显示访客数
message GetHideListReq {
    uint32 uid = 1;
    uint32 offset = 2;
    uint32 limit = 3;
}

message GetHideListResp {
    repeated UserVisitorRecordUser list = 1;
}

message UserVisitorRecordUser {
    uint32 uid = 1;
    string name = 2;
    string account = 3;
    uint32 sex = 4;
}

// 获取用户是否显示访客数
message GetHideStatusByUidReq {
    uint32 uid = 1;
    uint32 other_uid = 2;
}

message GetHideStatusByUidResp {
    bool hide = 1;
}

// 获取用户是否显示访客数
message SetHideStatusByUidReq {
    uint32 uid = 1;
    repeated uint32 other_uid_list = 2;
    bool hide = 3;
    bool is_all = 4;
}

message SetHideStatusByUidResp {
}

