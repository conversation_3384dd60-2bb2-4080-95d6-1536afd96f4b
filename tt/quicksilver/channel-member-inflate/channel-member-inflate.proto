syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channelmemberinflate";
package channelmemberinflate;


service ChannelMemberInflate {
    /* UGC 房间机器人数量 */
    rpc BatGetUGCChannelMemberInflateSize (BatGetUGCChannelMemberInflateSizeReq) returns (BatGetUGCChannelMemberInflateSizeResp) { }
    rpc GetChannelRobotList(GetChannelRobotListReq) returns (GetChannelRobotListResp) {}
}

message BatGetUGCChannelMemberInflateSizeReq {
    repeated uint32 channel_id_list = 1;
}
message ChannelMemberInflateInfo{
    bool is_inflate = 1; /* 是否启用灌水 true 是 */
    uint32 member_size = 2; /* 房间人数 真实人数或者灌水后的人数 */
}
message BatGetUGCChannelMemberInflateSizeResp {
    map<uint32,ChannelMemberInflateInfo> channel_id_member_size = 1; /* 灌水后房间人数 */
}

message GetChannelRobotListReq {
    uint32 channel_id = 1;
}

message GetChannelRobotListResp {
    repeated uint32 uid_list = 1;
}
