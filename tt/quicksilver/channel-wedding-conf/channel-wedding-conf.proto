syntax = "proto3";

package channel_wedding_conf;
import "tt/quicksilver/extension/options/options.proto";
option go_package = "golang.52tt.com/protocol/services/channel-wedding-conf";

service ChannelWeddingConf {
  option (service.options.service_ext) = {
    service_name: "channel-wedding-conf"
  };

  // 获取婚礼主题配置
  rpc GetThemeCfg(GetThemeCfgReq) returns (GetThemeCfgResp) {}

  // 获取婚礼主题配置列表
  rpc GetThemeCfgList(GetThemeCfgListReq) returns (GetThemeCfgListResp) {}

  // 获取全量的婚礼榜单背景资源
    rpc GetAllWeddingRankBackground(GetAllWeddingRankBackgroundReq) returns (GetAllWeddingRankBackgroundResp) {}

  // 获取婚礼主题的奖励配置
    rpc GetThemeFinishedAwardCfg(GetThemeFinishedAwardCfgReq) returns (GetThemeFinishedAwardCfgResp) {}
}

// 获取婚礼主题配置
message GetThemeCfgReq {
  uint32 theme_id = 1;
}

message GetThemeCfgResp {
  ThemeCfg theme_cfg = 1;
}

// 获取婚礼主题配置列表
message GetThemeCfgListReq {
  bool show_hide = 4; // 是否显示
}

message GetThemeCfgListResp {
  repeated ThemeCfg theme_cfg_list = 1;
  string theme_title_selected_icon = 2;  // 选中图标
  string theme_title_bg_icon = 3;  // 主题标题背景图标
}

// 主题配置
message ThemeCfg {
  uint32 theme_id = 1;
  string theme_name = 2;
  WeddingPriceInfo price_info = 3;
  ResourceCfg theme_room_resource = 4;  // 主题房间资源
  repeated WeddingSceneCfg scene_cfg_list = 5;  // 场景配置
  repeated ThemeLevelCfg theme_level_cfg_list = 6;  // 主题等级配置
  WeddingPreviewCfg wedding_preview_cfg = 7;  // 婚礼预告配置
  ResourceCfg memorial_video_resource = 8;  // 纪念视频资源
  ChairGameResourceCfg chair_res_cfg = 9;  // 抢椅子游戏资源配置
  string selected_theme_title_icon = 10;  // 选中时主题标题图标
  string unselected_theme_title_icon = 11;  // 未选中时主题标题图标
  string example_photo = 12;  // [婚礼大屏定制] 示例图片
  string theme_background = 13;  // 背景图片
  string theme_preview_text = 14;  // 左上角固定文案
  string reward_info_desc = 21; // 奖励信息描述
  repeated FinishWeddingAward reward_info_list = 15;
  string wedding_hall_coming_background = 16; // 仪式大厅未开始的背景图片
  string mail_lady_left_bg_icon = 17;  // 信件女在左边背景图标
  string mail_lady_right_bg_icon = 18;  // 信件女在右边背景图标
  string theme_icon = 19;  // 主题icon
  bool is_deleted = 20; // 是否已删除
  repeated uint32 wedding_gift_ids = 22; // 婚礼主题的礼物id列表
  string pre_progress_panel_resource_url = 23; // 婚礼前置流程面板资源
  string pre_progress_panel_resource_md5 = 24; // 婚礼前置流程面板资源md5
  ResourceCfg mvp_settlement_resource = 25; // MVP结算资源
  uint32 pre_progress_present_target_val = 26; // 婚礼前置流程目标礼物值
  map<uint32, uint32> pre_progress_clothes_extra_duration_cfg = 27; // 婚礼前置流程的服装额外时长配置 giftVal->extra_duration
  string pre_progress_panel_resource_url_pc = 28; // 婚礼前置流程面板资源, pc资源不兼容, 独立是用新字段
  string pre_progress_panel_resource_md5_pc = 29; // 婚礼前置流程面板资源md5
  string dress_preview_newcomers_desc = 30; // 新人服装预览文案 婚礼服装接受板块的新人服装描述文案
  string dress_preview_guest_desc = 31; // 嘉宾服装预览文案 婚礼服装接受板块的伴郎伴娘服装描述文案
  repeated ThemeStageCfg stage_cfg_list = 32; // 婚礼主题阶段配置
}

message ThemeStageCfg {
  uint32 stage = 1; // 阶段
  string stage_name = 2; // 阶段名称
  bool chair_game_enable = 3; // 是否支持抢椅子游戏
}

enum WeddingAwardType {
  WEDDING_AWARD_TYPE_UNSPECIFIED = 0;       // 无效值
  WEDDING_AWARD_TYPE_HEADWEAR= 1;           // 麦位框
  WEDDING_AWARD_TYPE_VA_FOLLOW_CHANNEL = 2; // 虚拟形象双人进房特效
  WEDDING_AWARD_TYPE_VA_BACKGROUND = 3;     // 虚拟形象背景
  WEDDING_AWARD_TYPE_FELLOW_LIGATURE = 4;   // 挚友连线
}

message AwardInfo {
    uint32 award_type = 1;      // 奖励类型
    string award_id = 2;        // 奖励ID,当奖励不区分性别时，award_id与award_id_female相等; 当奖励区分性别时，代表男性奖励id
    string award_id_female = 3; // 奖励ID-女
    uint32 amount = 4;          // 奖励数量, 装扮奖励时表示天数（挚友连线的天数由活动配置控制)
    uint32 level = 5;   // 婚礼等级
    string lv_name = 6; // 婚礼等级名称
}

message FinishWeddingAward {
  ResourceCfg award_animation = 1;  // 动画资源
  string top_text = 2; // 顶部文案
  string bottom_text = 3; // 底部文案
  AwardInfo award_info = 4[deprecated=true]; // 奖励信息
  repeated AwardInfo award_list = 5; // 奖励信息
}


// 婚礼场景动画枚举
enum WeddingScene {
  WEDDING_SCENE_UNSPECIFIED = 0;
  WEDDING_SCENE_BRIDE_GROOM_ENTER = 1;  // 新人进场
  WEDDING_SCENE_EXCHANGE_RING = 2;      // 交换戒指
  WEDDING_SCENE_HIGHLIGHT = 3;          // 高光时刻
  WEDDING_SCENE_GROUP_PHOTO = 4;        // 合影留恋
}

message ResourceCfg {
  uint32 resource_type = 1; // see channel_wedding_logic.proto WeddingAnimationType
  string resource_url = 2;
  string resource_md5 = 3;
  string resource_png = 4;
}

// 婚礼场景动画配置
message WeddingSceneCfg {
  uint32 scene = 1;           // 场景, see WeddingScene
  ResourceCfg resource = 2;  // 场景动画资源
  repeated WeddingSceneBoneCfg bone_cfg_list = 3;  // 骨骼配置
  string scene_icon = 4;  // 场景图标
  string clip_im_small_pic = 5;  // 场景片段im小图
  string clip_default_pic = 6;   // 沉淀场景片段缺省图
}

message WeddingScenePreview {
  ResourceCfg scene_animation = 1;  // 场景动画
  string small_icon = 2;  // 小图标
  ResourceCfg zoom_animation = 3; // 放大动画
}

message WeddingSceneBoneCfg {
  uint32 level = 1; // 等级 see channel_wedding_logic.proto WeddingLevelType
  uint32 seq_index = 2; // 分镜顺序号
  string animation_name = 3;// 动画名称
  uint32 bone_id = 4;  // 骨骼资源ID
  uint32 base_bone_id = 5;  // 基础骨骼资源ID
  repeated uint32 cp_item_id_list = 6; // cp皮肤物品ID列表
}

message WeddingPriceInfo {
  uint32 price = 1;  // 价格
  uint32 price_type = 2;  // 价格类型, see channel_wedding_logic.proto WeddingPriceType
  repeated WeddingPriceItem normal_time_price = 3; // 常规时段价格
  repeated WeddingPriceItem hot_time_price = 4; // 热门时段价格
}

message WeddingPriceItem {
  uint32 price = 1; // 价格
  uint32 gift_id = 2; // 礼物id
}

message ThemeLevelCfg {
  uint32 level = 1; // see channel_wedding_logic.proto WeddingLevelType
  string room_background_picture = 2;  // 背景资源图片
  string room_background_mp4_url = 3;  // 背景mp4
  map<uint32, GuestDressCfg> guest_dress_cfg_map = 4;  // 嘉宾服装配置
  WeddingScenePreview wedding_preview = 5; // 婚礼预览配置
  string wedding_hall_going_background = 6; // 仪式大厅进行中的背景图片
  string certificate_pic = 7; // 结婚证图片
  uint32 present_id = 8; // 手捧花礼物id
  uint32 present_day = 9; // 手捧花礼物天数
  string special_background_picture = 10;  // 结婚宣言、互换戒指 需要特殊的背景资源图片
  string special_background_mp4_url = 11;  // 背景mp4
  string fellow_space_wedding_element = 12; // 挚友空间婚礼元素
  string fellow_space_wedding_background = 13; // 挚友空间婚礼背景
  string fellow_house_space_wedding_background = 14; // 挚友小屋空间婚礼背景
  string fellow_space_wedding_color = 15; // 挚友空间婚礼颜色
  string fellow_space_wedding_certificate = 16; // 结婚证书
  uint32 buy_wedding_present_id = 17;          // 购买婚礼方T豆礼物id
}

// 抢椅子 椅子资源配置
message ChairGameResourceCfg {
  string chair_pic = 1;  // 椅子切图
  uint32 sitting_pose_female_id = 2;  // 坐姿ID-女
  uint32 sitting_pose_male_id = 3;    // 坐姿ID-男

  uint32 standby_female_id = 4; // 待机女性物品ID
  uint32 standby_male_id = 5;   // 待机男性物品ID
  repeated uint32 fail_female_ids = 6;    // 女-失败皮肤物品id列表
  repeated uint32 fail_male_ids = 7;     // 男
}

// 嘉宾服装配置
message GuestDressCfg {
  string dress_text = 1; //  服装文案
  GuestSuitCfg suit_cfg = 2;   // 套装配置
  ResourceCfg wedding_dress = 3;
  string small_icon = 4;
  string clothes_upgrade_popup_png = 5; // 服装升级弹窗图片
  string preview_page_dress_text = 6; // 预览页服装文案
}

// 套装配置
message GuestSuitCfg {
  string name = 1;  // 服装名称
  string icon = 2;  // 服装icon
  repeated uint32 item_ids = 3;  // 服装物品id列表
  uint32 duration_sec = 4;  // 时长,单位秒
}

// 婚礼预告配置
message WeddingPreviewCfg {
  ResourceCfg resource = 1;
  uint32 cp_bone_id = 2;  // 双人骨骼资源id
  repeated uint32 item_ids = 3;  // 皮肤物品id列表
  uint32 base_cp_bone_id = 4;  // 基础骨骼资源id
}

message WeddingRankBg {
    uint32 lv = 1;
    string bg_url = 2; // 背景图
}

message WeddingRankBgList {
    repeated WeddingRankBg wedding_rank_bg_list = 1; // 婚礼榜单背景资源
}

message GetAllWeddingRankBackgroundReq{
}

message GetAllWeddingRankBackgroundResp{
    map<uint32, WeddingRankBgList> wedding_rank_bg_map = 1; // 婚礼榜单背景资源
}

message GetThemeFinishedAwardCfgReq {
  uint32 theme_id = 1;
}


message FinishWeddingAwardList {
    uint32 theme_id = 1; // 婚礼主题ID
    repeated FinishWeddingAward award_list = 2; // 婚礼主题的奖励配置
}
message GetThemeFinishedAwardCfgResp {
  map<uint32, FinishWeddingAwardList> theme_finished_award_map = 1; // 婚礼主题的奖励配置
}