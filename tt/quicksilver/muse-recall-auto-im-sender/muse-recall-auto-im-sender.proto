syntax = "proto3";

package muse_recall_auto_im_sender;

option go_package = "golang.52tt.com/protocol/services/muse-recall-auto-im-sender";

service MuseRecallAutoImSender {
  rpc AddNonMicUser(AddNonMicUserRequest) returns (AddNonMicUserResponse) {}
  rpc DelNonMicUser(DelNonMicUserRequest) returns (DelNonMicUserResponse) {}
  rpc SetUserSimilarUsers(SetUserSimilarUsersRequest) returns (SetUserSimilarUsersResponse) {}
  rpc UserDetail(UserDetailRequest) returns (UserDetailResponse) {}
  rpc HeartbeatDetail(HeartbeatDetailRequest) returns (HeartbeatDetailResponse) {}
  rpc SendText(SendTextRequest) returns (SendTextResponse) {}
  rpc ClearUserRecord(ClearUserRecordRequest) returns (ClearUserRecordResponse) {}
}

message AddNonMicUserRequest {
  uint32 uid = 1;
}

message AddNonMicUserResponse {
}

message DelNonMicUserRequest {
  uint32 uid = 1;
}

message DelNonMicUserResponse {
}

message SetUserSimilarUsersRequest {
  uint32 uid = 1;
  string similar_type = 2;
  message SimilarUser {
    uint32 uid = 1;
    float score = 2;
  }
  repeated SimilarUser similar_uids = 3;
}

message SetUserSimilarUsersResponse {
}

message UserDetailRequest {
  uint32 uid = 1;
}

message UserDetailResponse {
  string reg_time = 1;
  repeated string today_send_list = 2;
  repeated string today_receive_list = 3;
  repeated string last_7_days_send_list = 4;
  int32 cron_step = 5;
  string cron_trigger_time = 6;
  message SimilarUser {
    uint32 uid = 1;
    float score = 2;
  }
  repeated SimilarUser similar_uids = 7;
}

message HeartbeatDetailRequest {
}

message HeartbeatDetailResponse {
  message Heartbeat {
    uint32 uid = 1;
    string last_heartbeat_time = 2;
  }
  repeated Heartbeat heartbeats = 1;
}

message SendTextRequest {
  uint32 sender = 1;
  uint32 receiver = 2;
}

message SendTextResponse {
}

message ClearUserRecordRequest {
  uint32 uid = 1;
}

message ClearUserRecordResponse {
}