syntax = "proto3";

package channel_wedding_minigame;
import "tt/quicksilver/extension/options/options.proto";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";
option go_package = "golang.52tt.com/protocol/services/channel-wedding-minigame";

service ChannelWeddingMinigame {
    option (service.options.service_ext) = {
        service_name: "channel-wedding-minigame"
      };

    // 获取用户报名列表
    rpc GetChairGameApplyList(GetChairGameApplyListRequest) returns (GetChairGameApplyListResponse) {}

    // 用户报名/取消报名
    rpc ApplyToJoinChairGame(ApplyToJoinChairGameRequest) returns (ApplyToJoinChairGameResponse) {}

    // 用户获取抢椅子游戏信息
    rpc GetChairGameInfo(GetChairGameInfoRequest) returns (GetChairGameInfoResponse) {}

    // 用户抢椅子
    rpc GrabChair(GrabChairRequest) returns (GrabChairResponse) {}

    // 主持人开启一句新游戏
    rpc StartChairGame(StartChairGameRequest) returns (StartChairGameResponse) {}

    // 主持人点击进入下一轮
    rpc SetChairGameToNextRound(SetChairGameToNextRoundRequest) returns (SetChairGameToNextRoundResponse) {}

    // 主持人点击开始游戏
    rpc StartGrabChair(StartGrabChairRequest) returns (StartGrabChairResponse) {}

    // 开启一局新游戏precheck
    rpc StartNewGamePreCheck(StartChairGameRequest) returns (StartChairGameResponse) {}

    // 新人设置游戏奖励
    rpc SetChairGameReward(SetChairGameRewardRequest) returns (SetChairGameRewardResponse) {}
    // 获取游戏奖励
    rpc GetChairGameReward(GetChairGameRewardRequest) returns (GetChairGameRewardResponse) {}

    // 获取房间内进行游戏中的用户列表
    rpc GetCurChairGamePlayers(GetCurChairGamePlayersRequest) returns (GetCurChairGamePlayersResponse) {}

    // 强行关闭游戏
    rpc ForceCloseChairGame(ForceCloseChairGameRequest) returns (ForceCloseChairGameResponse) {}

    // 手动推送当前游戏状态（以处理游戏结束推送丢失的状态）
    rpc PushCurChairGameInfo(PushCurChairGameInfoReq) returns (PushCurChairGameInfoResp) {}

    // 批量查询房内是否正在进行抢椅子游戏
    rpc BatGetIfChannelInChairGame(BatGetIfChannelInChairGameReq) returns (BatGetIfChannelInChairGameResp) {}
    

    /*********对账接口**********/

    // T豆对账
    rpc GetTBeanTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetTBeanOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

    // 奖励数据对账
    rpc GetAwardTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetAwardOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
    
    rpc GetAwardPackageOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}  // 废弃
    rpc GetAwardPackageTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}  // 废弃

    // 普通礼物架礼物补单接口
    rpc ReissueAward(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}
}

message BatGetIfChannelInChairGameReq{
    repeated uint32 channel_ids = 1;
}

message BatGetIfChannelInChairGameResp{
    repeated uint32 gaming_cid_list = 1;  // 返回正在抢椅子的房间id列表
}

message PushCurChairGameInfoReq{
    uint32 channel_id = 1;
    uint32 game_id = 2;
}

message PushCurChairGameInfoResp{
}

message ForceCloseChairGameRequest{
    uint32 channel_id = 1;
    uint32 wedding_id = 2;
}

message ForceCloseChairGameResponse{
}

message GetCurChairGamePlayersRequest {
    uint32 channel_id = 1;
}

message GetCurChairGamePlayersResponse {
    repeated PlayerInfo players = 1; // 正在参与游戏的用户
}

message SetChairGameRewardRequest{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 wedding_id = 3;
    ChairGameRewardInfo reward = 4; // 游戏奖励
}

message SetChairGameRewardResponse{
}

// 获取游戏奖励
message GetChairGameRewardRequest{
    uint32 channel_id = 1;
    uint32 wedding_id = 2;
}

message GetChairGameRewardResponse{
    repeated ChairGameRewardInfo reward_list = 1; // 游戏奖励
    ChairGameRewardSetting reward_setting = 2; // 游戏奖励设置
    uint32 sponsor_uid = 3; // 设置奖励用户uid
}


message ChairGameRewardSetting{
    repeated uint32 gift_type = 1;  // 支持配置的礼物类型，see ga_base.proto PresentTagType
    bool support_magic_gift = 2; // 是否支持幸运礼物

    uint32 price_limit = 3; // 价格限制，单位T豆
}


// 申请参加
message ApplyToJoinChairGameRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 wedding_id = 3;
    bool is_cancel = 4;    // 0-申请，1-取消
  }
  
  message ApplyToJoinChairGameResponse {
  }
  
  // 获取报名列表
  message GetChairGameApplyListRequest{
      uint32 channel_id = 1;
      uint32 wedding_id = 3;
  }
  
  message GetChairGameApplyListResponse{
      repeated uint32 apply_user_list = 1;  // 按报名先后顺序
      repeated ChairGameRewardInfo reward_list = 2; // 游戏奖励
  }

  // 抢椅子奖励信息
message ChairGameRewardInfo
{
    string icon = 1;        // 奖励图标
    string name = 2;        // 奖励名称
    uint32 value = 3;       // 奖励价值
    string reward_unit = 4; // 奖励单位 

    uint32 amount = 5;      // 数量下标

    // 以下三个字段[6,7,8]仅用户新人设置奖励时使用
    uint32 gift_id = 6;     // 礼物id
    uint32 gift_type = 7;   // 礼物类型 // see ga_base.proto PresentTagType
    bool is_magic_gift = 8; // 是否是幸运礼物
    uint32 price_type = 9;  // 价格类型 // see PresentPriceType 1-红钻，2-T豆
}

// 游戏进程
message ChairGameProgress
{
    uint32 game_id = 1;
    uint32 cur_round = 2;   // 游戏阶段, 第cur_round轮
    uint32 chair_num = 3;   // 本轮的椅子数量；当chair_num == 1时，该轮为决赛局
    uint32 round_status = 4; // 游戏状态,see ChairRoundStatus
    bool show_round_tip = 5; // 是否显示轮次提示动画
    
    // 本轮参与用户
    repeated uint32 round_palyer_uids = 6; // 参与本轮抢椅子的用户列表
    repeated uint32 round_winner_uids = 7;  // 本轮获胜者uid列表，列表排序表示抢到椅子的先后顺序
  
    uint32 next_round_chair_num = 8; // 下一轮的椅子数量
    int64 server_time_ms = 9;  // 服务器时间,单位毫秒
    int64 host_start_but_duration = 10; // 主持人点击开始游戏按钮后，游戏开始的倒计时时长，单位秒
    int64 host_button_end_ts = 11; // 主持人点击开始游戏按钮的时间戳，单位秒
}

message ChairGameInfo {
    uint32 game_id = 1;
    bool show_game_begin_anim = 2; // 是否显示游戏即将开始动画
    ChairGameProgress game_progress = 3;
    repeated ChairGameRewardInfo reward_list = 4; // 游戏奖励
    repeated PlayerInfo players = 5; // 参与游戏的用户
}

// 获取房内抢椅子游戏信息
message GetChairGameInfoRequest {
    uint32 channel_id = 1;
}

message GetChairGameInfoResponse {
    ChairGameInfo game_info = 1;
}

// 抢座
message GrabChairRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 game_id = 3;
}

message GrabChairResponse {
}

// --------------- 主持人侧 ------------------
// 开启新的一局
message StartChairGameRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
    repeated uint32 uids = 3; // 参与本轮抢椅子的用户列表
    uint32 wedding_id = 4; // 婚礼id
    repeated PlayerInfo players = 5; // 参与本轮抢椅子的用户列表
}

message PlayerInfo {
    uint32 uid = 1;
    uint32 mic_id = 2;
}

message StartChairGameResponse {
    repeated uint32 players = 1; // 通过检查的用户列表
}

// 进入下一轮
message SetChairGameToNextRoundRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
}

message SetChairGameToNextRoundResponse {
}

// 开抢
message StartGrabChairRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
}

message StartGrabChairResponse {
}