syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-open-game-auth";

package channel_open_game_auth;

service ChannelOpenGameAuth {
    rpc GetOpenid (GetOpenidReq) returns (GetOpenidResp) {
    }
    rpc GetUid (GetUidReq) returns (GetUidResp) {
    }
    rpc BatchGetUidByOpenid (BatchGetUidByOpenidReq) returns (BatchGetUidByOpenidResp) {
    }
    rpc BatchGetOpenidByUid (BatchGetOpenidByUidReq) returns (BatchGetOpenidByUidResp) {
    }
}

message GetOpenidReq {
    uint32 uid = 1;
    uint32 game_id = 2;
    uint32 cp_id = 3;
}

message GetOpenidResp {
    AuthInfo info = 1;
    string code = 2;
}

message GetUidReq {
    uint32 uid = 1;
    uint32 game_id = 2;
    string openid = 3;
}

message GetUidResp {
    uint32 uid = 1;
}

message BatchGetUidByOpenidReq {
    uint32 uid = 1;
    uint32 game_id = 2;
    repeated string openid_list = 3;
}

message BatchGetUidByOpenidResp {
    repeated AuthInfo info_list = 1;
}

message BatchGetOpenidByUidReq {
    uint32 uid = 1;
    uint32 game_id = 2;
    repeated uint32 uid_list = 3;
}

message BatchGetOpenidByUidResp {
    repeated AuthInfo info_list = 1;
}

message AuthInfo {
    uint32 uid = 1;
    string openid = 2;
}
