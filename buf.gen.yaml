version: v2
plugins:
  - remote: psr.ttyuyin.com/plugins/tt-ancient # 生成协议 message 和 gRPC 代码
    out: ../../../../protocol/services
    opt: module=golang.52tt.com/protocol/services
  - remote: psr.ttyuyin.com/plugins/tt-go-grpc-mock # 生成 gRPC mock 代码
    out: ../../../../protocol/services
    opt: module=golang.52tt.com/protocol/services
  - remote: psr.ttyuyin.com/plugins/tt-go-client # 生成 tyr gRPC 客户端代码
    out: ../../../../protocol/services
    opt: module=golang.52tt.com/protocol/services
  - remote: psr.ttyuyin.com/plugins/psr-http-go # 生成 psr http 框架代码，参考 https://gitlab.ttyuyin.com/hyperion-ecosystem/psr-http
    out: ../../../../protocol/services
    opt: module=golang.52tt.com/protocol/services