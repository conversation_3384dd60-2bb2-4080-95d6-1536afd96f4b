package mgr

import (
    "context"
    "fmt"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/protocol/services/esport_hall"
    game_card "golang.52tt.com/protocol/services/game-card"
    topic_channel_pb "golang.52tt.com/protocol/services/topic_channel/channel"
    "golang.52tt.com/protocol/services/topic_channel/tab"
    "golang.52tt.com/services/tt-rev/esport/esport-logic/internal/conf"
    "sync"
)

var 王者荣耀GameCardFiller = PropertyFillerChain(
    房间区服filler,
    房间段位要求filler,
    房间想找分路filler,
    房间房主要求filler,
    游戏卡区服filler,
    游戏卡段位类型filler,
    游戏卡游戏风格filler,
)

var 和平精英GameCardFiller = PropertyFillerChain(
    房间区服filler,
    房间段位要求filler,
    房间地图filler,
    房间房主要求filler,
    游戏卡区服filler,
    游戏卡段位filler,
    游戏卡游戏风格filler,
)

var 金铲铲之战GameCardFiller = PropertyFillerChain(
    房间区服filler,
    房间段位要求filler,
    房间房主要求filler,
    游戏卡区服filler,
    游戏卡段位filler,
)

var UserGameCardOnUgcFillerMap = map[string]UserGameCardOnUgcFiller{
    "王者荣耀":   王者荣耀GameCardFiller,
    "和平精英":   和平精英GameCardFiller,
    "金铲铲之战": 金铲铲之战GameCardFiller,
}

type UserGameCardOnUgcFiller func(channelSearchInfo map[string][]string, userSearchInfo map[string][]string, target *UserGameCardOnUgc)

var PropertyFillerChain = func(fillers ...UserGameCardOnUgcFiller) UserGameCardOnUgcFiller {
    return func(channelSearchInfo map[string][]string, userSearchInfo map[string][]string, target *UserGameCardOnUgc) {
        for _, f := range fillers {
            f(channelSearchInfo, userSearchInfo, target)
        }
    }
}

type UserGameCardOnUgc struct {
    GameId       uint32
    GameProperty []*esport_hall.GameProperty
}

func (receiver *UserGameCardOnUgc) GetGameId() uint32 {
    if receiver == nil {
        return 0
    }
    return receiver.GameId
}

func (receiver *UserGameCardOnUgc) GetGameProperty() []*esport_hall.GameProperty {
    if receiver == nil {
        return []*esport_hall.GameProperty{}
    }
    return receiver.GameProperty
}

// GetUserGameCardOnUGC 获取用户在UGC房间的游戏资料卡
// 1. 先查询房间的发布信息。
// 2. 再查询用户的游戏卡列表。
// 3. 根据房间的发布信息的game_id，匹配出用户的游戏卡信息
// 4. 组装成UserGameCardOnUgc返回
func (m *MgrImpl) GetUserGameCardOnUGC(ctx context.Context, uid uint32, cid uint32) (*UserGameCardOnUgc, error) {

    var channelInfo *topic_channel_pb.ChannelInfo
    var esportGame *conf.UGCGameMapToEsportGame
    channelSearchInfo := map[string][]string{}
    userSearchInfo := map[string][]string{}

    channelInfoResp, serverError := m.topicChannelCli.GetChannelByIds(ctx, &topic_channel_pb.GetChannelByIdsReq{
        Ids:       []uint32{cid},
        Types:     nil,
        ReturnAll: true,
        Source:    "ugc-esports",
    })
    if serverError != nil {
        return nil, fmt.Errorf("GetChannelByIds error: %w", serverError)
    }
    if len(channelInfoResp.GetInfo()) == 0 {
        return nil, fmt.Errorf("GetChannelByIds error: channel not found, cid: %d", cid)
    }
    channelInfo = channelInfoResp.GetInfo()[0]

    // ======================================= 获取游戏的信息 ==================================
    tabInfo, serverError := m.tabCli.GetTabById(ctx, channelInfo.GetTabId())
    if serverError != nil {
        return nil, fmt.Errorf("GetTabById error: %w", serverError)
    }

    // 获取映射过来的游戏信息
    ugcGameMapping := m.bc.GetUGCChannelSetting().UgcGameMapToEsportGame
    esportGame = ugcGameMapping[tabInfo.GetGameInfo().GetGameCardId()]
    if esportGame == nil || esportGame.EsportGameId == 0 || len(esportGame.GameName) == 0 {
        return nil, fmt.Errorf("esport game conf is empty or invalid, config: %v", esportGame)
    }
    // 构建返回值
    out := &UserGameCardOnUgc{
        GameId:       esportGame.EsportGameId,
        GameProperty: make([]*esport_hall.GameProperty, 0),
    }
    // 并发请求房间发布信息和用户的游戏卡信息
    wg := sync.WaitGroup{}
    wg.Add(2)
    // ========================= 获取房间的发布信息 =========================
    go func() {
        defer wg.Done()
        // 查询房间发布的block信息
        blockIndex := make(map[uint32]*tab.Block)
        blocksResp, serverError := m.tabCli.Blocks(ctx, &tab.BlocksReq{
            TabId: channelInfo.GetTabId(),
        })
        if serverError != nil {
            log.ErrorWithCtx(ctx, "GetUserGameCardOnUGC Blocks error: %v, uid: %d, cid: %d", serverError, uid, cid)
            return
        }
        // 建立索引
        for _, item := range blocksResp.GetBlocks() {
            blockIndex[item.GetId()] = item
        }

        // 过滤出发布的block信息，组装成房间的发布信息
        for _, item := range channelInfo.GetBlockOptions() {
            targetBlock := blockIndex[item.GetBlockId()]
            if targetBlock == nil {
                continue
            }
            // 判断block 是否是选项类型，如果是选择类型则用的是title，如果是用户输入类型，用的就是val
            if targetBlock.GetMode() == tab.Block_USER_INPUT {
                channelSearchInfo[targetBlock.GetTitle()] = append(channelSearchInfo[targetBlock.GetTitle()], item.GetElemVal())
            } else {
                for _, e := range targetBlock.GetElems() {
                    if e.GetId() == item.GetElemId() {
                        channelSearchInfo[targetBlock.GetTitle()] = append(channelSearchInfo[targetBlock.GetTitle()], e.GetTitle())
                        break
                    }
                }
            }
        }
    }()

    // =============================== 获取用户的游戏卡信息 ============================
    go func() {
        defer wg.Done()
        gameCardResp, serverError := m.gameCardCli.GetGameCard(ctx, uid)
        if serverError != nil {
            log.ErrorWithCtx(ctx, "GetUserGameCardOnUGC GetGameCard error: %v, uid: %d, cid: %d", serverError, uid, cid)
            return
        }
        // 根据tab的game_card_id匹配
        var gameCard *game_card.GameCardInfo
        for _, item := range gameCardResp {
            if item.GetGameCardId() == tabInfo.GetGameInfo().GetGameCardId() {
                gameCard = item
                break
            }
        }
        if gameCard != nil {
            // 如果有匹配到，组装成 userSearchInfo
            for _, opt := range gameCard.GetOptList() {
                // 判断opt的类型
                if opt.GetOptType() == game_card.GameCardOptType_GAME_CARD_OPT_INPUT {
                    for _, input := range opt.GetInputVal() {
                        userSearchInfo[input.GetElemTitle()] = append(userSearchInfo[input.GetElemTitle()], input.GetElemVal())
                    }
                } else {
                    userSearchInfo[opt.GetOptName()] = append(userSearchInfo[opt.GetOptName()], opt.GetValueList()...)
                }
            }
        } else {
            log.InfoWithCtx(ctx, "GetUserGameCardOnUGC 用户没有匹配的游戏卡 game_id: %d, uid: %d", tabInfo.GetGameInfo().GetGameCardId(), uid)
        }
    }()

    wg.Wait()

    log.DebugWithCtx(ctx, "GetUserGameCardOnUGC channelSearchInfo: %+v, userSearchInfo: %+v", channelSearchInfo, userSearchInfo)

    // 获取builder
    filler := UserGameCardOnUgcFillerMap[esportGame.GameName]
    if filler == nil {
        // 没有合适的filler，返回空
        return out, nil
    }

    filler(channelSearchInfo, userSearchInfo, out)
    log.DebugWithCtx(ctx, "GetUserGameCardOnUGC out: %+v", out)
    return out, nil
}

// GetUGCRoomGameId 获取UGC房间的游戏ID
func (m *MgrImpl) GetUGCRoomGameId(ctx context.Context, cid uint32) (uint32, error) {

    channelInfoResp, serverError := m.topicChannelCli.GetChannelByIds(ctx, &topic_channel_pb.GetChannelByIdsReq{
        Ids:       []uint32{cid},
        Types:     nil,
        ReturnAll: true,
        Source:    "ugc-esports",
    })
    if serverError != nil {
        return 0, fmt.Errorf("GetChannelByIds error: %w", serverError)
    }
    if len(channelInfoResp.GetInfo()) == 0 {
        return 0, fmt.Errorf("GetChannelByIds error: channel not found, cid: %d", cid)
    }
    channelInfo := channelInfoResp.GetInfo()[0]

    // ======================================= 获取游戏的信息 ==================================
    tabInfo, serverError := m.tabCli.GetTabById(ctx, channelInfo.GetTabId())
    if serverError != nil {
        return 0, fmt.Errorf("GetTabById error: %w", serverError)
    }

    // 获取映射过来的游戏信息
    ugcGameMapping := m.bc.GetUGCChannelSetting().UgcGameMapToEsportGame
    esportGame := ugcGameMapping[tabInfo.GetGameInfo().GetGameCardId()]
    if esportGame == nil || esportGame.EsportGameId == 0 || len(esportGame.GameName) == 0 {
        return 0, fmt.Errorf("esport game conf is empty or invalid, config: %v", esportGame)
    }
    return esportGame.EsportGameId, nil
}

var 房间区服filler = func(channelSearchInfo map[string][]string, userSearchInfo map[string][]string, target *UserGameCardOnUgc) {
    ugcKey := "区服"
    esportKey := "接单大区"

    if len(channelSearchInfo[ugcKey]) == 0 {
        return
    }

    valList := make([]*esport_hall.GamePropertyVal, 0, len(channelSearchInfo[ugcKey]))
    for _, item := range channelSearchInfo[ugcKey] {
        valList = append(valList, &esport_hall.GamePropertyVal{
            Name: item,
        })
    }

    target.GameProperty = append(target.GameProperty, &esport_hall.GameProperty{
        Name:         esportKey,
        ValList:      valList,
        PropertyType: uint32(esport_hall.GameProperty_PROPERTY_TYPE_CUSTOM),
    })
}

var 房间段位要求filler = func(channelSearchInfo map[string][]string, userSearchInfo map[string][]string, target *UserGameCardOnUgc) {
    ugcKey := "段位要求"
    esportKey := "可接段位"

    if len(channelSearchInfo[ugcKey]) == 0 {
        return
    }

    valList := make([]*esport_hall.GamePropertyVal, 0, len(channelSearchInfo[ugcKey]))
    for _, item := range channelSearchInfo[ugcKey] {
        valList = append(valList, &esport_hall.GamePropertyVal{
            Name: item,
        })
    }
    // 额外塞一个：全段位接单
    valList = append(valList, &esport_hall.GamePropertyVal{
        Name: "全段位接单",
    })

    target.GameProperty = append(target.GameProperty, &esport_hall.GameProperty{
        Name:         esportKey,
        ValList:      valList,
        PropertyType: uint32(esport_hall.GameProperty_PROPERTY_TYPE_CUSTOM),
    })
}

var 房间想找分路filler = func(channelSearchInfo map[string][]string, userSearchInfo map[string][]string, target *UserGameCardOnUgc) {
    ugcKey := "想找分路"
    esportKey := "擅长位置"

    if len(channelSearchInfo[ugcKey]) == 0 {
        return
    }

    valList := make([]*esport_hall.GamePropertyVal, 0, len(channelSearchInfo[ugcKey]))
    for _, item := range channelSearchInfo[ugcKey] {
        if item == "辅助" {
            valList = append(valList, &esport_hall.GamePropertyVal{
                Name: "游走",
            })
        } else {
            valList = append(valList, &esport_hall.GamePropertyVal{
                Name: item,
            })
        }
    }

    target.GameProperty = append(target.GameProperty, &esport_hall.GameProperty{
        Name:         esportKey,
        ValList:      valList,
        PropertyType: uint32(esport_hall.GameProperty_PROPERTY_TYPE_CUSTOM),
    })
}

var 房间房主要求filler = func(channelSearchInfo map[string][]string, userSearchInfo map[string][]string, target *UserGameCardOnUgc) {
    ugcKey := "房主要求"
    esportKey := "游戏特色"

    if len(channelSearchInfo[ugcKey]) == 0 {
        return
    }

    valList := make([]*esport_hall.GamePropertyVal, 0, len(channelSearchInfo[ugcKey]))
    genderVal := make([]*esport_hall.GamePropertyVal, 0, 2)
    for _, item := range channelSearchInfo[ugcKey] {
        if item == "只要男生" {
            genderVal = append(genderVal, &esport_hall.GamePropertyVal{
                Name: "男",
            })
            continue
        }
        if item == "只要女生" {
            genderVal = append(genderVal, &esport_hall.GamePropertyVal{
                Name: "女",
            })
            continue
        }
        valList = append(valList, &esport_hall.GamePropertyVal{
            Name: item,
        })
    }

    target.GameProperty = append(target.GameProperty, &esport_hall.GameProperty{
        Name:         esportKey,
        ValList:      valList,
        PropertyType: uint32(esport_hall.GameProperty_PROPERTY_TYPE_CUSTOM),
    })

    if len(genderVal) > 0 {
        target.GameProperty = append(target.GameProperty, &esport_hall.GameProperty{
            Name:         "性别",
            ValList:      genderVal,
            PropertyType: uint32(esport_hall.GameProperty_PROPERTY_TYPE_GENDER),
        })
    }

}

var 房间地图filler = func(channelSearchInfo map[string][]string, userSearchInfo map[string][]string, target *UserGameCardOnUgc) {
    ugcKey := "地图"
    esportKey := "擅长地图"

    if len(channelSearchInfo[ugcKey]) == 0 {
        return
    }
    valList := make([]*esport_hall.GamePropertyVal, 0, len(channelSearchInfo[ugcKey]))
    for _, item := range channelSearchInfo[ugcKey] {
        if item == "经典海岛" {
            valList = append(valList, &esport_hall.GamePropertyVal{
                Name: "海岛",
            })
        } else {
            valList = append(valList, &esport_hall.GamePropertyVal{
                Name: item,
            })
        }
    }

    target.GameProperty = append(target.GameProperty, &esport_hall.GameProperty{
        Name:         esportKey,
        ValList:      valList,
        PropertyType: uint32(esport_hall.GameProperty_PROPERTY_TYPE_CUSTOM),
    })
}

var 游戏卡区服filler = func(channelSearchInfo map[string][]string, userSearchInfo map[string][]string, target *UserGameCardOnUgc) {
    ugcKey := "区服"
    esportKey := "接单大区"

    if len(userSearchInfo[ugcKey]) == 0 {
        return
    }

    valList := make([]*esport_hall.GamePropertyVal, 0, len(userSearchInfo[ugcKey]))
    for _, item := range userSearchInfo[ugcKey] {
        valList = append(valList, &esport_hall.GamePropertyVal{
            Name: item,
        })
    }

    target.GameProperty = append(target.GameProperty, &esport_hall.GameProperty{
        Name:         esportKey,
        ValList:      valList,
        PropertyType: uint32(esport_hall.GameProperty_PROPERTY_TYPE_CUSTOM),
    })
}

var 游戏卡段位类型filler = func(channelSearchInfo map[string][]string, userSearchInfo map[string][]string, target *UserGameCardOnUgc) {
    ugcKey := "段位类型"
    esportKey := "可接段位"

    if len(userSearchInfo[ugcKey]) == 0 {
        return
    }

    valList := make([]*esport_hall.GamePropertyVal, 0, len(userSearchInfo[ugcKey]))
    for _, item := range userSearchInfo[ugcKey] {
        valList = append(valList, &esport_hall.GamePropertyVal{
            Name: item,
        })
    }
    // 额外塞一个：全段位接单
    valList = append(valList, &esport_hall.GamePropertyVal{
        Name: "全段位接单",
    })

    target.GameProperty = append(target.GameProperty, &esport_hall.GameProperty{
        Name:         esportKey,
        ValList:      valList,
        PropertyType: uint32(esport_hall.GameProperty_PROPERTY_TYPE_CUSTOM),
    })
}

var 游戏卡游戏风格filler = func(channelSearchInfo map[string][]string, userSearchInfo map[string][]string, target *UserGameCardOnUgc) {
    ugcKey := "游戏风格"
    esportKey := "游戏特色"

    if len(channelSearchInfo[ugcKey]) == 0 {
        return
    }

    valList := make([]*esport_hall.GamePropertyVal, 0, len(channelSearchInfo[ugcKey]))
    genderVal := make([]*esport_hall.GamePropertyVal, 0, 2)
    for _, item := range channelSearchInfo[ugcKey] {
        if item == "男生优先" {
            genderVal = append(genderVal, &esport_hall.GamePropertyVal{
                Name: "男",
            })
            continue
        }
        if item == "女生优先" {
            genderVal = append(genderVal, &esport_hall.GamePropertyVal{
                Name: "女",
            })
            continue
        }
        valList = append(valList, &esport_hall.GamePropertyVal{
            Name: item,
        })
    }

    target.GameProperty = append(target.GameProperty, &esport_hall.GameProperty{
        Name:         esportKey,
        ValList:      valList,
        PropertyType: uint32(esport_hall.GameProperty_PROPERTY_TYPE_CUSTOM),
    })

    if len(genderVal) > 0 {
        target.GameProperty = append(target.GameProperty, &esport_hall.GameProperty{
            Name:         "性别",
            ValList:      genderVal,
            PropertyType: uint32(esport_hall.GameProperty_PROPERTY_TYPE_GENDER),
        })
    }
}

var 游戏卡段位filler = func(channelSearchInfo map[string][]string, userSearchInfo map[string][]string, target *UserGameCardOnUgc) {
    ugcKey := "段位"
    esportKey := "段位信息"

    if len(userSearchInfo[ugcKey]) == 0 {
        return
    }

    valList := make([]*esport_hall.GamePropertyVal, 0, len(userSearchInfo[ugcKey]))
    for _, item := range userSearchInfo[ugcKey] {
        valList = append(valList, &esport_hall.GamePropertyVal{
            Name: item,
        })
    }

    target.GameProperty = append(target.GameProperty, &esport_hall.GameProperty{
        Name:         esportKey,
        ValList:      valList,
        PropertyType: uint32(esport_hall.GameProperty_PROPERTY_TYPE_CUSTOM),
    })
}
