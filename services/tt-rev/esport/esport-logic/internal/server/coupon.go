package server

import (
	"bytes"
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/ttversion"
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/esport_logic"
	errCode "golang.52tt.com/protocol/common/status"
	esport_trade "golang.52tt.com/protocol/services/esport-trade"
	"math"
	"math/rand"
	"sort"
	"text/template"
	"time"
)

var (
	couponMinVer             = ttversion.Parse("电竞优惠券版本", "android-6.53.5", "ios-6.53.5", "pc-2.0.4")
	couponManualChooseMinVer = ttversion.Parse("电竞优惠券可手动选择版本", "android-6.54.5", "ios-6.54.5", "pc-2.0.4")
)

func (s *Server) GainCoupon(ctx context.Context, baseReq *app.BaseReq, uid uint32, orderId string, entranceSourceType uint32) (*esport_logic.CouponPopupInfo, error) {
	// 获取订单详情
	orderDetailResp, err := s.orderService.GetOrderDetail(ctx, &esport_trade.GetOrderDetailRequest{
		OrderId: orderId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GainCoupon fail to GetOrderDetail. orderId:%s, err:%v", orderId, err)
		return nil, err
	}

	gainCouponResp, err := s.orderService.AutoGrantCoupon(ctx, &esport_trade.AutoGrantCouponRequest{
		BaseReq:         baseReq,
		Uid:             uid,
		OrderId:         orderId,
		Source:          entranceSourceType,
		PreCheck:        false,
		CoachUid:        orderDetailResp.GetOrder().GetCoachUid(),
		OrderTotalPrice: orderDetailResp.GetOrder().GetProductOrder().GetTotalPrice(),
	})
	if err != nil {
		log.WarnWithCtx(ctx, "GainCoupon fail to AutoGrantCoupon. uid:%d, orderId:%s, err:%v", uid, orderId, err)
		return nil, err
	}
	if len(gainCouponResp.GetCouponList()) == 0 {
		return nil, nil
	}

	showCouponPopupInfoCouponList := coupon2showCouponPopupInfoCoupon(gainCouponResp.GetCouponList())
	showCouponPopupInfo := &esport_logic.ShowCouponPopupInfo{
		CouponList: showCouponPopupInfoCouponList,
	}

	tplConf := s.tplConf.GetConfig()
	couponInfo, err := renderCouponInfo(ctx, tplConf.CouponPopupTpl.GainMainTpl, tplConf.CouponPopupTpl.GainSubTpl, showCouponPopupInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GainCoupon fail to renderCouponInfo. err:%v", err)
		return nil, err
	}

	// 抽取优惠券的id，并数分上报
	couponIds := make([]string, 0, len(gainCouponResp.GetCouponList()))
	for _, item := range gainCouponResp.GetCouponList() {
		couponIds = append(couponIds, item.GetCouponId())
	}
	s.mgr.AsyncReportByLinkIssueCoupon(ctx, uid, couponIds, orderId)

	return &esport_logic.CouponPopupInfo{
		Type: esport_logic.CouponPopupInfo_COUPON_POPUP_TYPE_GAIN_COVER,
		GainCouponCover: &esport_logic.GainCouponCover{
			AutoOpenSecond:  s.bc.GetCouponConf().GainCouponCoverTime,
			OpeningResource: s.bc.GetCouponConf().GainCouponCoverResource,
			OpeningMd5:      s.bc.GetCouponConf().GainCouponCoverMd5,
		},
		RemainCoupon: couponInfo,
	}, err
}

func (s *Server) ShowRemainCoupon(ctx context.Context) (*esport_logic.CouponPopupInfo, error) {
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return nil, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}
	// 新版本才可以领取
	if !couponMinVer.Atleast(serviceInfo.ClientType, serviceInfo.ClientVersion) {
		log.DebugWithCtx(ctx, "GainCoupon fail. feature not open. uid:%d, clientType:%s, clientVersion:%s", serviceInfo.UserID, serviceInfo.ClientType, serviceInfo.ClientVersion)
		return nil, nil
	}
	uid := serviceInfo.UserID

	// 每天只展示一次
	//resp, err := s.orderService.MarkFinishOrderShowRemainToday(ctx, &esport_trade.MarkFinishOrderShowRemainTodayRequest{
	//    Uid: uid,
	//})
	//if err != nil {
	//    log.ErrorWithCtx(ctx, "ShowRemainCoupon fail to MarkFinishOrderShowRemainToday. uid:%d, err:%v", uid, err)
	//    return nil, err
	//}
	//if !resp.MarkSuccess {
	//    return nil, nil
	//}

	userCouponResp, err := s.orderService.GetUserCoupon(ctx, &esport_trade.GetUserCouponRequest{
		Uid: uid,
	})
	if err != nil {
		log.WarnWithCtx(ctx, "GainCoupon fail to GetUserCoupon. uid:%d, err:%v", uid, err)
	}
	if len(userCouponResp.GetCouponList()) == 0 {
		return nil, nil
	}
	showCouponPopupInfoCouponList := coupon2showCouponPopupInfoCoupon(userCouponResp.GetCouponList())
	showCouponPopupInfo := &esport_logic.ShowCouponPopupInfo{
		SubTitle:   fmt.Sprintf("%d", len(showCouponPopupInfoCouponList)),
		CouponList: showCouponPopupInfoCouponList,
	}

	tplConf := s.tplConf.GetConfig()
	couponInfo, _ := renderCouponInfo(ctx, tplConf.CouponPopupTpl.FinishOrderShowRemainTpl,
		tplConf.CouponPopupTpl.FinishOrderShowRemainSubTpl, showCouponPopupInfo)
	return &esport_logic.CouponPopupInfo{
		Type:         esport_logic.CouponPopupInfo_COUPON_POPUP_TYPE_SHOW_REMAIN,
		RemainCoupon: couponInfo,
	}, nil
}

func coupon2showCouponPopupInfoCoupon(couponList []*esport_trade.Coupon) []*esport_logic.ShowCouponPopupInfo_CouponItem {
	showCouponPopupInfoCouponList := make([]*esport_logic.ShowCouponPopupInfo_CouponItem, 0, len(couponList))
	for _, item := range couponList {
		effectTime := time.Unix(int64(item.GetEffectTime()), 0)
		expireTime := time.Unix(int64(item.GetExpireTime()-1), 0) // -1 防止到期时间显示为下一天
		couponSubDesc := ""
		usageLimitText := ""
		if item.GetUsageLimitType() == uint32(esport_trade.CouponConfig_USAGE_LIMIT_TYPE_NONE) {
			couponSubDesc = "<tttext font-color=#FFF8D0 font-size=10 font-name=\"PingFang SC\">无门槛</tttext>"
		} else if item.GetUsageLimitText() != "" {
			usageLimitText = fmt.Sprintf("<tttext font-color=#FFF8D0CC font-size=10 font-name=\"PingFang SC\">%s</tttext>", item.GetUsageLimitText())
		}
		expiration := fmt.Sprintf("有效期 %s-%s", effectTime.Format("2006.01.02"), expireTime.Format("2006.01.02"))
		if effectTime.Month() == expireTime.Month() && effectTime.Day() == expireTime.Day() {
			expiration = fmt.Sprintf("有效期 %s可用", effectTime.Format("2006.01.02"))
		}
		couponExpiration := fmt.Sprintf("<tttext font-color=#FFF8D0CC font-size=10 font-name=\"PingFang SC\">%s</tttext>", expiration)

		showCouponPopupInfoCouponList = append(showCouponPopupInfoCouponList, &esport_logic.ShowCouponPopupInfo_CouponItem{
			CouponDesc:       fmt.Sprintf("%d", item.GetReducePrice()/100),
			CouponSubDesc:    couponSubDesc,
			CouponName:       item.GetCoponName(),
			CouponLimitText:  usageLimitText,
			CouponExpiration: couponExpiration,
			CornerText:       genCornerText(effectTime),
		})
	}

	return showCouponPopupInfoCouponList
}

func genCornerText(effectTime time.Time) string {
	nowZero := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Local)
	effectZero := time.Date(effectTime.Year(), effectTime.Month(), effectTime.Day(), 0, 0, 0, 0, time.Local)
	daysDifference := int(effectZero.Sub(nowZero).Hours() / 24)

	switch {
	case daysDifference < 0:
		return "今日可用"
	case daysDifference == 0:
		return "今日可用"
	case daysDifference == 1:
		return "明日可用"
	default:
		return fmt.Sprintf("%d日后可用", daysDifference)
	}
}

func renderCouponInfo(ctx context.Context, mainTpl, itemTpl string, data *esport_logic.ShowCouponPopupInfo) (string, error) {
	var itemBuf bytes.Buffer
	iItemTpl := template.Must(template.New("item").Parse(itemTpl))
	err := iItemTpl.Execute(&itemBuf, data.GetCouponList())
	if err != nil {
		log.ErrorWithCtx(ctx, "RenderCouponInfo", "err", err)
		return "", err
	}

	data.CouponInfo = itemBuf.String()
	iMainTpl := template.Must(template.New("main").Parse(mainTpl))
	var mainBuf bytes.Buffer
	err = iMainTpl.Execute(&mainBuf, data)
	if err != nil {
		log.ErrorWithCtx(ctx, "RenderCouponInfo", "err", err)
		return "", err
	}

	return mainBuf.String(), nil
}

func (s *Server) LoginAppShowCouponRemain(ctx context.Context, request *esport_logic.LoginAppShowCouponRemainRequest) (*esport_logic.LoginAppShowCouponRemainResponse, error) {
	resp := &esport_logic.LoginAppShowCouponRemainResponse{}

	if !s.bc.GetCouponConf().LoginShowSwitch {
		log.DebugWithCtx(ctx, "LoginAppShowCouponRemain fail. switch not open.")
		return resp, nil
	}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return resp, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}
	userCoupon, err := s.orderService.GetUserCoupon(ctx, &esport_trade.GetUserCouponRequest{
		Uid: serviceInfo.UserID,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "LoginAppShowCouponRemain fail to GetUserCoupon. err:%v", err)
		return resp, err
	}
	if len(userCoupon.GetCouponList()) == 0 {
		return resp, nil
	}

	showCouponPopupInfoCouponList := coupon2showCouponPopupInfoCoupon(userCoupon.GetCouponList())
	showCouponPopupInfo := &esport_logic.ShowCouponPopupInfo{
		CouponList: showCouponPopupInfoCouponList,
	}

	couponInfo, err := renderCouponInfo(ctx, s.tplConf.GetConfig().CouponPopupTpl.ShowRemainMainTpl,
		s.tplConf.GetConfig().GetCouponPopupTpl().ShowRemainSubTpl, showCouponPopupInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "LoginAppShowCouponRemain fail to renderCouponInfo. err:%v", err)
		return nil, err
	}
	resp.CouponInfo = couponInfo

	return resp, nil
}

func (s *Server) ShowManualGrantCoupon(ctx context.Context, in *esport_logic.ShowManualGrantCouponRequest) (out *esport_logic.ShowManualGrantCouponResponse, err error) {
	out = &esport_logic.ShowManualGrantCouponResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "ShowManualGrantCoupon in:%+v, out:%+v, err:%v", in, out, err)
	}()

	// 先判断开关
	if !s.bc.GetCouponConf().ShowUnreadManualGrantCoupon {
		return
	}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		err = protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
		return
	}

	// 获取未读的手动发券
	rsp, err := s.orderService.GetUnreadManualGrantCoupon(ctx, &esport_trade.GetUnreadManualGrantCouponRequest{Uid: serviceInfo.UserID})
	if err != nil {
		log.ErrorWithCtx(ctx, "ShowManualGrantCoupon fail to GetUnreadManualGrantCoupon. err:%v", err)
		return
	}
	if len(rsp.GetCouponList()) == 0 {
		return
	}

	// 渲染模板返回
	showCouponPopupInfoCouponList := coupon2showCouponPopupInfoCoupon(rsp.GetCouponList())
	showCouponPopupInfo := &esport_logic.ShowCouponPopupInfo{
		CouponList: showCouponPopupInfoCouponList,
	}
	couponInfo, err := renderCouponInfo(ctx, s.tplConf.GetConfig().GetCouponPopupTpl().ManualGrantCouponMainTpl,
		s.tplConf.GetConfig().GetCouponPopupTpl().ManualGrantCouponSubTpl, showCouponPopupInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "ShowManualGrantCoupon fail to renderCouponInfo. err:%v", err)
		return nil, err
	}

	out.CouponInfo = couponInfo
	return
}

func (s *Server) MarkManualGrantCouponRead(ctx context.Context, in *esport_logic.MarkManualGrantCouponReadRequest) (out *esport_logic.MarkManualGrantCouponReadResponse, err error) {
	out = &esport_logic.MarkManualGrantCouponReadResponse{}
	log.InfoWithCtx(ctx, "MarkManualGrantCouponRead")

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		err = protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
		return
	}

	_, err = s.orderService.MarkManualGrantCouponRead(ctx, &esport_trade.MarkManualGrantCouponReadRequest{Uid: serviceInfo.UserID})
	if err != nil {
		log.ErrorWithCtx(ctx, "MarkManualGrantCouponRead fail to MarkManualGrantCouponRead. err:%v", err)
		return
	}

	return
}

func (s *Server) GetCouponEntranceInfo(ctx context.Context, in *esport_logic.GetCouponEntranceInfoRequest) (out *esport_logic.GetCouponEntranceInfoResponse, err error) {
	out = &esport_logic.GetCouponEntranceInfoResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "GetCouponEntranceInfo in:%+v, out:%+v, err:%v", in, out, err)
	}()

	// 先判断开关
	if !s.bc.GetCouponConf().ShowCouponEntrance {
		return
	}
	out.ShowCouponEntrance = true

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		err = protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
		return
	}

	// 获取用户待使用的优惠券列表
	rsp, err := s.orderService.GetUserCoupon(ctx, &esport_trade.GetUserCouponRequest{Uid: serviceInfo.UserID})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCouponEntranceInfo fail to GetUserCoupon. err:%v", err)
		return
	}
	couponList := rsp.GetCouponList()
	if len(couponList) == 0 {
		return
	}

	// 拼装提示文案
	minExpireSec := calcUserCouponMinExpireSec(couponList)
	if minExpireSec < 3600*12 {
		expireHour := minExpireSec / 3600
		expireMinute := (minExpireSec % 3600) / 60
		if expireHour == 0 && expireMinute == 0 {
			out.CouponExpiringText = "优惠券还有1分钟过期，及时使用哦~"
		} else if expireHour == 0 {
			out.CouponExpiringText = fmt.Sprintf("优惠券还有%d分钟过期，及时使用哦~", expireMinute)
		} else if expireMinute == 0 {
			out.CouponExpiringText = fmt.Sprintf("优惠券还有%d小时过期，及时使用哦~", expireHour)
		} else {
			out.CouponExpiringText = fmt.Sprintf("优惠券还有%d小时%d分钟过期，及时使用哦~", expireHour, expireMinute)
		}
	}
	out.CouponUnusedText = fmt.Sprintf("还有%d张优惠券，记得及时使用哦~", len(couponList))

	return
}

func calcUserCouponMinExpireSec(couponList []*esport_trade.Coupon) uint32 {
	now := uint32(time.Now().Unix())
	minExpireSec := uint32(math.MaxUint32)
	for _, coupon := range couponList {
		if coupon.GetExpireTime() <= now {
			continue
		}
		expireDuration := coupon.GetExpireTime() - now
		if expireDuration < minExpireSec {
			minExpireSec = expireDuration
		}
	}
	return minExpireSec
}

const (
	homeCouponBubbleTextTypeNoParam = 0
	homeCouponBubbleTextTypeNZhang = 1
	homeCouponBubbleTextTypeNYuan = 2
)

func (s *Server) GetHomeCouponEntranceInfo(ctx context.Context, in *esport_logic.GetHomeCouponEntranceInfoRequest) (*esport_logic.GetHomeCouponEntranceInfoResponse, error) {
	out := &esport_logic.GetHomeCouponEntranceInfoResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "GetHomeCouponEntranceInfo in:%+v, out:%+v", in, out)
	}()

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, nil
	}

	// 获取用户待使用的优惠券列表
	rsp, err := s.orderService.GetUserCoupon(ctx, &esport_trade.GetUserCouponRequest{Uid: serviceInfo.UserID})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetHomeCouponEntranceInfo fail to GetUserCoupon. err:%v", err)
		return out, nil
	}
	couponList := rsp.GetCouponList()
	if len(couponList) == 0 {
		return out, nil
	}

	s.fillCouponBubbleInfo(ctx, couponList, out) // 填充金刚区的气泡信息
	s.fillCouponIconInfo(ctx, couponList, out) // 填充右下角的优惠券icon信息

	// 计算所有优惠券最早的生效/过期时间，客户端到点了来刷新
	checkTs := uint32(math.MaxUint32)
	now := uint32(time.Now().Unix())
	for _, coupon := range couponList {
		if coupon.GetExpireTime() > now && coupon.GetExpireTime() < checkTs {
			checkTs = coupon.GetExpireTime()
		}
		if coupon.GetEffectTime() > now && coupon.GetEffectTime() < checkTs {
			checkTs = coupon.GetEffectTime()
		}
	}
	if checkTs == uint32(math.MaxUint32) { // 理论上不会走到这里来，只是做个保护
		out.CheckTs = 0
	} else {
		// 随机延迟一段时间，避免集中在0点来刷新
		out.CheckTs = int64(checkTs) + int64(rand.Intn(300))
	}

	return out, nil
}

func (s *Server) fillCouponBubbleInfo(ctx context.Context, couponList []*esport_trade.Coupon, out *esport_logic.GetHomeCouponEntranceInfoResponse) {
	// 先判断开关
	if !s.bc.GetCouponConf().HomeShowCouponEntrance {
		return
	}
	out.ShowCouponBubbleForever = s.bc.GetCouponConf().HomeShowCouponBubbleForever
	out.CouponCornerImg = s.bc.GetCouponConf().HomeCouponCornerImg // 展示角标

	expiringList := getUserExpiringCoupon(ctx, couponList)
	// 展示气泡文案
	if s.bc.GetCouponConf().HomeCouponBubbleTextType == homeCouponBubbleTextTypeNoParam {
		out.CouponBubbleText = s.bc.GetCouponConf().HomeCouponBubbleUnusedText
		if len(expiringList) > 0 {
			out.CouponBubbleText = s.bc.GetCouponConf().HomeCouponBubbleExpireText
		}
	} else if s.bc.GetCouponConf().HomeCouponBubbleTextType == homeCouponBubbleTextTypeNZhang {
		out.CouponBubbleText = fmt.Sprintf(s.bc.GetCouponConf().HomeCouponBubbleUnusedText, len(couponList))
		if len(expiringList) > 0 {
			out.CouponBubbleText = fmt.Sprintf(s.bc.GetCouponConf().HomeCouponBubbleExpireText, len(expiringList))
		}
	} else if s.bc.GetCouponConf().HomeCouponBubbleTextType == homeCouponBubbleTextTypeNYuan {
		out.CouponBubbleText = fmt.Sprintf(s.bc.GetCouponConf().HomeCouponBubbleUnusedText, couponList[0].GetReducePrice()/100)
		if len(expiringList) > 0 {
			out.CouponBubbleText = fmt.Sprintf(s.bc.GetCouponConf().HomeCouponBubbleExpireText, expiringList[0].GetReducePrice()/100)
		}
	} else { // 展示兜底文案，避免新加的type识别不到
		out.CouponBubbleText = "还有待使用的优惠券"
		if len(expiringList) > 0 {
			out.CouponBubbleText = "还有待使用的优惠券即将过期"
		}
	}
}

func getUserExpiringCoupon(ctx context.Context, couponList []*esport_trade.Coupon) []*esport_trade.Coupon {
	now := uint32(time.Now().Unix())
	expiringList := make([]*esport_trade.Coupon, 0, len(couponList))
	for _, coupon := range couponList {
		if coupon.GetExpireTime() <= now {
			continue
		}
		if coupon.GetExpireTime() - now < 3600*12 {
			expiringList = append(expiringList, coupon)
		}
	}
	if len(expiringList) == 0 {
		return expiringList
	}
	// 排序，ReducePrice最大的在前面
	sort.Slice(expiringList, func(i, j int) bool {
		return expiringList[i].GetReducePrice() > expiringList[j].GetReducePrice()
	})
	return expiringList
}

func (s *Server) fillCouponIconInfo(ctx context.Context, couponList []*esport_trade.Coupon, out *esport_logic.GetHomeCouponEntranceInfoResponse) {
	// 先判断开关
	if !s.bc.GetCouponConf().HomeShowCouponIcon {
		return
	}

	// 计算所有今日可用的优惠券中 最大的面值
	now := uint32(time.Now().Unix())
	for _, coupon := range couponList {
		if coupon.GetEffectTime() > now || coupon.GetExpireTime() < now {
			continue
		}
		couponValue := coupon.GetReducePrice() / 100 // 单位：元
		if couponValue > out.TodayAvaliableCouponValue {
			out.TodayAvaliableCouponValue = couponValue
		}
	}
}