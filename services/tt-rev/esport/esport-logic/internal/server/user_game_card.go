package server

import (
	"context"
	"encoding/hex"
	"fmt"
	auditTypes "golang.52tt.com/pkg/audit"
	pbApp "golang.52tt.com/protocol/app"
	v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protoGrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/esport_logic"
	"golang.52tt.com/protocol/common/status"
	esport_skill "golang.52tt.com/protocol/services/esport-skill"
	"golang.52tt.com/protocol/services/esport_hall"
	game_card "golang.52tt.com/protocol/services/game-card"
	"google.golang.org/grpc/codes"
)

func (s *Server) GetEsportGameCardConfig(ctx context.Context, request *esport_logic.GetEsportGameCardConfigRequest) (*esport_logic.GetEsportGameCardConfigResponse, error) {
	resp := &esport_logic.GetEsportGameCardConfigResponse{}
	gameDetailResp, err := s.eSportSkillService.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
		GameId: request.GetGameId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEsportGameCardConfig GetGameDetailById failed, err: %v", err)
		return resp, err
	}
	if len(gameDetailResp.GetConfig().GetGameCardInfoItemList()) == 0 {
		return resp, protocol.NewExactServerError(codes.OK, status.ErrEsportHallGameCardNotFound, "游戏名片已删除，无法填写")
	}

	for _, item := range gameDetailResp.GetConfig().GetGameCardInfoItemList() {
		resp.InfoItemList = append(resp.InfoItemList, &esport_logic.EsportGameCardInfoItem{
			Title:   item.GetItemName(),
			Options: item.GetItemList(),
			Tips:    item.GetTips(),
		})
	}

	resp.GameName = gameDetailResp.GetConfig().GetName()
	return resp, nil
}

func (s *Server) GetEsportGameCardInfo(ctx context.Context, request *esport_logic.GetEsportGameCardInfoRequest) (*esport_logic.GetEsportGameCardInfoResponse, error) {
	resp := &esport_logic.GetEsportGameCardInfoResponse{}

	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetEsportGameCardInfo failed, cardId: %d, err: no service info in context", request.GetCardId())
		return resp, nil
	}

	gameCardInfoResp, err := s.eSportHallService.GetEsportGameCardInfo(ctx, &esport_hall.GetEsportGameCardInfoRequest{
		Uid:    serviceInfo.UserID,
		CardId: request.GetCardId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEsportGameCardInfo GetEsportGameCardInfo failed, cardId: %d, err: %v", request.GetCardId(), err)
		return resp, err
	}

	gameDetailResp, err := s.eSportSkillService.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
		GameId: gameCardInfoResp.GetGameId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEsportGameCardInfo GetGameDetailById, gameId: %d, err: %v", gameCardInfoResp.GetGameId(), err)
		return resp, err
	}
	if len(gameDetailResp.GetConfig().GetGameCardInfoItemList()) == 0 {
		return resp, protocol.NewExactServerError(codes.OK, status.ErrEsportHallGameCardNotFound, "游戏名片已删除")
	}

	userInfoMap := make(map[string]*esport_hall.EsportGameCardInfoItem)
	for _, item := range gameCardInfoResp.GetInfoItemList() {
		userInfoMap[item.GetTitle()] = item
	}

	for _, item := range gameDetailResp.GetConfig().GetGameCardInfoItemList() {
		newInfoItem := &esport_logic.EsportGameCardInfoItem{
			Title:   item.GetItemName(),
			Content: userInfoMap[item.GetItemName()].GetContent(),
			Options: item.GetItemList(),
			Tips:    item.GetTips(),
		}
		optExist := false
		for _, optItem := range item.GetItemList() {
			if optItem == userInfoMap[item.GetItemName()].GetContent() {
				optExist = true
				break
			}
		}
		if len(item.GetItemList()) > 0 && !optExist {
			newInfoItem.Content = ""
		}

		resp.InfoItemList = append(resp.InfoItemList, newInfoItem)
	}

	resp.GameName = gameDetailResp.GetConfig().GetName()

	return resp, nil
}

func (s *Server) UpsertEsportGameCardInfo(ctx context.Context, request *esport_logic.UpsertEsportGameCardInfoRequest) (*esport_logic.UpsertEsportGameCardInfoResponse, error) {
	resp := &esport_logic.UpsertEsportGameCardInfoResponse{}
	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "UpsertEsportGameCardInfo failed, request: %+v, err: no service info in context", request)
		return resp, nil
	}

	if len(request.GetInfoItemList()) == 0 {
		return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "参数不合法")
	}

	userInfo, err := s.userProfileCli.GetUserProfileV2(ctx, serviceInfo.UserID, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpsertEsportGameCardInfo GetUserProfileV2, req: %+v, err: %v", request, err)
		return resp, err
	}

	if err := s.censoringText(ctx, userInfo, request.GetInfoItemList()[0].Content); err != nil {
		log.ErrorWithCtx(ctx, "UpsertEsportGameCardInfo, req: %+v, err: %v", request, err)
		return resp, err
	}

	// 查询游戏配置, 校验传入内容是否合法
	gameId := request.GetGameId()
	if request.GetCardId() != 0 {
		cardInfo, err := s.eSportHallService.GetEsportGameCardInfo(ctx, &esport_hall.GetEsportGameCardInfoRequest{
			Uid:    serviceInfo.UserID,
			CardId: request.GetCardId(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "UpsertEsportGameCardInfo GetEsportGameCardInfo, request: %+v, err: %v", request, err)
			return resp, err
		}
		gameId = cardInfo.GameId
	}

	gameDetailResp, tErr := s.eSportSkillService.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
		GameId: gameId,
	})
	if tErr != nil {
		log.ErrorWithCtx(ctx, "UpsertEsportGameCardInfo GetGameDetailById, request: %+v, err: %v", request, tErr)
		return resp, tErr
	}
	optionMap := make(map[string][]string)
	for _, item := range gameDetailResp.GetConfig().GetGameCardInfoItemList() {
		optionMap[item.ItemName] = item.ItemList
	}
	for _, item := range request.GetInfoItemList() {
		nowOption := optionMap[item.Title]
		if len(nowOption) == 0 && item.Content == "" {
			return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("%s内容不能为空", item.Title))
		}
		if len(nowOption) > 0 {
			noMatch := true
			for _, option := range nowOption {
				if item.Content == option {
					noMatch = false
					break
				}
			}
			if noMatch {
				return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("%s选项不匹配", item.Title))
			}
		}
	}

	upsertItemList := make([]*esport_hall.EsportGameCardInfoItem, 0, len(request.GetInfoItemList()))
	for _, item := range request.GetInfoItemList() {
		upsertItemList = append(upsertItemList, &esport_hall.EsportGameCardInfoItem{
			Title:   item.GetTitle(),
			Content: item.GetContent(),
		})
	}
	if request.GetCardId() != 0 {
		_, tErr = s.eSportHallService.UpdateEsportGameCard(ctx, &esport_hall.UpdateEsportGameCardRequest{
			Uid:          serviceInfo.UserID,
			GameId:       request.GetGameId(),
			CardId:       request.GetCardId(),
			InfoItemList: upsertItemList,
			MsgId:        request.GetMsgId(),
			TargetUid:    request.GetTargetUid(),
		})
		if tErr != nil {
			log.ErrorWithCtx(ctx, "UpsertEsportGameCardInfo.UpdateEsportGameCard, request: %+v, err: %v", request, tErr)
			return resp, tErr
		}
	} else if request.GetGameId() != 0 {
		_, tErr = s.eSportHallService.CreateEsportGameCard(ctx, &esport_hall.CreateEsportGameCardRequest{
			Uid:          serviceInfo.UserID,
			GameId:       request.GetGameId(),
			CardId:       request.GetCardId(),
			InfoItemList: upsertItemList,
			MsgId:        request.GetMsgId(),
			TargetUid:    request.GetTargetUid(),
		})
		if tErr != nil {
			log.ErrorWithCtx(ctx, "UpsertEsportGameCardInfo.CreateEsportGameCard, request: %+v, err: %v", request, tErr)
			return resp, tErr
		}
	}
	return resp, tErr
}

func (s *Server) censoringText(ctx context.Context, userInfo *pbApp.UserProfile, text string) error {
	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "CensoringText, err: get serviceInfo fail")
		return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}

	taskContext := &v2.TaskContext{
		SceneCode: string(auditTypes.SCENE_CODE_ESPORT_SKILL_TEXT),
		AppId:     string(auditTypes.APP_ID_QUICKSILVER),
		Scenes:    []v2.Scene{v2.Scene_SCENE_DEFAULT},
		UserInfo: &v2.User{
			Id:       uint64(userInfo.GetUid()),
			Alias:    userInfo.GetAccountAlias(),
			Nickname: userInfo.GetNickname(),
		},
		DeviceInfo: &v2.Device{
			Id: hex.EncodeToString(serviceInfo.DeviceID),
			Ip: serviceInfo.ClientIPAddr().String(),
		},
		BelongObjId: userInfo.GetAccountAlias(),
	}

	verifyRes, serr := s.censoringProxyCli.Text().SyncScanText(ctx, &v2.SyncTextCheckReq{
		Context: taskContext,
		Text:    text,
		Async:   true,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "CensoringText SyncScanText err uid:%d, err:%+v", userInfo.GetUid(), serr)
		return protocol.NewExactServerError(codes.OK, status.ErrEsportHallGameCardInfoInvalid, "内容审核失败")
	}
	log.InfoWithCtx(ctx, "CensoringText SyncScanText. content:%s uid:%d, verifyRes:%+v", text, userInfo.GetUid(), verifyRes)

	// 机审结果 REJECT 不保存记录，直接返回异常
	if v2.Suggestion_REJECT == v2.Suggestion(verifyRes.GetResult()) {
		return protocol.NewExactServerError(codes.OK, status.ErrEsportHallGameCardInfoInvalid, "内容审核不通过")
	}

	return nil
}

func (s *Server) GetEsportGameCardList(ctx context.Context, request *esport_logic.GetEsportGameCardListRequest) (*esport_logic.GetEsportGameCardListResponse, error) {
	resp := &esport_logic.GetEsportGameCardListResponse{}
	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetEsportGameCardList failed, request: %+v, err: no service info in context", request)
		return resp, nil
	}
	// 查询名片
	cardListResp, err := s.eSportHallService.GetEsportGameCardList(ctx, &esport_hall.GetEsportGameCardListRequest{
		Uid: serviceInfo.UserID,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEsportGameCardList GetEsportGameCardList failed, request: %+v, err: err", request, err)
		return resp, err
	}
	gameIds := make([]uint32, 0, len(cardListResp.GetCardList()))
	happendGame := make(map[uint32]bool)
	for _, item := range cardListResp.GetCardList() {
		if happendGame[item.GameId] {
			continue
		}

		happendGame[item.GameId] = true
		gameIds = append(gameIds, item.GameId)
	}

	if len(gameIds) == 0 {
		log.ErrorWithCtx(ctx, "GetEsportGameCardList, request: %+v, err: no record", request)
		return resp, nil
	}

	// 查询游戏配置
	gameDetailsResp, err := s.eSportSkillService.GetGameDetailByIds(ctx, &esport_skill.GetGameDetailByIdsRequest{
		GameIds: gameIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEsportGameCardList GetGameDetailByIds failed, gameIds: %+v, err: err", gameIds, err)
		return resp, err
	}
	gameDetailMap := make(map[uint32]*esport_skill.EsportGameConfig)
	for _, item := range gameDetailsResp.GetConfigList() {
		gameDetailMap[item.GameId] = item
	}

	// 查询用户线游戏卡片配置
	ugcGameCardConfList, err := s.gameCardCli.GetAllGameCardConfFromCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEsportGameCardList GetAllGameCardConfFromCache failed, err: err", gameIds, err)
		return resp, err
	}
	ugcGameCardConfMap := make(map[string]*game_card.GameCardConfInfo)
	for _, item := range ugcGameCardConfList {
		ugcGameCardConfMap[item.GetGameCardName()] = item
	}

	// 组装返回数据
	for _, item := range cardListResp.GetCardList() {
		if len(item.InfoItemList) <= 1 {
			continue
		}
		gameDetail, ok := gameDetailMap[item.GetGameId()]
		if !ok {
			continue
		}
		if len(gameDetail.GetGameCardInfoItemList()) <= 1 {
			continue
		}

		gameIcon := ""
		ugcGameCardConf, ok := ugcGameCardConfMap[gameDetail.GetName()]
		if ok {
			gameIcon = ugcGameCardConf.GetGameCornerMarkImgUrl()
		}

		backgroundImg := s.bc.GetGameCardImage(gameDetail.GetGameId())
		if backgroundImg == "" {
			backgroundImg = gameDetail.GetGameBackground()
		}
		topContent := item.InfoItemList[0].Content
		infoItemList := buildGameCardInfoList(gameDetail.GetGameCardInfoItemList()[1:], item.InfoItemList)
		resp.CardList = append(resp.CardList, &esport_logic.EsportGameCardInfo{
			GameIcon:        gameIcon,
			TopContent:      topContent,
			InfoItemList:    infoItemList,
			BackgroundImg:   backgroundImg,
			BackgroundColor: gameDetail.GetGameColor(),
			CardId:          item.CardId,
			GameId:          item.GameId,
			GameName:        gameDetail.GetName(),
		})
	}

	return resp, nil
}

func buildGameCardInfoList(configInfoList []*esport_skill.GameCardInfoItem, userInfoList []*esport_hall.EsportGameCardInfoItem) []*esport_logic.EsportGameCardInfoItem {
	infoItemList := make([]*esport_logic.EsportGameCardInfoItem, 0, len(userInfoList))
	if len(userInfoList) == 0 {
		return infoItemList
	}

	userInfoMap := make(map[string]*esport_hall.EsportGameCardInfoItem)
	for _, item := range userInfoList {
		userInfoMap[item.GetTitle()] = item
	}

	for _, item := range configInfoList {
		newInfoItem := &esport_logic.EsportGameCardInfoItem{
			Title:   item.GetItemName(),
			Content: userInfoMap[item.GetItemName()].GetContent(),
			Tips:    item.GetTips(),
		}
		optExist := false
		for _, opt := range item.GetItemList() {
			if opt == newInfoItem.Content {
				optExist = true
				break
			}
		}
		if !optExist {
			newInfoItem.Content = item.GetTips()
		}
		infoItemList = append(infoItemList, newInfoItem)
	}

	return infoItemList
}

func (s *Server) SendEsportGameCard(ctx context.Context, request *esport_logic.SendEsportGameCardRequest) (*esport_logic.SendEsportGameCardResponse, error) {
	resp := &esport_logic.SendEsportGameCardResponse{}
	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "SendEsportGameCard failed, request: %+v, err: no service info in context", request)
		return resp, nil
	}

	_, err := s.eSportHallService.SendEsportGameCard(ctx, &esport_hall.SendEsportGameCardRequest{
		Uid:       serviceInfo.UserID,
		CardId:    request.GetCardId(),
		TargetUid: request.GetTargetUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendEsportGameCard failed, request: %+v, err: %v", request, err)
	}

	return resp, err
}

func (s *Server) DeleteEsportGameCard(ctx context.Context, request *esport_logic.DeleteEsportGameCardRequest) (*esport_logic.DeleteEsportGameCardResponse, error) {
	resp := &esport_logic.DeleteEsportGameCardResponse{}
	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "DeleteEsportGameCard failed, request: %+v, err: no service info in context", request)
		return resp, nil
	}
	_, err := s.eSportHallService.DeleteEsportGameCard(ctx, &esport_hall.DeleteEsportGameCardRequest{
		Uid:    serviceInfo.UserID,
		CardId: request.GetCardId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteEsportGameCard  DeleteEsportGameCard failed, request: %+v, err: %v", request, err)
	}
	return resp, err
}
