package server

import (
    "context"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app/esport_logic"
    errCode "golang.52tt.com/protocol/common/status"
    esport_skill "golang.52tt.com/protocol/services/esport-skill"
    "golang.52tt.com/protocol/services/esport_hall"
    "golang.52tt.com/protocol/services/esport_rcmd"
    "golang.52tt.com/services/tt-rev/esport/common/collection/transform"
    "golang.52tt.com/services/tt-rev/esport/esport-logic/internal/mgr"
    "math/rand"
)

func (s *Server) GetUGCReListEnt(ctx context.Context, request *esport_logic.GetUGCReListEntRequest) (*esport_logic.GetUGCReListEntResponse, error) {
    out := &esport_logic.GetUGCReListEntResponse{}
    defer func() {
        log.DebugWithCtx(ctx, "GetUGCReListEnt req: %v, out: %v", request, out)
    }()

    // 获取当前的上下文参数
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetUGCReListEnt ServiceInfoFromContext err: get serviceInfo fail")
        return out, nil
    }

    // 查询开关状态
    eSportSwitchResp, err := s.eSportSkillService.GetSwitch(ctx, &esport_skill.GetSwitchRequest{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUGCReListEnt GetSwitch failed, err: %v, uid: %d", err, serviceInfo.UserID)
        return out, nil
    }
    if eSportSwitchResp.GetSwitchStatus().GetMainSwitchStatus() != esport_skill.EsportSwitchStatus_SWITCH_STATUS_ON {
        log.InfoWithCtx(ctx, "GetUGCReListEnt main switch is not open, uid: %d", serviceInfo.UserID)
        return out, nil
    }

    // 获取配置的人群包配置
    crowdGroupId := s.bc.GetUGCChannelSetting().CrowdGroupId
    if len(crowdGroupId) != 0 {
        // 如果有配置人群包，则需要先判断用户是否在人群包中
        group, err := s.mgr.IsUserMatchCrowGroup(ctx, serviceInfo.UserID, crowdGroupId)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUGCReListEnt IsUserMatchCrowGroup err: %v, uid: %d", err, serviceInfo.UserID)
            return out, nil
        }
        if !group {
            log.InfoWithCtx(ctx, "GetUGCReListEnt user not in crowd group, uid: %d", serviceInfo.UserID)
            return out, nil
        }
    }

    // 检查房间的游戏是否是能映射过来的游戏
    gameId, err := s.mgr.GetUGCRoomGameId(ctx, request.GetCid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUGCReListEnt GetUGCRoomGameId err: %v", err)
        return out, nil
    }
    log.InfoWithCtx(ctx, "GetUGCReListEnt gameId: %d, cid: %d", gameId, request.GetCid())
    if gameId != 0 {
        out.IsVisible = true
    }
    return out, nil
}

func (s *Server) GetUGCReCoachCardInfo(ctx context.Context, request *esport_logic.GetUGCReCoachCardInfoRequest) (*esport_logic.GetUGCReCoachCardInfoResponse, error) {
    out := &esport_logic.GetUGCReCoachCardInfoResponse{}
    defer func() {
        log.DebugWithCtx(ctx, "GetUGCReCoachCardInfo req: %v, out: %v", request, out)
    }()

    // 获取当前的上下文参数
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetUGCReCoachCardInfo ServiceInfoFromContext err: get serviceInfo fail")
        return out, nil
    }

    // 查询开关状态
    eSportSwitchResp, err := s.eSportSkillService.GetSwitch(ctx, &esport_skill.GetSwitchRequest{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUGCReCoachCardInfo GetSwitch failed, err: %v, uid: %d", err, serviceInfo.UserID)
        return out, nil
    }
    if eSportSwitchResp.GetSwitchStatus().GetMainSwitchStatus() != esport_skill.EsportSwitchStatus_SWITCH_STATUS_ON {
        log.InfoWithCtx(ctx, "GetUGCReCoachCardInfo main switch is not open, uid: %d", serviceInfo.UserID)
        return out, nil
    }

    // 判断人群包
    // 获取配置的人群包配置
    crowdGroupId := s.bc.GetUGCChannelSetting().CrowdGroupId
    if len(crowdGroupId) != 0 {
        // 如果有配置人群包，则需要先判断用户是否在人群包中
        group, err := s.mgr.IsUserMatchCrowGroup(ctx, serviceInfo.UserID, crowdGroupId)
        if err != nil {
            // 如果判断失败，则认为用户不在人群包中
            log.ErrorWithCtx(ctx, "GetUGCReCoachCardInfo IsUserMatchCrowGroup err: %v, uid: %d", err, serviceInfo.UserID)
            return out, nil
        }
        if !group {
            // 如果用户不在人群包中，则直接返回
            log.InfoWithCtx(ctx, "GetUGCReCoachCardInfo user not in crowd group, uid: %d", serviceInfo.UserID)
            return out, nil
        }
    }
    // 获取用户在房间中的推荐参数
    searchCondition, err := s.mgr.GetUserGameCardOnUGC(ctx, serviceInfo.UserID, request.GetCid())
    if err != nil {
        // 如果发生了错误，则记录日志，走默认兜底的处理
        log.ErrorWithCtx(ctx, "GetUGCReCoachCardInfo GetUserGameCardOnUGC err: %v", err)
    }
    // 如果没有映射到电竞的游戏，则不需要处理，直接返回空
    if searchCondition.GetGameId() == 0 {
        log.InfoWithCtx(ctx, "GetUGCReCoachCardInfo GetUserGameCardOnUGC game_id is 0, req: %+v, uid: %d", request, serviceInfo.UserID)
        return out, nil
    }

    newRcmdSwitchResp, err := s.esportRcmdCli.GetNewRcmdSwitch(ctx, &esport_rcmd.GetNewRcmdSwitchRequest{
        Uid: serviceInfo.UserID,
    })
    if err != nil {
        log.WarnWithCtx(ctx, "GetUGCReCoachCardInfo failed to GetNewRcmdSwitch,req:%+v, err: %v", request, err)
    }

    var reCoachList []*esport_logic.EsportAreaCoachInfo
    if newRcmdSwitchResp.GetNewRcmdSwitch() {
        log.InfoWithCtx(ctx, "新推荐接口 GetUGCReCoachCardInfo new rcmd switch is on, uid: %d", serviceInfo.UserID)
        reCoachList, _, err = s.mgr.GetRcmdSkillProduct(ctx, serviceInfo.UserID, &esport_rcmd.GetEsportRcmdSkillProductReq{
            SceneType:    esport_rcmd.SceneType_SCENE_TYPE_UGC_CHANNEL,
            SkillId:      searchCondition.GetGameId(),
            Limit:        s.bc.GetUGCChannelSetting().RecommendListLength,
            GameProperty: searchCondition.GetGameProperty(),
        })
        if err != nil {
            // 如果发生了错误，则认为没有推荐的大神
            log.ErrorWithCtx(ctx, "GetUGCReCoachCardInfo GetRcmdSkillProduct err: %v", err)
            return out, nil
        }
    } else {
        reCoachForUGC, err := s.eSportHallService.GetReCoachForUGC(ctx, &esport_hall.GetReCoachForUGCRequest{
            Uid:          serviceInfo.UserID,
            GameId:       searchCondition.GetGameId(),
            Cid:          request.GetCid(),
            PropertyList: searchCondition.GetGameProperty(),
            Limit:        s.bc.GetUGCChannelSetting().RecommendListLength,
        })
        if err != nil {
            // 如果发生了错误，则认为没有推荐的大神
            log.ErrorWithCtx(ctx, "GetUGCReCoachCardInfo GetReCoachForUGC err: %v", err)
            return out, nil
        }
        reCoachList = transform.Map(reCoachForUGC.CoachList, func(in *esport_hall.GameCoachInfo) *esport_logic.EsportAreaCoachInfo {
            res := mgr.HallCoachInfoToLogicCoachInfoFn(in)
            res.GameId = searchCondition.GetGameId()
            return res
        })
    }

    // 如果有推荐的教练，则返回
    if len(reCoachList) > 0 {
        list, err := s.mgr.AssembleRecommendList(ctx, reCoachList, searchCondition.GameId, serviceInfo.UserID)
        if err != nil {
            // 组装发生了错误，则记录日志，认为没有推荐的大神
            log.ErrorWithCtx(ctx, "GetUGCReCoachCardInfo AssembleRecommendList err: %v", err)
            return out, nil
        }
        // 从list中随机挑一个
        randomIndex := rand.Intn(len(list))
        out.Gods = list[randomIndex]
    }

    return out, nil
}

func (s *Server) NoMoreReOnUGC(ctx context.Context, request *esport_logic.NoMoreReOnUGCRequest) (*esport_logic.NoMoreReOnUGCResponse, error) {
    out := &esport_logic.NoMoreReOnUGCResponse{}
    defer func() {
        log.DebugWithCtx(ctx, "NoMoreReOnUGC req: %v, out: %v", request, out)
    }()

    // 参数校验
    if request.CoachId == 0 || request.Cid == 0 {
        log.ErrorWithCtx(ctx, "NoMoreReOnUGC 参数错误: CoachId 或 Cid 为空, req: %v", request)
        return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid, "参数错误: CoachId 或 Cid 为空")
    }

    // 获取当前的上下文参数
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "NoMoreReOnUGC ServiceInfoFromContext err: get serviceInfo fail")
        return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid, "无法获取上下文")
    }

    _, err := s.eSportHallService.AddIgnoreRecommendCoach(ctx, &esport_hall.AddIgnoreRecommendCoachRequest{
        Uid:       serviceInfo.UserID,
        CoachId:   request.GetCoachId(),
        Cid:       request.GetCid(),
        Operation: esport_hall.IgnoreReCoachOnUGCOperation_IGNORE_RE_COACH_ON_UGC_OPERATION_UNLIKE,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "NoMoreReOnUGC AddIgnoreRecommendCoach err: %v", err)
        return out, protocol.NewExactServerError(nil, errCode.ErrEsportsCommonErr, "AddIgnoreRecommendCoach fail")
    }
    return out, nil
}

func (s *Server) GetRecommendedGodList(ctx context.Context, request *esport_logic.GetRecommendedGodListRequest) (*esport_logic.GetRecommendedGodListResponse, error) {
    out := &esport_logic.GetRecommendedGodListResponse{}
    defer func() {
        log.DebugWithCtx(ctx, "GetRecommendedGodList req: %v, out: %v", request, out)
    }()

    // 获取当前的上下文参数
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetRecommendedGodList ServiceInfoFromContext err: get serviceInfo fail")
        return out, nil
    }

    // 查询开关状态
    eSportSwitchResp, err := s.eSportSkillService.GetSwitch(ctx, &esport_skill.GetSwitchRequest{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetRecommendedGodList GetSwitch failed, err: %v, uid: %d", err, serviceInfo.UserID)
        return out, nil
    }
    if eSportSwitchResp.GetSwitchStatus().GetMainSwitchStatus() != esport_skill.EsportSwitchStatus_SWITCH_STATUS_ON {
        log.InfoWithCtx(ctx, "GetRecommendedGodList main switch is not open, uid: %d", serviceInfo.UserID)
        return out, nil
    }

    // 判断人群包
    // 获取配置的人群包配置
    ugcChannelSetting := s.bc.GetUGCChannelSetting()
    crowdGroupId := ugcChannelSetting.CrowdGroupId
    if len(crowdGroupId) != 0 {
        // 如果有配置人群包，则需要先判断用户是否在人群包中
        group, err := s.mgr.IsUserMatchCrowGroup(ctx, serviceInfo.UserID, crowdGroupId)
        if err != nil {
            // 如果判断失败，则认为用户不在人群包中
            log.ErrorWithCtx(ctx, "GetRecommendedGodList IsUserMatchCrowGroup err: %v, uid: %d", err, serviceInfo.UserID)
            return out, nil
        }
        if !group {
            // 如果用户不在人群包中，则直接返回
            log.InfoWithCtx(ctx, "GetRecommendedGodList user not in crowd group, uid: %d", serviceInfo.UserID)
            return out, nil
        }
    }

    // 获取用户在房间中的推荐参数
    searchCondition, err := s.mgr.GetUserGameCardOnUGC(ctx, serviceInfo.UserID, request.GetCid())
    if err != nil {
        // 如果发生了错误，则记录日志，走默认兜底的处理
        log.ErrorWithCtx(ctx, "GetRecommendedGodList GetUserGameCardOnUGC err: %v", err)
    }
    // 如果没有映射到电竞的游戏，则不需要处理，直接返回空
    if searchCondition.GetGameId() == 0 {
        log.InfoWithCtx(ctx, "GetRecommendedGodList GetUserGameCardOnUGC game_id is 0, req: %+v, uid: %d", request, serviceInfo.UserID)
        return out, nil
    }

    switchResp, err := s.esportRcmdCli.GetNewRcmdSwitch(ctx, &esport_rcmd.GetNewRcmdSwitchRequest{
        Uid: serviceInfo.UserID,
    })
    if err != nil {
        log.WarnWithCtx(ctx, "GetRecommendedGodList failed to GetNewRcmdSwitch,req:%+v, err: %v", request, err)
    }

    var reCoachList []*esport_logic.EsportAreaCoachInfo
    if switchResp.GetNewRcmdSwitch() {
        log.InfoWithCtx(ctx, "新推荐接口 GetRecommendedGodList new rcmd switch is on, uid: %d", serviceInfo.UserID)
        reCoachList, _, err = s.mgr.GetRcmdSkillProduct(ctx, serviceInfo.UserID, &esport_rcmd.GetEsportRcmdSkillProductReq{
            SceneType:    esport_rcmd.SceneType_SCENE_TYPE_UGC_TOGETHER_ROOM_LIST,
            SkillId:      searchCondition.GetGameId(),
            Limit:        s.bc.GetUGCChannelSetting().RecommendListLength,
            GameProperty: searchCondition.GetGameProperty(),
        })
        if err != nil {
            // 如果发生了错误，则认为没有推荐的大神
            log.ErrorWithCtx(ctx, "GetRecommendedGodList GetRcmdSkillProduct err: %v", err)
            return out, nil
        }
    } else {
        reCoachForUGC, err := s.eSportHallService.GetReCoachForUGC(ctx, &esport_hall.GetReCoachForUGCRequest{
            Uid:          serviceInfo.UserID,
            GameId:       searchCondition.GetGameId(),
            Cid:          request.GetCid(),
            PropertyList: searchCondition.GetGameProperty(),
            Limit:        ugcChannelSetting.RecommendListLength,
        })
        if err != nil {
            // 如果发生了错误，则认为没有推荐的大神
            log.ErrorWithCtx(ctx, "GetRecommendedGodList GetReCoachForUGC err: %v", err)
            return out, nil
        }
        reCoachList = transform.Map(reCoachForUGC.CoachList, func(in *esport_hall.GameCoachInfo) *esport_logic.EsportAreaCoachInfo {
            res := mgr.HallCoachInfoToLogicCoachInfoFn(in)
            res.GameId = searchCondition.GetGameId()
            return res
        })
    }

    // 如果有推荐的教练，则返回
    if len(reCoachList) > 0 {
        list, err := s.mgr.AssembleRecommendList(ctx, reCoachList, searchCondition.GameId, serviceInfo.UserID)
        if err != nil {
            // 组装发生了错误，则记录日志，认为没有推荐的大神
            log.ErrorWithCtx(ctx, "GetRecommendedGodList AssembleRecommendList err: %v", err)
            return out, nil
        }
        out.Gods = list
    }

    return out, nil
}
