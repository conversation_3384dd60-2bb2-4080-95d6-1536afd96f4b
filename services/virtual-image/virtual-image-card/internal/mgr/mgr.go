package mgr

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/pkg/protocol"
    ga "golang.52tt.com/protocol/app"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/virtual-image-card"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/cache"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/config"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/event"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/local_cache"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/pay_api"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/rpc"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/store"
    "math"
    "sort"
    "time"
)

type Mgr struct {
    cache      *cache.Cache
    store      *store.Store
    rpcCli     *rpc.Client
    localCache *local_cache.LocalCache
    producer   *event.CardProducer
    timerD     *timer.Timer
}

func NewMgr(ctx context.Context, store_ *store.Store, cache_ *cache.Cache, rpcCli_ *rpc.Client,
    localCache_ *local_cache.LocalCache, producer_ *event.CardProducer) (*Mgr, error) {
    mgr := &Mgr{
        store:      store_,
        cache:      cache_,
        rpcCli:     rpcCli_,
        localCache: localCache_,
        producer:   producer_,
    }

    err := mgr.setupTimer()
    if err != nil {
        log.ErrorWithCtx(ctx, "setupTimer err: %v", err)
        return nil, err
    }

    return mgr, nil
}

func (m *Mgr) Close() {
    _ = m.cache.Close()
    _ = m.store.Close()
    m.localCache.Shutdown()
    m.producer.Close()
}

func (m *Mgr) GetUserCardInfo(ctx context.Context, uid uint32) (out *pb.UserCardInfo, err error) {
    out = &pb.UserCardInfo{}
    defer func() {
        log.DebugWithCtx(ctx, "GetUserCardInfo in: %d, out: %+v, err: %v", uid, out, err)
    }()

    // 优先查白名单，查得到就不管后面了
    whiteInfo := config.GetCardWhiteUser(uid)
    if whiteInfo != nil {
        out = m.fillUserCardInfoWhite2Pb(whiteInfo)
        return
    }

    // 查缓存
    userCache, err := m.cache.GetUserCard(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserCardInfo GetCache err: %v", err)
        return
    }
    if userCache != nil {
        out = m.fillUserCardInfoCache2Pb(userCache)
        return
    }

    // 查数据库
    userCache = &cache.UserCard{Uid: uid}
    remain, err := m.store.GetRemain(ctx, uid, nil)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserCardInfo GetRemain err: %v", err)
        return
    }
    if remain != nil {
        userCache.EffectTs = getTimestamp(remain.EffectTime)
        userCache.ExpireTs = getTimestamp(remain.ExpireTime)
        userCache.DiscountOrderId = remain.DiscountOrderId
        userCache.FirstBuyOrderId = remain.FirstBuyOrderId
        userCache.BuyEffectTs = getTimestamp(remain.BuyEffectTime)
        userCache.BuyExpireTs = getTimestamp(remain.BuyExpireTime)
        userCache.TrialEffectTs = getTimestamp(remain.TrialEffectTime)
        userCache.TrialExpireTs = getTimestamp(remain.TrialExpireTime)
        userCache.Contracts = make([]*cache.UserContract, 0)

        var contracts []*store.Contract
        contracts, err = m.store.GetUserContractList(ctx, uid, nil)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUserCardInfo GetUserContractList err: %v", err)
            return
        }
        for _, contract := range contracts {
            userCache.Contracts = append(userCache.Contracts, &cache.UserContract{
                ContractId: contract.ContractId,
                PayChannel: contract.PayChannel,
                PackageId:  contract.PackageId,
                NextPayTs:  contract.NextPayTime.Unix(),
                CreateTs:   contract.CreateTime.Unix(),
            })
        }
        sort.SliceStable(userCache.Contracts, func(i, j int) bool { // 苹果排前面，支付宝在后面
            return userCache.Contracts[i].PayChannel > userCache.Contracts[j].PayChannel
        })
    }

    out = m.fillUserCardInfoCache2Pb(userCache) // 返回结果
    _ = m.cache.SetUserCard(ctx, userCache)     // 设置缓存
    return
}

func (m *Mgr) fillUserCardInfoWhite2Pb(whiteInfo *config.CardWhiteUser) *pb.UserCardInfo {
    out := &pb.UserCardInfo{
        Uid:             whiteInfo.Uid,
        EffectTs:        whiteInfo.EffectTs,
        ExpireTs:        whiteInfo.ExpireTs,
        BuyEffectTs:     whiteInfo.BuyEffectTs,
        BuyExpireTs:     whiteInfo.BuyExpireTs,
        TrialEffectTs:   whiteInfo.TrialEffectTs,
        TrialExpireTs:   whiteInfo.TrialExpireTs,
    }
    if whiteInfo.BuyEffectTs != 0 && whiteInfo.BuyExpireTs != 0 {
        out.DiscountOrderId = "fake_order_id"
        out.FirstBuyOrderId = "fake_order_id"
    }

    if whiteInfo.HasContract { // 这个就随便造假了
        out.Contracts = []*pb.UserContractInfo{
            {
                Uid:        whiteInfo.Uid,
                ContractId: "fake_contract_id",
                PayChannel: pb.PayChannel_PAY_CHANNEL_ALIPAY,
                NextPayTs:  whiteInfo.ExpireTs,
                CreateTs:   whiteInfo.EffectTs,
                Package:    &pb.Package{Id: 1, Name: "假包月"},
            },
        }
    }

    return out
}

func (m *Mgr) fillUserCardInfoCache2Pb(userCache *cache.UserCard) *pb.UserCardInfo {
    out := &pb.UserCardInfo{
        Uid:             userCache.Uid,
        EffectTs:        userCache.EffectTs,
        ExpireTs:        userCache.ExpireTs,
        DiscountOrderId: userCache.DiscountOrderId,
        FirstBuyOrderId: userCache.FirstBuyOrderId,
        BuyEffectTs:     userCache.BuyEffectTs,
        BuyExpireTs:     userCache.BuyExpireTs,
        TrialEffectTs:   userCache.TrialEffectTs,
        TrialExpireTs:   userCache.TrialExpireTs,
        Contracts:       make([]*pb.UserContractInfo, 0),
    }

    for _, contract := range userCache.Contracts {
        packageConf := m.localCache.GetPackageConf(contract.PackageId)
        if packageConf == nil {
            continue
        }

        out.Contracts = append(out.Contracts, &pb.UserContractInfo{
            Uid:        userCache.Uid,
            ContractId: contract.ContractId,
            PayChannel: pb.PayChannel(contract.PayChannel),
            CreateTs:   contract.CreateTs,
            NextPayTs:  contract.NextPayTs,
            Package:    dbPack2pbPack(packageConf),
        })
    }

    return out
}

func (m *Mgr) GetUserRedemptionInfo(ctx context.Context, in *pb.GetUserRedemptionInfoReq) (out *pb.GetUserRedemptionInfoResp, err error) {
    out = &pb.GetUserRedemptionInfoResp{}
    defer func() {
        log.DebugWithCtx(ctx, "GetUserRedemptionInfo in: %+v, out: %+v, err: %v", in, out, err)
    }()

    // 查缓存
    userCache, err := m.cache.GetUserRedemption(ctx, in.Uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserRedemptionInfo GetCache err: %v", err)
        return
    }
    if userCache != nil {
        out.HasSingleBuyOrderWaitingRedemption = m.calcUserRedemptionResult(userCache)
        return
    }

    // 查数据库
    userCache = &cache.UserRedemption{Uid: in.Uid}
    orderList, err := m.store.GetUserOrderRedemptionList(ctx, in.Uid, time.Now(), nil)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserRedemptionInfo GetDb err: %v", err)
        return
    }
    for _, order := range orderList {
        userCache.Orders = append(userCache.Orders, cache.UserRedemptionOrder{
            OrderId:     order.OrderId,
            PackageType: order.PackageType,
            BeginTime:   order.BeginTime,
            EndTime:     order.EndTime,
        })
    }
    
    out.HasSingleBuyOrderWaitingRedemption = m.calcUserRedemptionResult(userCache)
    _ = m.cache.SetUserRedemption(ctx, userCache) // 设置缓存

    return
}

func (m *Mgr) calcUserRedemptionResult(userCache *cache.UserRedemption) bool {
    now := time.Now()
    for _, order := range userCache.Orders {
        if order.EndTime.Before(now) {
            continue
        }
        if order.PackageType == uint32(pb.PackageType_PACKAGE_TYPE_NORMAL) {
            return true
        }
    }
    return false
}

func (m *Mgr) GetPurchaseHistory(ctx context.Context, in *pb.GetPurchaseHistoryReq) (out *pb.GetPurchaseHistoryResp, err error) {
    // 没必要缓存redis，这是个低频接口，查从库就行
    out = &pb.GetPurchaseHistoryResp{}
    defer func() {
        log.DebugWithCtx(ctx, "GetPurchaseHistory in: %+v, out: %+v, err: %v", in, out, err)
    }()

    if in.GetPage() < 1 || in.GetPageSize() < 1 || in.GetPageSize() > 100 {
        err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "分页参数错误")
        return
    }

    orderList, err := m.store.GetUserYearOrders(ctx, in.Uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPurchaseHistory GetUserYearOrders err: %v", err)
        return
    }

    // 根据page、page_size截取orderList
    start := int(in.PageSize) * (int(in.Page) - 1)
    stop := int(in.PageSize) * int(in.Page)
    if start >= len(orderList) {
        return
    }
    if stop >= len(orderList) {
        stop = len(orderList)
    } else {
        out.HasMore = true
    }
    orderList = orderList[start:stop]

    // 填充数据返回
    out.PurchaseList = make([]*pb.PurchaseRecord, 0, len(orderList))
    for _, order := range orderList {
        desc := fmt.Sprintf("购买无限换装卡【%s】", order.Package.Name)
        if order.OrderRole == store.OrderRoleSystem {
            desc = fmt.Sprintf("自动续费无限换装卡【%s】", order.Package.Name)
        }
        out.PurchaseList = append(out.PurchaseList, &pb.PurchaseRecord{
            OrderId:     order.OrderId,
            Time:        order.PayTime.Format("2006-01-02 15:04:05"),
            Desc:        desc,
            Price:       pay_api.ConvertCent2Yuan(order.PayPriceCent),
            HasRefunded: order.Status == store.OrderStatusRevoke,
        })
    }

    return
}

func (m *Mgr) GetUserPackageList(ctx context.Context, uid, marketId, clientType uint32) (out *pb.GetUserPackageListResp, err error) {
    out = &pb.GetUserPackageListResp{}
    saleList := m.GetShelfSalePackageListWithCache(ctx)
    sort.Slice(saleList, func(i, j int) bool {
        return saleList[i].Weight > saleList[j].Weight
    })

    cardInfo, err := m.GetUserCardInfo(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserPackageList GetUserCardInfo err:%v", err)
        return out, err
    }

    var hasAuto bool
    if len(cardInfo.Contracts) > 0 {
        hasAuto = true
    }

    _, expLv, err := m.rpcCli.ExpCli.GetUserExp(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPackageList GetUserExp failed uid:%d err:%s", uid, err)
        return
    }

    consumeNumInfo, err := m.rpcCli.NumericCli.GetPersonalNumericV2(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPackageList GetPersonalNumeric failed uid:%d err:%s", uid, err)
        return
    }

    nobilityInfo, err := m.rpcCli.NobilityCli.GetNobilityInfo(ctx, uid, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPackageList GetNobilityInfo failed uid:%d err:%s", uid, err)
        return
    }

    groupSet, err := m.getUserAllGroup(ctx, uid, saleList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPackageList getUserAllGroup failed uid:%d err:%s", uid, err)
        return
    }

    statusList := m.GetUserCardStatus(cardInfo)

    for _, sale := range saleList {
        // 非上架中不返回
        if sale.SaleStatus != pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_SHELF {
            continue
        }
        packageInfo := sale.GetPackageInfo()

        // 马甲包不同跳过
        if packageInfo.GetMarketId() != marketId {
            continue
        }
        // 套餐客户端不匹配跳过
        if clientType == uint32(ga.TT_CLIENT_TYPE_TT_CLIENT_IOS) {
            if packageInfo.GetProductId() == "" {
                continue
            }
        } else if clientType == uint32(ga.TT_CLIENT_TYPE_TT_CLIENT_TYPE_ANDROID) || clientType == uint32(ga.TT_CLIENT_TYPE_TT_CLIENT_TYPE_PC_TT) {
            if packageInfo.GetProductId() != "" {
                continue
            }
        } else {
            log.ErrorWithCtx(ctx, "GetUserPackageList unknown client_type:%d", clientType)
            return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "unknown client_type")
        }
        var isAutoRenew bool
        if sale.GetPackageInfo().GetPackageType() == pb.PackageType_PACKAGE_TYPE_AUTO_RENEW {
            isAutoRenew = true
        }
        // 如果是自动续费套餐，并且处于签约中，则跳过
        if hasAuto && isAutoRenew {
            continue
        }

        opt := &PackFilterOpt{
            Uid:        uid,
            StatusList: statusList,
            ExpLevel:   expLv,
            ConsumeNum: uint32(consumeNumInfo.GetRichLevel().GetServerLevel()),
            NobLevel:   nobilityInfo.GetLevel(),
            GroupSet:   groupSet,
        }
        if filterErr := m.PackFilter(ctx, sale, opt); filterErr != nil {
            log.DebugWithCtx(ctx, "GetPackageList packFilter fail uid:%v pkgInfo:%+v err:%v", uid, sale, filterErr)
            continue
        }

        // 区分首购价
        var payPriceCent = packageInfo.GetPriceCent()
        var discountLabel string
        // 区分首购角标
        var desc = sale.GetLabel()
        // 配置了首购，并且没有购买过
        if cardInfo.DiscountOrderId == "" && packageInfo.GetDiscountPriceCent() != 0 {
            payPriceCent = packageInfo.GetDiscountPriceCent()
            discountLabel = packageInfo.GetDiscountLabel()
            desc = packageInfo.GetDiscountDesc()
        }

        out.PackageList = append(out.PackageList, &pb.UserPackage{
            PackageId:         packageInfo.GetId(),
            Name:              packageInfo.GetName(),
            Desc:              desc,
            CurrentPriceCent:  payPriceCent,
            OriginalPriceCent: packageInfo.GetOriginalPriceCent(),
            DiscountLabel:     discountLabel,
            DailyPriceCent:    uint32(math.Ceil(float64(payPriceCent)/10/float64(packageInfo.GetDays())) * 10),
            PayPriceCent:      payPriceCent,
            Explanation:       packageInfo.GetDesc(),
            IsAuto:            isAutoRenew,
            PayChannelList:    getPayChannelList(clientType, isAutoRenew),
            ProductId:         packageInfo.GetProductId(),
            ShowCondition:     sale.GetCondition(),
        })
    }

    return out, nil
}

func getPayChannelList(clientType uint32, isRenew bool) []pb.PayChannel {
    if clientType == uint32(ga.TT_CLIENT_TYPE_TT_CLIENT_IOS) {
        return []pb.PayChannel{pb.PayChannel_PAY_CHANNEL_APPSTORE}
    } else if clientType == uint32(ga.TT_CLIENT_TYPE_TT_CLIENT_TYPE_ANDROID) {
        if isRenew {
            return []pb.PayChannel{pb.PayChannel_PAY_CHANNEL_ALIPAY}
        } else {
            return []pb.PayChannel{pb.PayChannel_PAY_CHANNEL_WECHAT, pb.PayChannel_PAY_CHANNEL_ALIPAY}
        }
    } else {
        return []pb.PayChannel{}
    }
}

type PackFilterOpt struct {
    Uid        uint32
    StatusList []pb.CardStatus // 开卡状态
    ExpLevel   uint32
    ConsumeNum uint32
    NobLevel   uint32
    GroupSet   map[string]bool
}

func (m *Mgr) PackFilter(ctx context.Context, info *pb.SalePackage, opt *PackFilterOpt) error {
    if info.GetCondition().GetCondType() == uint32(pb.DisplayCondition_ENUM_USER_ALL) {
        return nil
    }

    isStatusOk := false
    if len(info.GetCondition().GetCardStatus()) == 0 {
        isStatusOk = true
    } else {
        for _, pkgStatus := range info.GetCondition().GetCardStatus() {
            for _, userStatus := range opt.StatusList {
                if pkgStatus == userStatus {
                    isStatusOk = true
                    break // 找到一个就行
                }
            }
        }
    }

    log.DebugWithCtx(ctx, "PackFilter info: %+v, opt: %+v, isStatusOk: %v", info, opt, isStatusOk)
    if isStatusOk && opt.ExpLevel >= info.GetCondition().GetUserLv() &&
        opt.ConsumeNum >= info.GetCondition().GetRichLv() &&
        opt.NobLevel >= info.GetCondition().GetNobilityLv() &&
        checkUserGroup(info.GetCondition().GetPeopleGroupList(), opt.GroupSet) {
        return nil
    }

    return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "条件不符合")
}

// 检查是否处于人群包内
func (m *Mgr) CheckUserGroup(ctx context.Context, uid uint32, groupIdList []string) bool {
    if len(groupIdList) == 0 {
        return true
    }
    groupList, err := m.rpcCli.UserGroupCli.GetUserHasGroup(ctx, uid, groupIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserHasGroup err: %v", err)
        return false
    }
    if len(groupList) == 0 {
        log.DebugWithCtx(ctx, "checkUserGroup failed, uid: %d, groupIdList: %v, groupList: %v", uid, groupIdList, groupList)
        return false
    } else {
        return true
    }
}

func (m *Mgr) getUserAllGroup(ctx context.Context, uid uint32, saleList []*pb.SalePackage) (map[string]bool, error) {
    groupSet := make(map[string]bool)

    var idList []string
    idSet := make(map[string]bool)
    for _, sp := range saleList {
        if sp.GetCondition().GetCondType() == uint32(pb.DisplayCondition_ENUM_USER_ALL) {
            continue
        }
        for _, group := range sp.GetCondition().GetPeopleGroupList() {
            if !idSet[group] {
                idSet[group] = true
                idList = append(idList, group)
            }
        }
    }
    if len(idList) == 0 {
        return groupSet, nil
    }

    groupList, err := m.rpcCli.UserGroupCli.GetUserHasGroup(ctx, uid, idList)
    if err != nil {
        log.ErrorWithCtx(ctx, "getUserAllGroup err: %v", err)
        return groupSet, nil // 只要有1个人群包id配错了，整个接口就会报错，影响接口继续往下走。因此还是不报错了，只是打印日志
    }
    for _, group := range groupList {
        groupSet[group] = true
    }
    return groupSet, nil
}

func checkUserGroup(groupList []string, groupSet map[string]bool) bool {
    if len(groupList) == 0 {
        return true
    }
    for _, group := range groupList {
        if groupSet[group] {
            return true
        }
    }
    return false
}

func (m *Mgr) GetUserCardStatus(cardInfo *pb.UserCardInfo) []pb.CardStatus {
    statusList := make([]pb.CardStatus, 0)
    nowTs := time.Now().Unix()

    if cardInfo.BuyExpireTs == 0 {
        statusList = append(statusList, pb.CardStatus_ENUM_STATUS_NO_OPEN)
    } else {
        if cardInfo.BuyExpireTs < nowTs {
            statusList = append(statusList, pb.CardStatus_ENUM_STATUS_EXPIRED)
        } else {
            if len(cardInfo.GetContracts()) > 0 {
                statusList = append(statusList, pb.CardStatus_ENUM_STATUS_SIGN)
            } else {
                if cardInfo.BuyExpireTs < nowTs+int64(config.GetDynamicConfig().ExpireAlertStatusKeepHour*3600) {
                    statusList = append(statusList, pb.CardStatus_ENUM_STATUS_SOON_EXPIRE)
                } else {
                    statusList = append(statusList, pb.CardStatus_ENUM_STATUS_OPENING)
                }
            }
        }
    }

    if cardInfo.TrialEffectTs <= nowTs && cardInfo.TrialExpireTs > nowTs {
        statusList = append(statusList, pb.CardStatus_ENUM_STATUS_TRIAL)
    }

    return statusList
}
